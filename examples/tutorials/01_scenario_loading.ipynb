{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Scenario loading and structure \n", "\n", "`GPUDrive` is a multi-agent driving simulator built on top of the [Waymo Open Motion Dataset (WOMD)](https://waymo.com/open/) (See also [<PERSON><PERSON><PERSON> et al., 2021](https://arxiv.org/abs/2104.10133)). \n", "\n", "In this tutorial, we explain the structure of a traffic scenario and show use processed scenario data with `GPUDrive`.\n", "\n", "**Useful links to learn more**:\n", "- [`waymo-open-dataset`](https://github.com/waymo-research/waymo-open-dataset): Official dataset repo\n", "- [tf.Example proto format](https://waymo.com/open/data/motion/tfexample): Data dictionary for a raw WOMD scenario\n", "- [GPUDrive `data_utils`](https://github.com/Emerge-Lab/gpudrive/tree/main/data_utils): Docs and code we use to process the WOMD scenarios"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# Dependencies\n", "import json\n", "import os\n", "from pathlib import Path\n", "import seaborn as sns\n", "import pandas as pd\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "\n", "# Set working directory to the base directory 'gpudrive'\n", "working_dir = Path.cwd()\n", "while working_dir.name != 'gpudrive':\n", "    working_dir = working_dir.parent\n", "    if working_dir == Path.home():\n", "        raise FileNotFoundError(\"Base directory 'gpudrive' not found\")\n", "os.ch<PERSON>(working_dir)\n", "\n", "cmap = [\"r\", \"g\", \"b\", \"y\", \"c\"]\n", "%config InlineBackend.figure_format = 'svg'\n", "sns.set(\"notebook\", font_scale=1.1, rc={\"figure.figsize\": (8, 3)})\n", "sns.set_style(\"ticks\", rc={\"figure.facecolor\": \"none\", \"axes.facecolor\": \"none\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterating through the WOMD dataset\n", "\n", "We upload a folder containing three scenarios in the `data/processed/examples` directory that you can work with. The full dataset can be downloaded [here](https://github.com/Emerge-Lab/gpudrive/tree/main?tab=readme-ov-file#dataset). \n", "\n", "\n", "Notice that the data folder is structured as follows:\n", "\n", "```bash\n", "data/\n", "    - tfrecord-xxxxx-of-xxxxx\n", "    - ....\n", "    - tfrecord-xxxxx-of-xxxxx\n", "```\n", "\n", "Every file beginning with `tfrecord` is a unique traffic scenario.\n", "\n", "To use the dataset with the simulator, we use the conventions from [PyTorch dataloaders](https://pytorch.org/tutorials/beginner/basics/data_tutorial.html). \n", "\n", "\n", "Here is example of how to set up a dataloader:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from gpudrive.env.dataset import SceneDataLoader\n", "\n", "data_loader = SceneDataLoader(\n", "    root=\"data/processed/examples\", # Path to the dataset\n", "    batch_size=10, # Batch size, you want this to be equal to the number of worlds (envs) so that every world receives a different scene\n", "    dataset_size=4, # Total number of different scenes we want to use\n", "    sample_with_replacement=True, \n", "    seed=42, \n", "    shuffle=True,   \n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["['data/processed/examples/tfrecord-00002-of-01000_407.json',\n", " 'data/processed/examples/tfrecord-00002-of-01000_407.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_402.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_325.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_4.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_402.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_4.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_325.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_325.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_4.json']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# The full dataset that we will be using\n", "data_loader.dataset"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'data/processed/examples/tfrecord-00000-of-01000_325.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_4.json',\n", " 'data/processed/examples/tfrecord-00000-of-01000_402.json',\n", " 'data/processed/examples/tfrecord-00002-of-01000_407.json'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Notice that it only has 4 unique scenes, since we set the dataset_size to 4\n", "set(data_loader.dataset)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["'data/processed/examples/tfrecord-00002-of-01000_407.json'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data_files = next(iter(data_loader))\n", "\n", "data_files[0]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('/home/<USER>/work/research/sim_agents/gpudrive/build')\n", "from gpudrive.env.env_torch import GPUDriveTorchEnv\n", "from gpudrive.env.config import EnvConfig"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Pass the data_loader to the environment \n", "env = GPUDriveTorchEnv(\n", "    config=EnvConfig(),\n", "    data_loader=data_loader,\n", "    max_cont_agents=64,  \n", "    device=\"cpu\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Deep dive: What is inside a traffic scenario? 🤔🔬"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Though every scenario in the WOMD is unique, they all share the same basic data structure. Traffic scenarios are essentially dictionaries, which you can inspect using tools like [JSON Formatter](https://jsonformatter.org/json-viewer). We'll also look at one in this notebook. In a nutshell, traffic scenarios contain a few key elements:\n", "\n", "- **Road map**: The layout and structure of the roads.\n", "- **Human driving (expert) demonstrations**: Examples of human driving behavior.\n", "- **Road objects**: Elements such as stop signs and other traffic signals."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['name', 'scenario_id', 'objects', 'roads', 'tl_states', 'metadata'])"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Take an example scene\n", "data_path = \"data/processed/examples/tfrecord-00000-of-01000_325.json\"\n", "\n", "with open(data_path) as file:\n", "    traffic_scene = json.load(file)\n", "\n", "traffic_scene.keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "We will show you how to render a scene in ⏭️ tutorial `03`, which introduces the gym environment wrapper. Let's first take a closer look at the data structure."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Global Overview\n", "\n", "A traffic scene includes the following key elements:\n", "\n", "- **`name`**: The name of the traffic scenario.  \n", "- **`scenario_id`**: Unique identifier for every scenario.\n", "- **`objects`**: Dynamic entities such as vehicles or other moving elements in the scene.  \n", "- **`roads`**: Stationary elements, including road points and fixed objects.  \n", "- **`tl_states`**: Traffic light states (currently not included in processing).  \n", "- **`metadata`**: Additional details about the traffic scenario, such as the index of the self-driving car (SDC) and details for the WOSAC Challenge."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["traffic_scene[\"tl_states\"]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["'tfrecord-00000-of-01000_325.json'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["traffic_scene[\"name\"]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'sdc_track_index': 61,\n", " 'objects_of_interest': [],\n", " 'tracks_to_predict': [{'track_index': 3, 'difficulty': 0},\n", "  {'track_index': 32, 'difficulty': 0},\n", "  {'track_index': 1, 'difficulty': 0}]}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["traffic_scene[\"metadata\"]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ef3a8f65142f41ac'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["traffic_scene[\"scenario_id\"]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"535.707672pt\" height=\"277.069521pt\" viewBox=\"0 0 535.**********.069521\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-15T01:19:04.541432</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.0, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 277.069521 \n", "L 535.**********.069521 \n", "L 535.707672 0 \n", "L 0 0 \n", "L 0 277.069521 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 82.**********.549937 \n", "L 528.**********.549937 \n", "L 528.507672 23.229938 \n", "L 82.107672 23.229938 \n", "L 82.**********.549937 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"md739f4ceec\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md739f4ceec\" x=\"193.707672\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- vehicle -->\n", "      <g style=\"fill: #262626\" transform=\"translate(180.831572 236.025192) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-76\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"59.179688\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"120.703125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"184.082031\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"211.865234\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"266.845703\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"294.628906\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#md739f4ceec\" x=\"416.907672\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- pedestrian -->\n", "      <g style=\"fill: #262626\" transform=\"translate(396.38666 251.315017) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-70\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"63.476562\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"125\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"188.476562\"/>\n", "       <use xlink:href=\"#DejaVuSans-73\" x=\"250\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.099609\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"341.308594\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"382.421875\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"410.205078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"471.484375\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- Time step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(272.785141 267.124334) scale(0.132 -0.132)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"57.958984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"85.742188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"183.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"244.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"276.464844\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"328.564453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"367.773438\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"429.296875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m37839863cb\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m37839863cb\" x=\"82.107672\" y=\"181.989938\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- −10000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 186.586992) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"338.28125\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m37839863cb\" x=\"82.107672\" y=\"151.933283\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- −8000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 156.530338) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m37839863cb\" x=\"82.107672\" y=\"121.876629\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- −6000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 126.473683) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m37839863cb\" x=\"82.107672\" y=\"91.819974\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −4000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 96.417029) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m37839863cb\" x=\"82.107672\" y=\"61.76332\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −2000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 66.360375) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m37839863cb\" x=\"82.107672\" y=\"31.706665\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(64.909047 36.30372) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- IS VALID -->\n", "     <g style=\"fill: #262626\" transform=\"translate(17.229938 133.939781) rotate(-90) scale(0.132 -0.132)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-56\" d=\"M 1831 0 \n", "L 50 4666 \n", "L 709 4666 \n", "L 2188 738 \n", "L 3669 4666 \n", "L 4325 4666 \n", "L 2547 0 \n", "L 1831 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-41\" d=\"M 2188 4044 \n", "L 1331 1722 \n", "L 3047 1722 \n", "L 2188 4044 \n", "z\n", "M 1831 4666 \n", "L 2547 4666 \n", "L 4325 0 \n", "L 3669 0 \n", "L 3244 1197 \n", "L 1141 1197 \n", "L 716 0 \n", "L 50 0 \n", "L 1831 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4c\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 531 \n", "L 3531 531 \n", "L 3531 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-44\" d=\"M 1259 4147 \n", "L 1259 519 \n", "L 2022 519 \n", "Q 2988 519 3436 956 \n", "Q 3884 1394 3884 2338 \n", "Q 3884 3275 3436 3711 \n", "Q 2988 4147 2022 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 1925 4666 \n", "Q 3281 4666 3915 4102 \n", "Q 4550 3538 4550 2338 \n", "Q 4550 1131 3912 565 \n", "Q 3275 0 1925 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-49\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"29.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"92.96875\"/>\n", "      <use xlink:href=\"#DejaVuSans-56\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-41\" x=\"186.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"255.197266\"/>\n", "      <use xlink:href=\"#DejaVuSans-49\" x=\"310.910156\"/>\n", "      <use xlink:href=\"#DejaVuSans-44\" x=\"340.402344\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 137.907672 31.706665 \n", "L 249.507672 31.706665 \n", "L 249.507672 30.895136 \n", "L 137.907672 30.895136 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #c44e52; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 361.107672 31.706665 \n", "L 472.707672 31.706665 \n", "L 472.707672 31.586439 \n", "L 361.107672 31.586439 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #55a868; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 137.907672 31.706665 \n", "L 249.507672 31.706665 \n", "L 249.507672 30.789938 \n", "L 137.907672 30.789938 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #c44e52; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 361.107672 31.706665 \n", "L 472.707672 31.706665 \n", "L 472.707672 30.910164 \n", "L 361.107672 30.910164 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #55a868; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 584.307672 31.706665 \n", "L 695.907672 31.706665 \n", "L 695.907672 31.451184 \n", "L 584.307672 31.451184 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #4c72b0; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 807.507672 31.706665 \n", "L 919.107672 31.706665 \n", "L 919.107672 31.466212 \n", "L 807.507672 31.466212 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #ccb974; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 1030.707672 31.706665 \n", "L 1142.307672 31.706665 \n", "L 1142.307672 31.616495 \n", "L 1030.707672 31.616495 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #64b5cd; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 1253.907672 31.706665 \n", "L 1365.507672 31.706665 \n", "L 1365.507672 31.646552 \n", "L 1253.907672 31.646552 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #c44e52; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 137.907672 31.706665 \n", "L 249.507672 31.706665 \n", "L 249.507672 30.895136 \n", "L 137.907672 30.895136 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #c44e52; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 361.107672 31.706665 \n", "L 472.707672 31.706665 \n", "L 472.707672 31.586439 \n", "L 361.107672 31.586439 \n", "z\n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: #55a868; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"line2d_9\">\n", "    <path d=\"M 193.707672 31.712906 \n", "L 416.907672 31.712993 \n", "L 536.707672 31.71303 \n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: none; stroke: #4c72b0; stroke-width: 1.5; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"line2d_10\">\n", "    <path d=\"M 193.707672 31.712906 \n", "L 416.907672 31.712993 \n", "L 536.707672 31.71303 \n", "\" clip-path=\"url(#p012baebd4c)\" style=\"fill: none; stroke: #dd8452; stroke-width: 1.5; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"line2d_11\">\n", "    <defs>\n", "     <path id=\"mf187996c60\" d=\"M 3 0 \n", "L -3 -0 \n", "\" style=\"stroke: #55a868\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p012baebd4c)\">\n", "     <use xlink:href=\"#mf187996c60\" x=\"193.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"416.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"640.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"863.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"1086.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"1309.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"1532.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"1756.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"1979.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"2202.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"2425.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"2648.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"2872.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"3095.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"3318.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"3541.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"3764.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"3988.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"4211.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"4434.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"4657.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"4880.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"5104.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"5327.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"5550.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"5773.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"5996.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"6220.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"6443.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"6666.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"6889.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"7112.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"7336.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"7559.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"7782.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"8005.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"8228.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"8452.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"8675.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"8898.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"9121.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"9344.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"9568.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"9791.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"10014.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"10237.707672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"10460.907672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"10684.107672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"10907.307672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"11130.507672\" y=\"31.691637\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"11353.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"11576.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"11800.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"12023.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"12246.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"12469.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"12692.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"12916.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"13139.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"13362.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"13585.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"13808.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"14032.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"14255.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"14478.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"14701.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"14924.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"15148.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"15371.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"15594.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"15817.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"16040.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"16264.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"16487.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"16710.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"16933.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"17156.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"17380.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"17603.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"17826.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"18049.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"18272.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"18496.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"18719.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"18942.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"19165.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"19388.907672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"19612.107672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"19835.307672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"20058.507672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "     <use xlink:href=\"#mf187996c60\" x=\"20281.707672\" y=\"31.706665\" style=\"fill: #55a868; stroke: #55a868\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 82.**********.549938 \n", "L 82.107672 23.229938 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 528.**********.549938 \n", "L 528.507672 23.229938 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 82.**********.549937 \n", "L 528.**********.549937 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 82.107672 23.229938 \n", "L 528.507672 23.229938 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- Distribution of road objects in traffic scene. Total # objects: 62 -->\n", "    <g style=\"fill: #262626\" transform=\"translate(98.568859 17.229938) scale(0.132 -0.132)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6a\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 -63 \n", "Q 1178 -731 923 -1031 \n", "Q 669 -1331 103 -1331 \n", "L -116 -1331 \n", "L -116 -844 \n", "L 38 -844 \n", "Q 366 -844 484 -692 \n", "Q 603 -541 603 -63 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-23\" d=\"M 3272 2816 \n", "L 2363 2816 \n", "L 2100 1772 \n", "L 3016 1772 \n", "L 3272 2816 \n", "z\n", "M 2803 4594 \n", "L 2478 3297 \n", "L 3391 3297 \n", "L 3719 4594 \n", "L 4219 4594 \n", "L 3897 3297 \n", "L 4872 3297 \n", "L 4872 2816 \n", "L 3775 2816 \n", "L 3519 1772 \n", "L 4513 1772 \n", "L 4513 1294 \n", "L 3397 1294 \n", "L 3072 0 \n", "L 2572 0 \n", "L 2894 1294 \n", "L 1978 1294 \n", "L 1656 0 \n", "L 1153 0 \n", "L 1478 1294 \n", "L 494 1294 \n", "L 494 1772 \n", "L 1594 1772 \n", "L 1856 2816 \n", "L 850 2816 \n", "L 850 3297 \n", "L 1978 3297 \n", "L 2297 4594 \n", "L 2803 4594 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3a\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 0 \n", "L 750 0 \n", "L 750 794 \n", "z\n", "M 750 3309 \n", "L 1409 3309 \n", "L 1409 2516 \n", "L 750 2516 \n", "L 750 3309 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-44\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"77.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"104.785156\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"156.884766\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"196.09375\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"237.207031\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"264.990234\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"328.466797\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"391.845703\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"431.054688\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"458.837891\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"520.019531\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"583.398438\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"615.185547\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"676.367188\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"711.572266\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"743.359375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"782.222656\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"843.404297\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"904.683594\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"968.160156\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"999.947266\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"1061.128906\"/>\n", "     <use xlink:href=\"#DejaVuSans-6a\" x=\"1124.605469\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"1152.388672\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"1213.912109\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1268.892578\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"1308.101562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1360.201172\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1391.988281\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1419.771484\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1483.150391\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1514.9375\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"1554.146484\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"1595.259766\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"1656.539062\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"1691.744141\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1726.949219\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"1754.732422\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1809.712891\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"1841.5\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"1893.599609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"1948.580078\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"2010.103516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"2073.482422\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"2135.005859\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2166.792969\"/>\n", "     <use xlink:href=\"#DejaVuSans-54\" x=\"2198.580078\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"2242.664062\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"2303.845703\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"2343.054688\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"2404.333984\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2432.117188\"/>\n", "     <use xlink:href=\"#DejaVuSans-23\" x=\"2463.904297\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2547.693359\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"2579.480469\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"2640.662109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6a\" x=\"2704.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"2731.921875\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"2793.445312\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"2848.425781\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"2887.634766\"/>\n", "     <use xlink:href=\"#DejaVuSans-3a\" x=\"2939.734375\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2973.425781\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" x=\"3005.212891\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"3068.835938\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p012baebd4c\">\n", "   <rect x=\"82.107672\" y=\"23.229938\" width=\"446.4\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x300 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["pd.Series(\n", "    [\n", "        traffic_scene[\"objects\"][idx][\"type\"]\n", "        for idx in range(len(traffic_scene[\"objects\"]))\n", "    ]\n", ").value_counts().plot(kind=\"bar\", rot=45, color=cmap)\n", "plt.title(\n", "    f'Distribution of road objects in traffic scene. Total # objects: {len(traffic_scene[\"objects\"])}'\n", ")\n", "plt.show()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This traffic scenario only contains vehicles and pedestrians, some scenes have cyclists as well."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"485.69725pt\" height=\"259.674087pt\" viewBox=\"0 0 485.69725 259.674087\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-15T01:19:16.693210</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.0, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 259.674087 \n", "L 485.69725 259.674087 \n", "L 485.69725 0 \n", "L 0 0 \n", "L 0 259.674087 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 32.09725 189.549937 \n", "L 478.49725 189.549937 \n", "L 478.49725 23.229937 \n", "L 32.09725 23.229937 \n", "L 32.09725 189.549937 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m4ac965353a\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4ac965353a\" x=\"69.29725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- lane -->\n", "      <g style=\"fill: #262626\" transform=\"translate(62.504594 223.858304) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-6c\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"89.0625\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"152.441406\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m4ac965353a\" x=\"143.69725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- driveway -->\n", "      <g style=\"fill: #262626\" transform=\"translate(126.578582 244.51033) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-64\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"63.476562\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"104.589844\"/>\n", "       <use xlink:href=\"#DejaVuSans-76\" x=\"132.373047\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"191.552734\"/>\n", "       <use xlink:href=\"#DejaVuSans-77\" x=\"253.076172\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"334.863281\"/>\n", "       <use xlink:href=\"#DejaVuSans-79\" x=\"396.142578\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m4ac965353a\" x=\"218.09725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- road_edge -->\n", "      <g style=\"fill: #262626\" transform=\"translate(197.886393 250.456744) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-72\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"38.863281\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"100.044922\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"161.324219\"/>\n", "       <use xlink:href=\"#DejaVuSans-5f\" x=\"224.800781\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"274.800781\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"336.324219\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"399.800781\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"463.277344\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m4ac965353a\" x=\"292.49725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- road_line -->\n", "      <g style=\"fill: #262626\" transform=\"translate(275.262274 244.504982) rotate(-45) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-72\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"38.863281\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"100.044922\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"161.324219\"/>\n", "       <use xlink:href=\"#DejaVuSans-5f\" x=\"224.800781\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"274.800781\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"302.583984\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"330.367188\"/>\n", "       <use xlink:href=\"#DejaVuSans-65\" x=\"393.746094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m4ac965353a\" x=\"366.89725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- stop_sign -->\n", "      <g style=\"fill: #262626\" transform=\"translate(348.916966 245.995596) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-73\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"52.099609\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"91.308594\"/>\n", "       <use xlink:href=\"#DejaVuSans-70\" x=\"152.490234\"/>\n", "       <use xlink:href=\"#DejaVuSans-5f\" x=\"215.966797\"/>\n", "       <use xlink:href=\"#DejaVuSans-73\" x=\"265.966797\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"318.066406\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"345.849609\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"409.326172\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m4ac965353a\" x=\"441.29725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- crosswalk -->\n", "      <g style=\"fill: #262626\" transform=\"translate(422.782885 247.301722) rotate(-45) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-63\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"54.980469\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"93.84375\"/>\n", "       <use xlink:href=\"#DejaVuSans-73\" x=\"155.025391\"/>\n", "       <use xlink:href=\"#DejaVuSans-73\" x=\"207.125\"/>\n", "       <use xlink:href=\"#DejaVuSans-77\" x=\"259.224609\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"341.011719\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"402.291016\"/>\n", "       <use xlink:href=\"#DejaVuSans-6b\" x=\"430.074219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m06ad317d0a\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m06ad317d0a\" x=\"32.09725\" y=\"189.549937\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(14.898625 194.146992) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m06ad317d0a\" x=\"32.09725\" y=\"137.615511\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(7.2 142.212566) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m06ad317d0a\" x=\"32.09725\" y=\"85.681085\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(7.2 90.27814) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m06ad317d0a\" x=\"32.09725\" y=\"33.746659\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(7.2 38.343713) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.69725 189.549937 \n", "L 87.89725 189.549937 \n", "L 87.89725 31.149937 \n", "L 50.69725 31.149937 \n", "z\n", "\" clip-path=\"url(#p299a9ceccf)\" style=\"fill: #c44e52; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 125.09725 189.549937 \n", "L 162.29725 189.549937 \n", "L 162.29725 51.923708 \n", "L 125.09725 51.923708 \n", "z\n", "\" clip-path=\"url(#p299a9ceccf)\" style=\"fill: #55a868; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 199.49725 189.549937 \n", "L 236.69725 189.549937 \n", "L 236.69725 145.405675 \n", "L 199.49725 145.405675 \n", "z\n", "\" clip-path=\"url(#p299a9ceccf)\" style=\"fill: #4c72b0; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 273.89725 189.549937 \n", "L 311.09725 189.549937 \n", "L 311.09725 148.002397 \n", "L 273.89725 148.002397 \n", "z\n", "\" clip-path=\"url(#p299a9ceccf)\" style=\"fill: #ccb974; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 348.29725 189.549937 \n", "L 385.49725 189.549937 \n", "L 385.49725 173.96961 \n", "L 348.29725 173.96961 \n", "z\n", "\" clip-path=\"url(#p299a9ceccf)\" style=\"fill: #64b5cd; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 422.69725 189.549937 \n", "L 459.89725 189.549937 \n", "L 459.89725 179.163052 \n", "L 422.69725 179.163052 \n", "z\n", "\" clip-path=\"url(#p299a9ceccf)\" style=\"fill: #c44e52; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 32.09725 189.549937 \n", "L 32.09725 23.229937 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 478.49725 189.549937 \n", "L 478.49725 23.229937 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 32.09725 189.549937 \n", "L 478.49725 189.549937 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 32.09725 23.229937 \n", "L 478.49725 23.229937 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- Distribution of road points in traffic scene. Total # points: 157 -->\n", "    <g style=\"fill: #262626\" transform=\"translate(51.37375 17.229937) scale(0.132 -0.132)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-44\" d=\"M 1259 4147 \n", "L 1259 519 \n", "L 2022 519 \n", "Q 2988 519 3436 956 \n", "Q 3884 1394 3884 2338 \n", "Q 3884 3275 3436 3711 \n", "Q 2988 4147 2022 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 1925 4666 \n", "Q 3281 4666 3915 4102 \n", "Q 4550 3538 4550 2338 \n", "Q 4550 1131 3912 565 \n", "Q 3275 0 1925 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-23\" d=\"M 3272 2816 \n", "L 2363 2816 \n", "L 2100 1772 \n", "L 3016 1772 \n", "L 3272 2816 \n", "z\n", "M 2803 4594 \n", "L 2478 3297 \n", "L 3391 3297 \n", "L 3719 4594 \n", "L 4219 4594 \n", "L 3897 3297 \n", "L 4872 3297 \n", "L 4872 2816 \n", "L 3775 2816 \n", "L 3519 1772 \n", "L 4513 1772 \n", "L 4513 1294 \n", "L 3397 1294 \n", "L 3072 0 \n", "L 2572 0 \n", "L 2894 1294 \n", "L 1978 1294 \n", "L 1656 0 \n", "L 1153 0 \n", "L 1478 1294 \n", "L 494 1294 \n", "L 494 1772 \n", "L 1594 1772 \n", "L 1856 2816 \n", "L 850 2816 \n", "L 850 3297 \n", "L 1978 3297 \n", "L 2297 4594 \n", "L 2803 4594 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3a\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 0 \n", "L 750 0 \n", "L 750 794 \n", "z\n", "M 750 3309 \n", "L 1409 3309 \n", "L 1409 2516 \n", "L 750 2516 \n", "L 750 3309 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-44\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"77.001953\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"104.785156\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"156.884766\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"196.09375\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"237.207031\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"264.990234\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"328.466797\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"391.845703\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"431.054688\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"458.837891\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"520.019531\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"583.398438\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"615.185547\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"676.367188\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"711.572266\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"743.359375\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"782.222656\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"843.404297\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"904.683594\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"968.160156\"/>\n", "     <use xlink:href=\"#DejaVuSans-70\" x=\"999.947266\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"1063.423828\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1124.605469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1152.388672\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1215.767578\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"1254.976562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1307.076172\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1338.863281\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1366.646484\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1430.025391\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"1461.8125\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"1501.021484\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"1542.134766\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"1603.414062\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"1638.619141\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"1673.824219\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"1701.607422\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"1756.587891\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"1788.375\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"1840.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"1895.455078\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"1956.978516\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"2020.357422\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"2081.880859\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2113.667969\"/>\n", "     <use xlink:href=\"#DejaVuSans-54\" x=\"2145.455078\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"2189.539062\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"2250.720703\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"2289.929688\"/>\n", "     <use xlink:href=\"#DejaVuSans-6c\" x=\"2351.208984\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2378.992188\"/>\n", "     <use xlink:href=\"#DejaVuSans-23\" x=\"2410.779297\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2494.568359\"/>\n", "     <use xlink:href=\"#DejaVuSans-70\" x=\"2526.355469\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"2589.832031\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"2651.013672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"2678.796875\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"2742.175781\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"2781.384766\"/>\n", "     <use xlink:href=\"#DejaVuSans-3a\" x=\"2833.484375\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"2867.175781\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"2898.962891\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"2962.585938\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"3026.208984\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p299a9ceccf\">\n", "   <rect x=\"32.09725\" y=\"23.229937\" width=\"446.4\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x300 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["pd.Series(\n", "    [traffic_scene[\"roads\"][idx][\"type\"] for idx in range(len(traffic_scene[\"roads\"]))]\n", ").value_counts().plot(kind=\"bar\", rot=45, color=cmap)\n", "plt.title(\n", "    f'Distribution of road points in traffic scene. Total # points: {len(traffic_scene[\"roads\"])}'\n", ")\n", "plt.show()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### In-Depth: Road Objects\n", "\n", "This is a list of different road objects in the traffic scene. For each road object, we have information about its position, velocity, size, in which direction it's heading, whether it's a valid object, the type, and the final position of the vehicle."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['position', 'width', 'length', 'height', 'heading', 'velocity', 'valid', 'goalPosition', 'type', 'id', 'mark_as_expert'])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Take the first object\n", "idx = 0\n", "\n", "# For each object, we have this information:\n", "traffic_scene[\"objects\"][idx].keys()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"x\": -8330.69921875,\n", "        \"y\": 8096.3125,\n", "        \"z\": -38.52962875366211\n", "    },\n", "    {\n", "        \"x\": -8329.8330078125,\n", "        \"y\": 8095.95556640625,\n", "        \"z\": -38.500244512748395\n", "    },\n", "    {\n", "        \"x\": -8328.87109375,\n", "        \"y\": 8095.533203125,\n", "        \"z\": -38.49168714732872\n", "    },\n", "    {\n", "        \"x\": -8327.91796875,\n", "        \"y\": 8095.09716796875,\n", "        \"z\": -38.482875825391815\n", "    },\n", "    {\n", "        \"x\": -8326.9345703125,\n", "        \"y\": 8094.64990234375,\n", "        \"z\": -38.4840157041386\n", "    },\n", "    {\n", "        \"x\": -8325.93359375,\n", "        \"y\": 8094.1865234375,\n", "        \"z\": -38.47635243273912\n", "    },\n", "    {\n", "        \"x\": -8324.958984375,\n", "        \"y\": 8093.74658203125,\n", "        \"z\": -38.46117327593842\n", "    },\n", "    {\n", "        \"x\": -8323.9189453125,\n", "        \"y\": 8093.279296875,\n", "        \"z\": -38.463948981567015\n", "    },\n", "    {\n", "        \"x\": -8322.89453125,\n", "        \"y\": 8092.822265625,\n", "        \"z\": -38.461732818135395\n", "    },\n", "    {\n", "        \"x\": -8321.853515625,\n", "        \"y\": 8092.35546875,\n", "        \"z\": -38.441345671330836\n", "    }\n", "]\n"]}], "source": ["# Position contains the (x, y) coordinates for the vehicle at every time step\n", "print(json.dumps(traffic_scene[\"objects\"][idx][\"position\"][:10], indent=4))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2.066138744354248, 4.621184349060059)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Width and length together make the size of the object, and is used to see if there is a collision\n", "traffic_scene[\"objects\"][idx][\"width\"], traffic_scene[\"objects\"][idx][\"length\"]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["An object's heading refers to the direction it is pointing or moving in. The default coordinate system in Nocturne is right-handed, where the positive x and y axes point to the right and downwards, respectively. In a right-handed coordinate system, 0 degrees is located on the x-axis and the angle increases counter-clockwise.\n", "\n", "Because the scene is created from the viewpoint of an ego driver, there may be instances where the heading of certain vehicles is not available. These cases are represented by the value `-10_000`, to indicate that these steps should be filtered out or are invalid."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"535.707672pt\" height=\"218.705656pt\" viewBox=\"0 0 535.**********.705656\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-15T01:19:31.918326</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.0, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 218.705656 \n", "L 535.**********.705656 \n", "L 535.707672 -0 \n", "L 0 -0 \n", "L 0 218.705656 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 82.**********.52 \n", "L 528.**********.52 \n", "L 528.507672 7.2 \n", "L 82.107672 7.2 \n", "L 82.**********.52 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"md243e14d29\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md243e14d29\" x=\"102.398581\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(98.549268 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#md243e14d29\" x=\"192.580399\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(184.881774 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#md243e14d29\" x=\"282.762217\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(275.063592 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#md243e14d29\" x=\"372.944036\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(365.245411 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#md243e14d29\" x=\"463.125854\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(455.427229 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- Time step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(272.785141 208.760469) scale(0.132 -0.132)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"57.958984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"85.742188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"183.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"244.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"276.464844\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"328.564453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"367.773438\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"429.296875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"meff1a1d1e7\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#meff1a1d1e7\" x=\"82.107672\" y=\"165.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- −10000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 170.557055) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"338.28125\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#meff1a1d1e7\" x=\"82.107672\" y=\"135.718744\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −8000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 140.315799) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#meff1a1d1e7\" x=\"82.107672\" y=\"105.477489\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −6000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 110.074543) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#meff1a1d1e7\" x=\"82.107672\" y=\"75.236233\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −4000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 79.833287) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#meff1a1d1e7\" x=\"82.107672\" y=\"44.994977\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −2000 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.67375 49.592032) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"274.658203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#meff1a1d1e7\" x=\"82.107672\" y=\"14.753721\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(64.909047 19.350776) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Heading -->\n", "     <g style=\"fill: #262626\" transform=\"translate(17.229938 117.825281) rotate(-90) scale(0.132 -0.132)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-48\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"75.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"136.71875\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"197.998047\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"261.474609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"289.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"352.636719\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 102.398581 14.76 \n", "L 106.907672 14.760088 \n", "L 111.416763 14.760157 \n", "L 115.925854 14.76018 \n", "L 120.434945 14.760187 \n", "L 124.944036 14.760126 \n", "L 129.453126 14.760128 \n", "L 133.962217 14.760133 \n", "L 138.471308 14.760061 \n", "L 142.980399 14.760105 \n", "L 147.48949 14.760069 \n", "L 151.998581 14.760047 \n", "L 156.507672 14.760049 \n", "L 161.016763 14.760053 \n", "L 165.525854 14.760044 \n", "L 170.034945 14.760043 \n", "L 174.544036 14.76004 \n", "L 179.053126 14.760084 \n", "L 183.562217 14.76004 \n", "L 188.071308 14.760022 \n", "L 192.580399 14.760026 \n", "L 197.08949 14.760009 \n", "L 201.598581 14.760016 \n", "L 206.107672 14.760102 \n", "L 210.616763 14.760029 \n", "L 215.125854 14.760078 \n", "L 219.634945 14.76002 \n", "L 224.144036 14.760056 \n", "L 228.653126 14.760046 \n", "L 233.162217 14.760134 \n", "L 237.671308 14.760071 \n", "L 242.180399 14.760126 \n", "L 246.68949 14.760061 \n", "L 251.198581 14.760136 \n", "L 255.707672 14.760011 \n", "L 260.216763 14.760181 \n", "L 264.725854 14.760188 \n", "L 269.234945 14.760171 \n", "L 273.744036 14.760119 \n", "L 278.253126 14.76016 \n", "L 282.762217 14.760255 \n", "L 287.271308 14.76022 \n", "L 291.780399 14.760192 \n", "L 296.28949 14.760255 \n", "L 300.798581 14.760218 \n", "L 305.307672 14.760167 \n", "L 309.816763 14.760165 \n", "L 314.325854 14.760048 \n", "L 318.834945 14.760108 \n", "L 323.344036 14.760289 \n", "L 327.853126 165.96 \n", "L 332.362217 165.96 \n", "L 336.871308 165.96 \n", "L 341.380399 165.96 \n", "L 345.88949 165.96 \n", "L 350.398581 165.96 \n", "L 354.907672 165.96 \n", "L 359.416763 165.96 \n", "L 363.925854 165.96 \n", "L 368.434945 165.96 \n", "L 372.944036 165.96 \n", "L 377.453126 165.96 \n", "L 381.962217 165.96 \n", "L 386.471308 165.96 \n", "L 390.980399 165.96 \n", "L 395.48949 165.96 \n", "L 399.998581 165.96 \n", "L 404.507672 165.96 \n", "L 409.016763 165.96 \n", "L 413.525854 165.96 \n", "L 418.034945 165.96 \n", "L 422.544036 165.96 \n", "L 427.053126 165.96 \n", "L 431.562217 165.96 \n", "L 436.071308 165.96 \n", "L 440.580399 165.96 \n", "L 445.08949 165.96 \n", "L 449.598581 165.96 \n", "L 454.107672 165.96 \n", "L 458.616763 165.96 \n", "L 463.125854 165.96 \n", "L 467.634945 165.96 \n", "L 472.144036 165.96 \n", "L 476.653126 165.96 \n", "L 481.162217 165.96 \n", "L 485.671308 165.96 \n", "L 490.180399 165.96 \n", "L 494.68949 165.96 \n", "L 499.198581 165.96 \n", "L 503.707672 165.96 \n", "L 508.216763 165.96 \n", "\" clip-path=\"url(#p65a7a60f86)\" style=\"fill: none; stroke: #4c72b0; stroke-width: 1.5; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 82.**********.52 \n", "L 82.107672 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 528.**********.52 \n", "L 528.507672 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 82.**********.52 \n", "L 528.**********.52 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 82.107672 7.2 \n", "L 528.507672 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p65a7a60f86\">\n", "   <rect x=\"82.107672\" y=\"7.2\" width=\"446.4\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x300 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Heading is the direction in which the vehicle is pointing\n", "plt.plot(traffic_scene[\"objects\"][idx][\"heading\"])\n", "plt.xlabel(\"Time step\")\n", "plt.ylabel(\"Heading\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"x\": 8.662109375,\n", "        \"y\": -3.5693359375\n", "    },\n", "    {\n", "        \"x\": 8.662109375,\n", "        \"y\": -3.5693359375\n", "    },\n", "    {\n", "        \"x\": 9.619140625,\n", "        \"y\": -4.2236328125\n", "    },\n", "    {\n", "        \"x\": 9.53125,\n", "        \"y\": -4.3603515625\n", "    },\n", "    {\n", "        \"x\": 9.833984375,\n", "        \"y\": -4.47265625\n", "    },\n", "    {\n", "        \"x\": 10.009765625,\n", "        \"y\": -4.6337890625\n", "    },\n", "    {\n", "        \"x\": 9.74609375,\n", "        \"y\": -4.3994140625\n", "    },\n", "    {\n", "        \"x\": 10.400390625,\n", "        \"y\": -4.6728515625\n", "    },\n", "    {\n", "        \"x\": 10.244140625,\n", "        \"y\": -4.5703125\n", "    },\n", "    {\n", "        \"x\": 10.41015625,\n", "        \"y\": -4.66796875\n", "    }\n", "]\n"]}], "source": ["# Velocity shows the velocity in the x- and y- directions\n", "print(json.dumps(traffic_scene[\"objects\"][idx][\"velocity\"][:10], indent=4))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"506.317906pt\" height=\"218.705656pt\" viewBox=\"0 0 506.**********.705656\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-04-15T01:19:41.473977</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.0, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 218.705656 \n", "L 506.**********.705656 \n", "L 506.317906 0 \n", "L 0 0 \n", "L 0 218.705656 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.52 \n", "L 499.**********.52 \n", "L 499.117906 7.2 \n", "L 52.717906 7.2 \n", "L 52.**********.52 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"meb8cdf0fbe\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#meb8cdf0fbe\" x=\"73.008815\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(69.159503 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#meb8cdf0fbe\" x=\"163.190634\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(155.492009 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#meb8cdf0fbe\" x=\"253.372452\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(245.673827 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#meb8cdf0fbe\" x=\"343.55427\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(335.855645 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#meb8cdf0fbe\" x=\"433.736088\" y=\"173.52\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(426.037463 192.214109) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- Time step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(243.395375 208.760469) scale(0.132 -0.132)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"57.958984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"85.742188\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"183.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"244.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"276.464844\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"328.564453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"367.773438\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"429.296875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m0659a83b45\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0659a83b45\" x=\"52.717906\" y=\"165.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 170.557055) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m0659a83b45\" x=\"52.717906\" y=\"135.72\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.2 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 140.317055) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m0659a83b45\" x=\"52.717906\" y=\"105.48\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.4 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 110.077055) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m0659a83b45\" x=\"52.717906\" y=\"75.24\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.6 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 79.837055) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m0659a83b45\" x=\"52.717906\" y=\"45\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.8 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 49.597055) scale(0.121 -0.121)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m0659a83b45\" x=\"52.717906\" y=\"14.76\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 1.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.975125 19.357055) scale(0.121 -0.121)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- IS VALID -->\n", "     <g style=\"fill: #262626\" transform=\"translate(17.229938 117.909844) rotate(-90) scale(0.132 -0.132)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-56\" d=\"M 1831 0 \n", "L 50 4666 \n", "L 709 4666 \n", "L 2188 738 \n", "L 3669 4666 \n", "L 4325 4666 \n", "L 2547 0 \n", "L 1831 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-41\" d=\"M 2188 4044 \n", "L 1331 1722 \n", "L 3047 1722 \n", "L 2188 4044 \n", "z\n", "M 1831 4666 \n", "L 2547 4666 \n", "L 4325 0 \n", "L 3669 0 \n", "L 3244 1197 \n", "L 1141 1197 \n", "L 716 0 \n", "L 50 0 \n", "L 1831 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4c\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 531 \n", "L 3531 531 \n", "L 3531 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-44\" d=\"M 1259 4147 \n", "L 1259 519 \n", "L 2022 519 \n", "Q 2988 519 3436 956 \n", "Q 3884 1394 3884 2338 \n", "Q 3884 3275 3436 3711 \n", "Q 2988 4147 2022 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 1925 4666 \n", "Q 3281 4666 3915 4102 \n", "Q 4550 3538 4550 2338 \n", "Q 4550 1131 3912 565 \n", "Q 3275 0 1925 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-49\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"29.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"92.96875\"/>\n", "      <use xlink:href=\"#DejaVuSans-56\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-41\" x=\"186.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"255.197266\"/>\n", "      <use xlink:href=\"#DejaVuSans-49\" x=\"310.910156\"/>\n", "      <use xlink:href=\"#DejaVuSans-44\" x=\"340.402344\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <defs>\n", "     <path id=\"me316d16396\" d=\"M 3 0 \n", "L -3 -0 \n", "\" style=\"stroke: #4c72b0\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pd0e5e24406)\">\n", "     <use xlink:href=\"#me316d16396\" x=\"73.008815\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"77.517906\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"82.026997\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"86.536088\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"91.045179\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"95.55427\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"100.063361\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"104.572452\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"109.081543\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"113.590634\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"118.099724\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"122.608815\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"127.117906\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"131.626997\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"136.136088\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"140.645179\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"145.15427\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"149.663361\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"154.172452\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"158.681543\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"163.190634\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"167.699724\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"172.208815\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"176.717906\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"181.226997\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"185.736088\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"190.245179\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"194.75427\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"199.263361\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"203.772452\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"208.281543\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"212.790634\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"217.299724\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"221.808815\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"226.317906\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"230.826997\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"235.336088\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"239.845179\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"244.35427\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"248.863361\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"253.372452\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"257.881543\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"262.390634\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"266.899724\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"271.408815\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"275.917906\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"280.426997\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"284.936088\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"289.445179\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"293.95427\" y=\"14.76\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"298.463361\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"302.972452\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"307.481543\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"311.990634\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"316.499724\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"321.008815\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"325.517906\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"330.026997\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"334.536088\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"339.045179\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"343.55427\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"348.063361\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"352.572452\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"357.081543\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"361.590634\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"366.099724\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"370.608815\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"375.117906\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"379.626997\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"384.136088\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"388.645179\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"393.15427\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"397.663361\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"402.172452\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"406.681543\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"411.190634\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"415.699724\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"420.208815\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"424.717906\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"429.226997\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"433.736088\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"438.245179\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"442.75427\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"447.263361\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"451.772452\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"456.281543\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"460.790634\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"465.299724\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"469.808815\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"474.317906\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "     <use xlink:href=\"#me316d16396\" x=\"478.826997\" y=\"165.96\" style=\"fill: #4c72b0; stroke: #4c72b0\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.52 \n", "L 52.717906 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 499.**********.52 \n", "L 499.117906 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.52 \n", "L 499.**********.52 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.717906 7.2 \n", "L 499.117906 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pd0e5e24406\">\n", "   <rect x=\"52.717906\" y=\"7.2\" width=\"446.4\" height=\"166.32\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x300 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Valid indicates if the state of the vehicle was observed for each timepoint\n", "plt.xlabel(\"Time step\")\n", "plt.ylabel(\"IS VALID\")\n", "plt.plot(traffic_scene[\"objects\"][idx][\"valid\"], \"_\", lw=5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'x': -8275.5390625, 'y': 8071.49462890625, 'z': -38.305127086351085}"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Each object has a goalPosition, an (x, y) position within the scene\n", "traffic_scene[\"objects\"][idx][\"goalPosition\"]"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["'vehicle'"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# Finally, we have the type of the vehicle\n", "traffic_scene[\"objects\"][idx][\"type\"]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### In-Depth: Road Points\n", "\n", "Road points are static objects in the scene."]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['geometry', 'type', 'map_element_id', 'id'])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["traffic_scene[\"roads\"][idx].keys()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["'road_edge'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# This point represents the edge of a road\n", "traffic_scene[\"roads\"][idx][\"type\"]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    {\n", "        \"x\": -8541.524547445766,\n", "        \"y\": 8167.38744960166,\n", "        \"z\": -39.31986597933531\n", "    },\n", "    {\n", "        \"x\": -8541.052105238197,\n", "        \"y\": 8167.254393662127,\n", "        \"z\": -39.32161462356804\n", "    },\n", "    {\n", "        \"x\": -8540.579632931813,\n", "        \"y\": 8167.121444633392,\n", "        \"z\": -39.32336326780076\n", "    },\n", "    {\n", "        \"x\": -8540.107127841891,\n", "        \"y\": 8166.9886121632835,\n", "        \"z\": -39.32336326780076\n", "    },\n", "    {\n", "        \"x\": -8539.634587292505,\n", "        \"y\": 8166.855905899633,\n", "        \"z\": -39.32511191203348\n", "    },\n", "    {\n", "        \"x\": -8539.162008608988,\n", "        \"y\": 8166.723335492638,\n", "        \"z\": -39.326860556266205\n", "    },\n", "    {\n", "        \"x\": -8538.689389127983,\n", "        \"y\": 8166.590910594858,\n", "        \"z\": -39.32860920049893\n", "    },\n", "    {\n", "        \"x\": -8538.216726198703,\n", "        \"y\": 8166.458640862011,\n", "        \"z\": -39.33035784473165\n", "    },\n", "    {\n", "        \"x\": -8537.744017176647,\n", "        \"y\": 8166.326535951388,\n", "        \"z\": -39.33210648896437\n", "    },\n", "    {\n", "        \"x\": -8537.271259426108,\n", "        \"y\": 8166.194605524229,\n", "        \"z\": -39.3338551331971\n", "    }\n", "]\n"]}], "source": ["# Geometry contains the (x, y) position(s) for a road point\n", "# Note that this will be a list for road lanes and edges but a single (x, y) tuple for stop signs and alike\n", "print(json.dumps(traffic_scene[\"roads\"][idx][\"geometry\"][:10], indent=4));"]}], "metadata": {"kernelspec": {"display_name": "gpudrive", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}