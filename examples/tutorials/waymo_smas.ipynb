# Basic Imports
import json
import os
from pathlib import Path
import seaborn as sns
import pandas as pd
%matplotlib inline
import matplotlib.pyplot as plts
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import mediapy
from gpudrive.env.env_torch import GPUDriveTorchEnv
from gpudrive.env.config import EnvConfig
from gpudrive.env.dataset import SceneDataLoader
from gpudrive.visualize.utils import img_from_fig
from torch.utils.data import Dataset, DataLoader
from tqdm.notebook import tqdm
from gpudrive.datatypes.observation import GlobalEgoState

# waymo_dataset.py

import os
import torch
import tensorflow as tf
from torch.utils.data import IterableDataset
from scenedataloader import SceneDataLoader  # gpudrive’s loader
from waymo_open_dataset.protos import scenario_pb2

class WaymoIterableDataset(IterableDataset):
    def __init__(
        self,
        root: str,
        batch_size: int,
        dataset_size: int,
        H: int,
        T: int,
        num_agents: int,
        sample_with_replacement: bool = False,
        shuffle: bool = False,
        seed: int = 42,
    ):
        super().__init__()
        self.loader = SceneDataLoader(
            root=root,
            batch_size=batch_size,
            dataset_size=dataset_size,
            sample_with_replacement=sample_with_replacement,
            shuffle=shuffle,
            seed=seed,
        )
        self.H = H
        self.T = T
        self.N = num_agents

    def parse_scenario(self, path: str):
        """
        Parses one Waymo Scenario TFRecord and returns:
         - past_states:   Tensor [N, H, 4] → (x,y,heading,vel)
         - map_feats:     Tensor [map_dim]    → e.g. rasterized map features
         - future_states: Tensor [N, T, 3]   → (x,y,z)
        """
        # Read the first SequenceExample in this file
        iterator = tf.data.TFRecordDataset(path)
        proto = scenario_pb2.Scenario()
        for raw in iterator:
            proto.ParseFromString(raw.numpy())
            break

        # Build a dict of agent trajectories keyed by track_id
        tracks = {}
        for frame in proto.frames:
            t = frame.timestamp_micros / 1e6  # seconds
            for agent in frame.agents:
                tid = agent.id.id
                # Use lane_centerline or bounding_box center
                # Here: center of bounding box for simplicity
                x = agent.pose_x
                y = agent.pose_y
                heading = agent.heading
                speed = agent.speed
                if tid not in tracks:
                    tracks[tid] = []
                tracks[tid].append((t, x, y, heading, speed))

        # Sort tracks by time, then sample uniformly
        all_ids = list(tracks.keys())[: self.N]  # take up to N agents
        # Create fixed-length past and future arrays
        past = torch.zeros(self.N, self.H, 4, dtype=torch.float32)
        future = torch.zeros(self.N, self.T, 3, dtype=torch.float32)

        # We assume frames are uniformly sampled at Δ seconds; 
        # here we simply take the first H and next T entries per agent
        for i, tid in enumerate(all_ids):
            traj = sorted(tracks[tid], key=lambda x: x[0])
            # past: take first H
            for h in range(min(self.H, len(traj))):
                _, x, y, heading, speed = traj[h]
                past[i, h] = torch.tensor([x, y, heading, speed])
            # future: take next T
            for t in range(self.T):
                idx = h + 1 + t
                if idx < len(traj):
                    _, x, y, _, _ = traj[idx]
                    future[i, t] = torch.tensor([x, y, 0.0])  # z=0

        # Map features placeholder: you should replace this with real rasterization
        map_feats = torch.zeros(1, 64, 64)  # e.g., a 64×64 bird’s-eye map

        return past, map_feats, future

    def __iter__(self):
        for batch_files in self.loader:
            B = len(batch_files)
            past_states = torch.zeros(B, self.N, self.H, 4)
            map_feats   = torch.zeros(B, 1, 64, 64)
            future_states = torch.zeros(B, self.N, self.T, 3)

            for i, path in enumerate(batch_files):
                p, m, f = self.parse_scenario(path)
                past_states[i]   = p
                map_feats[i]     = m
                future_states[i] = f

            yield past_states, map_feats, future_states