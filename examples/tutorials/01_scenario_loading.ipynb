# Dependencies
import json
import os
from pathlib import Path
import seaborn as sns
import pandas as pd
%matplotlib inline
import matplotlib.pyplot as plt

# Set working directory to the base directory 'gpudrive'
working_dir = Path.cwd()
while working_dir.name != 'gpudrive':
    working_dir = working_dir.parent
    if working_dir == Path.home():
        raise FileNotFoundError("Base directory 'gpudrive' not found")
os.chdir(working_dir)

cmap = ["r", "g", "b", "y", "c"]
%config InlineBackend.figure_format = 'svg'
sns.set("notebook", font_scale=1.1, rc={"figure.figsize": (8, 3)})
sns.set_style("ticks", rc={"figure.facecolor": "none", "axes.facecolor": "none"})

from gpudrive.env.dataset import SceneDataLoader

data_loader = SceneDataLoader(
    root="data/processed/examples", # Path to the dataset
    batch_size=10, # Batch size, you want this to be equal to the number of worlds (envs) so that every world receives a different scene
    dataset_size=4, # Total number of different scenes we want to use
    sample_with_replacement=True, 
    seed=42, 
    shuffle=True,   
)

# The full dataset that we will be using
data_loader.dataset

# Notice that it only has 4 unique scenes, since we set the dataset_size to 4
set(data_loader.dataset)

data_files = next(iter(data_loader))

data_files[0]

import sys
sys.path.append('/home/<USER>/work/research/sim_agents/gpudrive/build')
from gpudrive.env.env_torch import GPUDriveTorchEnv
from gpudrive.env.config import EnvConfig

# Pass the data_loader to the environment 
env = GPUDriveTorchEnv(
    config=EnvConfig(),
    data_loader=data_loader,
    max_cont_agents=64,  
    device="cpu",
)

# Take an example scene
data_path = "data/processed/examples/tfrecord-00000-of-01000_325.json"

with open(data_path) as file:
    traffic_scene = json.load(file)

traffic_scene.keys()

traffic_scene["tl_states"]

traffic_scene["name"]

traffic_scene["metadata"]

traffic_scene["scenario_id"]

pd.Series(
    [
        traffic_scene["objects"][idx]["type"]
        for idx in range(len(traffic_scene["objects"]))
    ]
).value_counts().plot(kind="bar", rot=45, color=cmap)
plt.title(
    f'Distribution of road objects in traffic scene. Total # objects: {len(traffic_scene["objects"])}'
)
plt.show()

pd.Series(
    [traffic_scene["roads"][idx]["type"] for idx in range(len(traffic_scene["roads"]))]
).value_counts().plot(kind="bar", rot=45, color=cmap)
plt.title(
    f'Distribution of road points in traffic scene. Total # points: {len(traffic_scene["roads"])}'
)
plt.show()

# Take the first object
idx = 0

# For each object, we have this information:
traffic_scene["objects"][idx].keys()

# Position contains the (x, y) coordinates for the vehicle at every time step
print(json.dumps(traffic_scene["objects"][idx]["position"][:10], indent=4))

# Width and length together make the size of the object, and is used to see if there is a collision
traffic_scene["objects"][idx]["width"], traffic_scene["objects"][idx]["length"]

# Heading is the direction in which the vehicle is pointing
plt.plot(traffic_scene["objects"][idx]["heading"])
plt.xlabel("Time step")
plt.ylabel("Heading")
plt.show()

# Velocity shows the velocity in the x- and y- directions
print(json.dumps(traffic_scene["objects"][idx]["velocity"][:10], indent=4))

# Valid indicates if the state of the vehicle was observed for each timepoint
plt.xlabel("Time step")
plt.ylabel("IS VALID")
plt.plot(traffic_scene["objects"][idx]["valid"], "_", lw=5)
plt.show()

# Each object has a goalPosition, an (x, y) position within the scene
traffic_scene["objects"][idx]["goalPosition"]

# Finally, we have the type of the vehicle
traffic_scene["objects"][idx]["type"]

traffic_scene["roads"][idx].keys()

# This point represents the edge of a road
traffic_scene["roads"][idx]["type"]

# Geometry contains the (x, y) position(s) for a road point
# Note that this will be a list for road lanes and edges but a single (x, y) tuple for stop signs and alike
print(json.dumps(traffic_scene["roads"][idx]["geometry"][:10], indent=4));