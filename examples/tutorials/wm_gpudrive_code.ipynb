# Dependencies
import json
import os
from pathlib import Path
import seaborn as sns
import pandas as pd
%matplotlib inline
import matplotlib.pyplot as plts
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import mediapy
from gpudrive.env.env_torch import GPUDriveTorchEnv
from gpudrive.env.config import EnvConfig
from gpudrive.env.dataset import SceneDataLoader
from gpudrive.visualize.utils import img_from_fig
from torch.utils.data import Dataset, DataLoader
from tqdm.notebook import tqdm
from gpudrive.datatypes.observation import GlobalEgoState


# Set working directory to the base directory 'gpudrive'
working_dir = Path.cwd()
while working_dir.name != 'gpudrive':
    working_dir = working_dir.parent
    if working_dir == Path.home():
        raise FileNotFoundError("Base directory 'gpudrive' not found")
os.chdir(working_dir)

# Plotting settings
cmap = ["r", "g", "b", "y", "c"]
%config InlineBackend.figure_format = 'svg'
sns.set("notebook", font_scale=1.1, rc={"figure.figsize": (8, 3)})
sns.set_style("ticks", rc={"figure.facecolor": "none", "axes.facecolor": "none"})

# Check for GPU availability
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")


# Step 1: Create a data loader for GPUDrive
data_loader = SceneDataLoader(
    root="data/processed/examples",  # Path to the dataset
    batch_size=2,  # Number of parallel environments
    dataset_size=2,  # Total number of different scenes to use
    sample_with_replacement=True,
    seed=42,
    shuffle=True,
)

# Step 2: Create environment configuration
env_config = EnvConfig(
    steer_actions=torch.round(torch.linspace(-1.0, 1.0, 3), decimals=3),
    accel_actions=torch.round(torch.linspace(-3.0, 3.0, 3), decimals=3),
)

# Step 3: Create the environment
env = GPUDriveTorchEnv(
    config=env_config,
    data_loader=data_loader,
    max_cont_agents=64,  # Maximum number of agents to control
    device=device,
)

# Step 4: Reset the environment and collect data
obs = env.reset()
print(f"Observation shape: {obs.shape}")

# Step 5: Define JEPA components

# 5.1 Define the Encoder for JEPA
class JEPAEncoder(nn.Module):
    def __init__(self, state_dim=5, history_len=11, map_dim=128, latent_dim=512):
        super().__init__()
        self.state_dim = state_dim
        self.history_len = history_len
        self.map_dim = map_dim
        self.latent_dim = latent_dim
        
        # Agent state encoder (processes historical states)
        self.state_encoder = nn.Sequential(
            nn.Linear(state_dim * history_len, 256),
            nn.ReLU(),
            nn.Linear(256, 384),
            nn.ReLU()
        )
        
        # Map encoder (simplified - in practice, this would be a GNN like VectorNet)
        self.map_encoder = nn.Sequential(
            nn.Linear(map_dim, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU()
        )
        
        # Fusion layer
        self.fusion = nn.Sequential(
            nn.Linear(384 + 128, latent_dim),
            nn.LayerNorm(latent_dim)
        )
    
    def forward(self, states, map_embedding):
        """
        Args:
            states: [batch_size, num_agents, history_len, state_dim]
            map_embedding: [batch_size, map_dim] or [batch_size, 2, map_dim]
        Returns:
            latent: [batch_size, num_agents, latent_dim]
        """
        batch_size, num_agents = states.shape[0], states.shape[1]
        
        # Print detailed information about input tensors
        print(f"States shape: {states.shape}")
        print(f"Map embedding shape: {map_embedding.shape}")
        
        # Flatten historical states
        states_flat = states.reshape(batch_size, num_agents, -1)  # [B, N, H*S]
        print(f"Flattened states shape: {states_flat.shape}")
        
        # Encode agent states
        agent_encodings = self.state_encoder(states_flat)  # [B, N, 384]
        print(f"Agent encodings shape: {agent_encodings.shape}")
        
        # Try a completely different approach - reshape map_embedding to match expected input
        if len(map_embedding.shape) == 3:  # [batch_size, 2, map_dim]
            # Flatten the map embedding to [batch_size, 2*map_dim]
            map_embedding_flat = map_embedding.reshape(batch_size, -1)
            print(f"Flattened map embedding shape: {map_embedding_flat.shape}")
            
            # Create a new linear layer on-the-fly to handle this larger input
            temp_map_encoder = nn.Linear(map_embedding_flat.shape[1], 128).to(map_embedding.device)
            map_enc = temp_map_encoder(map_embedding_flat)
        else:
            # Standard processing
            map_enc = self.map_encoder(map_embedding)
        
        print(f"Map encoding shape: {map_enc.shape}")
        
        # Expand map encoding to match agent dimensions
        map_enc = map_enc.unsqueeze(1).expand(batch_size, num_agents, -1)
        print(f"Expanded map encoding shape: {map_enc.shape}")
        
        # Concatenate agent and map features
        combined = torch.cat([agent_encodings, map_enc], dim=-1)
        print(f"Combined features shape: {combined.shape}")
        
        # Fuse features
        latent = self.fusion(combined)
        print(f"Output latent shape: {latent.shape}")
        
        return latent

# 5.2 Define the Predictor for JEPA
class JEPAPredictor(nn.Module):
    def __init__(self, latent_dim=512, future_steps=10):
        super().__init__()
        self.latent_dim = latent_dim
        self.future_steps = future_steps
        
        # Predictor network (predicts future latents)
        self.predictor = nn.ModuleList([
            nn.Sequential(
                nn.Linear(latent_dim, latent_dim),
                nn.ReLU(),
                nn.Linear(latent_dim, latent_dim),
                nn.LayerNorm(latent_dim)
            ) for _ in range(future_steps)
        ])
    
    def forward(self, latent):
        """
        Args:
            latent: [batch_size, num_agents, latent_dim]
        Returns:
            predicted_latents: [batch_size, num_agents, future_steps, latent_dim]
        """
        batch_size, num_agents = latent.shape[0], latent.shape[1]
        predicted_latents = torch.zeros(batch_size, num_agents, self.future_steps, self.latent_dim, device=latent.device)
        
        for t in range(self.future_steps):
            predicted_latents[:, :, t] = self.predictor[t](latent)
        
        return predicted_latents

# 5.3 Define the Decoder (to convert latents back to trajectories)
class JEPADecoder(nn.Module):
    def __init__(self, latent_dim=512, state_dim=5):
        super().__init__()
        self.latent_dim = latent_dim
        self.state_dim = state_dim
        
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, 384),
            nn.ReLU(),
            nn.Linear(384, 256),
            nn.ReLU(),
            nn.Linear(256, state_dim)
        )
    
    def forward(self, latent):
        """
        Args:
            latent: [batch_size, num_agents, latent_dim] or [batch_size, num_agents, future_steps, latent_dim]
        Returns:
            trajectories: [batch_size, num_agents, future_steps, state_dim] or [batch_size, num_agents, state_dim]
        """
        if len(latent.shape) == 4:  # [B, N, T, D]
            batch_size, num_agents, future_steps = latent.shape[0], latent.shape[1], latent.shape[2]
            latent_flat = latent.reshape(-1, self.latent_dim)  # [B*N*T, D]
            decoded = self.decoder(latent_flat)  # [B*N*T, state_dim]
            return decoded.reshape(batch_size, num_agents, future_steps, self.state_dim)
        else:  # [B, N, D]
            return self.decoder(latent)  # [B, N, state_dim]

# 5.4 Define the Diffusion Model
class LatentDiffusion(nn.Module):
    def __init__(self, latent_dim=512, time_steps=1000):
        super().__init__()
        self.latent_dim = latent_dim
        self.time_steps = time_steps
        
        # Noise predictor network
        self.noise_predictor = nn.Sequential(
            nn.Linear(latent_dim * 2 + 1, 768),  # latent + condition + time embedding
            nn.SiLU(),
            nn.Linear(768, 768),
            nn.SiLU(),
            nn.Linear(768, latent_dim)
        )
        
        # Define beta schedule for diffusion
        self.beta = torch.linspace(0.0001, 0.02, time_steps).to(device)
        self.alpha = 1. - self.beta
        self.alpha_cumprod = torch.cumprod(self.alpha, dim=0)
    
    def forward(self, latent, condition, t):
        """
        Args:
            latent: [batch_size, num_agents, latent_dim] - noisy latent
            condition: [batch_size, num_agents, latent_dim] - conditioning latent (z_t)
            t: [batch_size, num_agents] - timestep
        Returns:
            predicted_noise: [batch_size, num_agents, latent_dim]
        """
        batch_size, num_agents = latent.shape[0], latent.shape[1]
        
        # Time embedding
        t_emb = t.unsqueeze(-1) / self.time_steps  # [B, N, 1]
        
        # Concatenate inputs
        model_input = torch.cat([latent, condition, t_emb], dim=-1)  # [B, N, 2*latent_dim+1]
        
        # Predict noise
        predicted_noise = self.noise_predictor(model_input)  # [B, N, latent_dim]
        
        return predicted_noise
    
    def add_noise(self, latent, t):
        """Add noise to latent according to diffusion schedule"""
        batch_size, num_agents = latent.shape[0], latent.shape[1]
        
        # Get alpha_cumprod for timestep t
        a = self.alpha_cumprod[t]
        
        # Reshape for broadcasting
        a = a.view(-1, 1, 1)  # [B, 1, 1] or [B*N, 1, 1]
        
        # Generate random noise
        epsilon = torch.randn_like(latent)  # [B, N, D]
        
        # Add noise according to diffusion process
        noisy_latent = torch.sqrt(a) * latent + torch.sqrt(1 - a) * epsilon
        
        return noisy_latent, epsilon
    
    def sample(self, condition, num_steps=100):
        """Sample from the diffusion model"""
        batch_size, num_agents = condition.shape[0], condition.shape[1]
        
        # Start from random noise
        x = torch.randn(batch_size, num_agents, self.latent_dim, device=device)
        
        # Gradually denoise
        for i in reversed(range(0, self.time_steps, self.time_steps // num_steps)):
            t = torch.full((batch_size, num_agents), i, device=device, dtype=torch.long)
            
            # Predict noise
            predicted_noise = self.forward(x, condition, t)
            
            # Get alpha values for current timestep
            alpha = self.alpha[i]
            alpha_cumprod = self.alpha_cumprod[i]
            beta = self.beta[i]
            
            # No noise for last step
            noise = torch.zeros_like(x) if i == 0 else torch.randn_like(x)
            
            # Update x using predicted noise
            x = (1 / torch.sqrt(alpha)) * (x - ((1 - alpha) / torch.sqrt(1 - alpha_cumprod)) * predicted_noise) + torch.sqrt(beta) * noise
        
        return x

# 5.5 Define the complete JEPA model
class JEPA(nn.Module):
    def __init__(self, state_dim=5, history_len=11, future_len=10, map_dim=128, latent_dim=512):
        super().__init__()
        self.state_dim = state_dim
        self.history_len = history_len
        self.future_len = future_len
        self.map_dim = map_dim
        self.latent_dim = latent_dim
        
        # Main components
        self.encoder = JEPAEncoder(state_dim, history_len, map_dim, latent_dim)
        self.predictor = JEPAPredictor(latent_dim, future_len)
        self.decoder = JEPADecoder(latent_dim, state_dim)
        self.diffusion = LatentDiffusion(latent_dim)
        
        # Target encoder (for computing target latents)
        self.target_encoder = JEPAEncoder(state_dim, history_len, map_dim, latent_dim)
        
        # Initialize target encoder with same weights as encoder
        for param, target_param in zip(self.encoder.parameters(), self.target_encoder.parameters()):
            target_param.data.copy_(param.data)
            target_param.requires_grad = False
    
    def forward(self, history_states, future_states, map_embedding):
        """
        Args:
            history_states: [batch_size, num_agents, history_len, state_dim]
            future_states: [batch_size, num_agents, future_len, state_dim]
            map_embedding: [batch_size, map_dim]
        Returns:
            loss dict containing JEPA and diffusion losses
        """
        batch_size, num_agents = history_states.shape[0], history_states.shape[1]
        
        # Encode historical states
        z_t = self.encoder(history_states, map_embedding)  # [B, N, latent_dim]
        
        # Predict future latents
        pred_z_future = self.predictor(z_t)  # [B, N, future_len, latent_dim]
        
        # Compute target latents using target encoder
        with torch.no_grad():
            # For each future timestep, encode the corresponding state
            target_z_future = torch.zeros_like(pred_z_future)
            for t in range(self.future_len):
                # Create input for target encoder: history + 1 future step
                target_input = torch.cat([
                    history_states[:, :, 1:],  # Remove earliest history
                    future_states[:, :, :t+1]  # Add future states up to t+1
                ], dim=2)[:, :, -self.history_len:]  # Keep only last history_len steps
                
                # Encode with target encoder
                target_z_future[:, :, t] = self.target_encoder(target_input, map_embedding)
        
        # JEPA loss: MSE between predicted and target latents
        jepa_loss = F.mse_loss(pred_z_future, target_z_future)
        
        # Diffusion loss
        diffusion_loss = 0.0
        for t in range(self.future_len):
            # Sample timestep for diffusion
            diff_t = torch.randint(0, self.diffusion.time_steps, (batch_size, num_agents), device=device)
            
            # Add noise to target latent
            noisy_latent, noise = self.diffusion.add_noise(target_z_future[:, :, t], diff_t)
            
            # Predict noise
            pred_noise = self.diffusion(noisy_latent, z_t, diff_t)
            
            # Compute diffusion loss
            diffusion_loss += F.mse_loss(pred_noise, noise)
        
        diffusion_loss /= self.future_len
        
        # Reconstruction loss (optional)
        pred_trajectories = self.decoder(pred_z_future)
        recon_loss = F.mse_loss(pred_trajectories, future_states)
        
        return {
            'jepa_loss': jepa_loss,
            'diffusion_loss': diffusion_loss,
            'recon_loss': recon_loss,
            'total_loss': jepa_loss + diffusion_loss + 0.1 * recon_loss
        }
    
    def predict(self, history_states, map_embedding, num_samples=1, diffusion_steps=50):
        """
        Generate future trajectories
        Args:
            history_states: [batch_size, num_agents, history_len, state_dim]
            map_embedding: [batch_size, map_dim]
            num_samples: Number of different futures to sample
            diffusion_steps: Number of diffusion steps for sampling
        Returns:
            predicted_trajectories: [batch_size, num_agents, num_samples, future_len, state_dim]
        """
        batch_size, num_agents = history_states.shape[0], history_states.shape[1]
        
        # Encode historical states
        z_t = self.encoder(history_states, map_embedding)  # [B, N, latent_dim]
        
        # Generate samples
        all_samples = []
        for _ in range(num_samples):
            # Sample from diffusion model
            sampled_latents = []
            for t in range(self.future_len):
                # For each future timestep, sample from diffusion
                sampled_latent = self.diffusion.sample(z_t, diffusion_steps)  # [B, N, latent_dim]
                sampled_latents.append(sampled_latent)
            
            # Stack sampled latents
            sampled_latents = torch.stack(sampled_latents, dim=2)  # [B, N, future_len, latent_dim]
            
            # Decode to trajectories
            sampled_trajectories = self.decoder(sampled_latents)  # [B, N, future_len, state_dim]
            all_samples.append(sampled_trajectories)
        
        # Stack all samples
        all_samples = torch.stack(all_samples, dim=2)  # [B, N, num_samples, future_len, state_dim]
        
        return all_samples


# Create a simple progress tracking function that doesn't depend on IPython
def simple_progress(iterable, desc=None):
    """A very simple progress tracker that works without any dependencies"""
    if desc:
        print(f"{desc}...")
    
    total = len(iterable) if hasattr(iterable, '__len__') else None
    
    for i, item in enumerate(iterable):
        if total and i % max(1, total // 10) == 0:
            print(f"Progress: {i}/{total} ({i/total*100:.1f}%)")
        yield item
    
    if total:
        print(f"Completed: {total}/{total} (100%)")

# Step 6: Create a dataset for training JEPA
class TrajectoryDataset(Dataset):
    def __init__(self, env, num_samples=100, history_len=11, future_len=10):
        self.env = env
        self.num_samples = num_samples
        self.history_len = history_len
        self.future_len = future_len
        self.data = self._collect_data()
    
    def _collect_data(self):
        data = []
        
        print(f"Collecting {self.num_samples} trajectory samples...")
        for i in range(self.num_samples):
            if i % 5 == 0:
                print(f"Sample {i}/{self.num_samples}")
                
            # Reset environment
            obs = self.env.reset()
            
            # Get initial state
            states = []
            
            # Run simulation to collect trajectories
            for t in range(self.history_len + self.future_len):
                # Get agent states
                agent_states = self._extract_agent_states()
                states.append(agent_states)
                
                # Take random actions
                rand_action = torch.randint(
                    low=0,
                    high=self.env.action_space.n,
                    size=(self.env.num_worlds, self.env.max_agent_count),
                    device=self.env.device,
                )
                
                # Step environment
                self.env.step_dynamics(rand_action)
            
            # Stack states
            states = torch.stack(states, dim=1)  # [num_agents, timesteps, state_dim]
            
            # Extract map features (simplified)
            map_embedding = self._extract_map_features()
            
            # Split into history and future
            history = states[:, :self.history_len]
            future = states[:, self.history_len:]
            
            # Add to dataset
            data.append((history, future, map_embedding))
        
        print(f"Data collection complete. Collected {len(data)} samples.")
        return data
    
    def _extract_agent_states(self):
        """Extract agent states from environment"""
        # Get agent states from the environment's observation tensors
        # Based on the codebase, we need to use the absolute self observation tensor
        
        # Get global agent observations
        global_agent_obs = GlobalEgoState.from_tensor(
            abs_self_obs_tensor=self.env.sim.absolute_self_observation_tensor(),
            backend=self.env.backend,
            device=self.env.device,
        )
        
        # Extract position, heading, and velocity
        # Using the correct attribute names from GlobalEgoState
        agent_pos = torch.stack([global_agent_obs.pos_x, global_agent_obs.pos_y], dim=-1)  # [num_worlds, num_agents, 2]
        agent_heading = global_agent_obs.rotation_angle.unsqueeze(-1)  # [num_worlds, num_agents, 1]
        
        # Since velocity is not directly available, we'll use zeros for now
        # In a real implementation, you would calculate velocity from position changes
        num_worlds, num_agents = global_agent_obs.shape
        agent_vel = torch.zeros((num_worlds, num_agents, 2), device=self.env.device)
        
        # Calculate speed (magnitude of velocity)
        speed = torch.zeros((num_worlds, num_agents, 1), device=self.env.device)
        
        # Calculate acceleration (simplified - just use a zero tensor for now)
        accel = torch.zeros((num_worlds, num_agents, 1), device=self.env.device)
        
        # Combine into state vector [x, y, heading, speed, accel]
        states = torch.cat([agent_pos, agent_heading, speed, accel], dim=-1)  # [num_worlds, num_agents, 5]
        
        # Reshape to [num_agents, state_dim]
        # We flatten the batch dimension (num_worlds) into the agent dimension
        states = states.reshape(num_worlds * num_agents, states.shape[-1])
        
        return states
    
    def _extract_map_features(self):
        """Extract map features from environment"""
        # For simplicity, just create a random map embedding
        # In a real implementation, you would extract features from the map
        batch_size = self.env.num_worlds
        map_dim = 128
        map_embedding = torch.randn(batch_size, map_dim, device=self.env.device)
        
        return map_embedding
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        history, future, map_embedding = self.data[idx]
        return history, future, map_embedding

# Train the model
def train_jepa(model, dataset, num_epochs=5, batch_size=2, learning_rate=1e-4):
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    
    # Training loop
    for epoch in range(num_epochs):
        total_loss = 0.0
        num_batches = 0
        
        # Create DataLoader
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=batch_size, shuffle=True
        )
        
        for batch in dataloader:
            # Unpack batch
            history_states, future_states, map_embedding = batch
            
            # Print shapes for debugging
            print(f"Batch shapes - History: {history_states.shape}, Future: {future_states.shape}, Map: {map_embedding.shape}")
            
            # Zero gradients
            optimizer.zero_grad()
            
            try:
                # Forward pass
                loss_dict = model(history_states, future_states, map_embedding)
                
                # Calculate total loss
                total_loss_val = sum(loss_dict.values())
                
                # Backward pass
                total_loss_val.backward()
                
                # Update weights
                optimizer.step()
                
                # Accumulate loss
                total_loss += total_loss_val.item()
                num_batches += 1
                
                print(f"Batch processed successfully")
            except Exception as e:
                print(f"Error in batch: {e}")
                # Print tensor shapes for debugging
                print(f"Tensor shapes in error: History={history_states.shape}, Future={future_states.shape}, Map={map_embedding.shape}")
                continue
        
        # Print epoch statistics
        avg_loss = total_loss / num_batches if num_batches > 0 else float('inf')
        print(f"Epoch {epoch+1}/{num_epochs}, Average Loss: {avg_loss:.4f}")
    
    return model

# Create the dataset
dataset = TrajectoryDataset(
    env=env,
    num_samples=5,  # Number of trajectories to collect
    history_len=11,  # 11 timesteps of history
    future_len=10    # 10 timesteps to predict
)

print(f"Dataset created with {len(dataset)} samples")

# Create the JEPA model
jepa_model = JEPA(
    state_dim=5,           # x, y, heading, velocity, acceleration
    history_len=11,        # 11 timesteps of history
    future_len=10,         # 10 timesteps to predict
    map_dim=128,           # Map embedding dimension
    latent_dim=512         # Latent space dimension
)

# Train the model
trained_model = train_jepa(
    model=jepa_model,
    dataset=dataset,
    num_epochs=5,          # Number of training epochs
    batch_size=2,          # Batch size
    learning_rate=1e-4     # Learning rate
)

# Save the trained model
torch.save(trained_model.state_dict(), "jepa_model.pth")

# Step 8: Evaluate the model
def evaluate_jepa(model, dataset, num_samples=5):
    # Create data loader with batch size 1
    dataloader = DataLoader(dataset, batch_size=1, shuffle=True)
    
    model.to(device)
    model.eval()
    
    with torch.no_grad():
        for i, (history, future, map_embedding) in enumerate(dataloader):
            if i >= num_samples:
                break
                
            # Move data to device
            history = history.to(device)
            future = future.to(device)
            map_embedding = map_embedding.to(device)
            
            # Encode historical states
            z_t = model.encoder(history, map_embedding)
            
            # Predict future latents
            pred_z_future = model.predictor(z_t)
            
            # Decode to trajectories
            pred_trajectories = model.decoder(pred_z_future)
            
            # Plot results
            plt.figure(figsize=(12, 6))
            
            # Get the first agent's trajectory for visualization
            true_traj = future[0, 0].cpu().numpy()
            pred_traj = pred_trajectories[0, 0].cpu().numpy()
            hist_traj = history[0, 0].cpu().numpy()
            
            # Plot x, y positions
            plt.subplot(1, 2, 1)
            plt.plot(hist_traj[:, 0], hist_traj[:, 1], 'b-', label='History')
            plt.plot(true_traj[:, 0], true_traj[:, 1], 'g-', label='Ground Truth')
            plt.plot(pred_traj[:, 0], pred_traj[:, 1], 'r--', label='Prediction')
            plt.legend()
            plt.title('Trajectory Prediction')
            plt.xlabel('X Position')
            plt.ylabel('Y Position')
            
            # Plot velocity over time
            plt.subplot(1, 2, 2)
            time_steps = np.arange(future.shape[2])
            plt.plot(time_steps, true_traj[:, 3], 'g-', label='Ground Truth')
            plt.plot(time_steps, pred_traj[:, 3], 'r--', label='Prediction')
            plt.legend()
            plt.title('Velocity Prediction')
            plt.xlabel('Time Step')
            plt.ylabel('Velocity')
            
            plt.tight_layout()
            plt.show()

# Evaluate the model
evaluate_jepa(trained_model, dataset, num_samples=3)

def visualize_predictions_simple(model, dataset, num_samples=2):
    """
    Simplified visualization of model predictions using matplotlib.
    """
    import matplotlib.pyplot as plt
    import numpy as np
    
    # Get a batch from the dataset
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=num_samples, shuffle=True)
    batch = next(iter(dataloader))
    history_states, future_states, map_embedding = batch
    
    print(f"History states shape: {history_states.shape}")
    print(f"Future states shape: {future_states.shape}")
    
    # Put model in evaluation mode
    model.eval()
    
    with torch.no_grad():
        # Encode historical states
        z_t = model.encoder(history_states, map_embedding)
        
        # Predict future latents
        pred_z_future = model.predictor(z_t)
        
        # Decode to trajectories
        pred_trajectories = model.decoder(pred_z_future)
        
        print(f"Predicted trajectories shape: {pred_trajectories.shape}")
    
    # Plot results for each sample
    for i in range(num_samples):
        plt.figure(figsize=(12, 6))
        
        # Get trajectories for visualization
        hist_traj = history_states[i, 0].cpu().numpy()  # First agent
        true_traj = future_states[i, 0].cpu().numpy()   # First agent
        pred_traj = pred_trajectories[i, 0].cpu().numpy()  # First agent
        
        # Plot x, y positions
        plt.subplot(1, 2, 1)
        plt.plot(hist_traj[:, 0], hist_traj[:, 1], 'b-', label='History')
        plt.plot(true_traj[:, 0], true_traj[:, 1], 'g-', label='Ground Truth')
        plt.plot(pred_traj[:, 0], pred_traj[:, 1], 'r--', label='Prediction')
        plt.legend()
        plt.title(f'Sample {i+1}: Trajectory Prediction')
        plt.xlabel('X Position')
        plt.ylabel('Y Position')
        
        # Plot heading over time
        plt.subplot(1, 2, 2)
        time_steps = np.arange(future_states.shape[2])
        
        # Check if we have heading information (usually index 2)
        if true_traj.shape[1] > 2 and pred_traj.shape[1] > 2:
            plt.plot(time_steps, true_traj[:, 2], 'g-', label='Ground Truth')
            plt.plot(time_steps, pred_traj[:, 2], 'r--', label='Prediction')
            plt.legend()
            plt.title('Heading Prediction')
            plt.xlabel('Time Step')
            plt.ylabel('Heading')
        else:
            # If no heading, plot distance from start
            true_dist = np.sqrt(np.sum((true_traj - true_traj[0])**2, axis=1))
            pred_dist = np.sqrt(np.sum((pred_traj - pred_traj[0])**2, axis=1))
            plt.plot(time_steps, true_dist, 'g-', label='Ground Truth')
            plt.plot(time_steps, pred_dist, 'r--', label='Prediction')
            plt.legend()
            plt.title('Distance from Start')
            plt.xlabel('Time Step')
            plt.ylabel('Distance')
        
        plt.tight_layout()
        plt.show()

# Run the simplified visualization
visualize_predictions_simple(trained_model, dataset, num_samples=2)

# Run the visualization
%matplotlib inline
visualize_predictions_simple(trained_model, dataset, num_samples=2)


def visualize_predictions_birdseye(model, dataset, num_samples=2):
    """
    Visualize model predictions using bird's eye view plots.
    """
    from PIL import Image
    import matplotlib.pyplot as plt
    import numpy as np
    
    # Get a batch from the dataset
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=num_samples, shuffle=True)
    batch = next(iter(dataloader))
    history_states, future_states, map_embedding = batch
    
    print(f"History states shape: {history_states.shape}")
    print(f"Future states shape: {future_states.shape}")
    
    # Create environment for visualization
    from gpudrive.env.config import EnvConfig, RenderConfig
    from gpudrive.env.dataset import SceneDataLoader
    from gpudrive.env.env_torch import GPUDriveTorchEnv
    from gpudrive.visualize.utils import img_from_fig
    
    # Set up environment
    render_config = RenderConfig(render_3d=False)  # Use 2D rendering for bird's eye view
    env_config = EnvConfig(dynamics_model="delta_local")
    
    # Create data loader from the same data as the dataset
    data_loader = SceneDataLoader(
        root="data/processed/examples",
        batch_size=num_samples,
        dataset_size=num_samples,
        sample_with_replacement=True,
    )
    
    # Create environment
    env = GPUDriveTorchEnv(
        config=env_config,
        data_loader=data_loader,
        max_cont_agents=64,
        device="cpu",
        render_config=render_config,
        action_type="continuous"
    )
    
    # Reset environment
    _ = env.reset(env.cont_agent_mask)
    
    # Put model in evaluation mode
    model.eval()
    
    with torch.no_grad():
        # Encode historical states
        z_t = model.encoder(history_states, map_embedding)
        
        # Predict future latents
        pred_z_future = model.predictor(z_t)
        
        # Decode to trajectories
        pred_trajectories = model.decoder(pred_z_future)
        
        print(f"Predicted trajectories shape: {pred_trajectories.shape}")
    
    # For each sample, create a bird's eye view visualization
    for i in range(num_samples):
        # Plot a bird's eye view of the environment
        sim_state_figs = env.vis.plot_simulator_state(
            env_indices=[i],
            zoom_radius=50,
            time_steps=[0],
        )
        
        # Convert figure to image and display
        img = img_from_fig(sim_state_figs[0])
        plt.figure(figsize=(10, 10))
        plt.imshow(img)
        plt.axis('off')
        plt.title(f"Bird's Eye View - Sample {i+1}")
        plt.show()
        
        # Close the figure to free memory
        plt.close(sim_state_figs[0])
        
        # Now overlay the predicted trajectory on a new plot
        plt.figure(figsize=(10, 10))
        
        # Get trajectories for visualization
        hist_traj = history_states[i, 0].cpu().numpy()  # First agent
        true_traj = future_states[i, 0].cpu().numpy()   # First agent
        pred_traj = pred_trajectories[i, 0].cpu().numpy()  # First agent
        
        # Plot x, y positions
        plt.plot(hist_traj[:, 0], hist_traj[:, 1], 'b-', linewidth=2, label='History')
        plt.plot(true_traj[:, 0], true_traj[:, 1], 'g-', linewidth=2, label='Ground Truth')
        plt.plot(pred_traj[:, 0], pred_traj[:, 1], 'r--', linewidth=2, label='Prediction')
        
        # Add markers for start and end points
        plt.scatter(hist_traj[0, 0], hist_traj[0, 1], c='blue', s=100, marker='o', label='Start')
        plt.scatter(true_traj[-1, 0], true_traj[-1, 1], c='green', s=100, marker='x', label='True End')
        plt.scatter(pred_traj[-1, 0], pred_traj[-1, 1], c='red', s=100, marker='x', label='Predicted End')
        
        plt.legend(loc='upper right')
        plt.title(f"Trajectory Prediction - Sample {i+1}")
        plt.axis('equal')  # Equal aspect ratio
        plt.grid(True)
        plt.show()

# Run the bird's eye view visualization
visualize_predictions_birdseye(trained_model, dataset, num_samples=2)