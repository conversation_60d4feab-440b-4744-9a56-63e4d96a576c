{"cells": [{"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["# Dependencies\n", "import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "import warnings\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import datetime\n", "\n", "sns.set(\"notebook\", font_scale=1.05, rc={\"figure.figsize\": (10, 5)})\n", "sns.set_style(\"ticks\", rc={\"figure.facecolor\": \"none\", \"axes.facecolor\": \"none\"})\n", "%config InlineBackend.figure_format = 'svg'\n", "warnings.filterwarnings(\"ignore\")\n", "plt.set_loglevel(\"WARNING\")\n", "mpl.rcParams[\"lines.markersize\"] = 8\n", "\n", "# generate datetime string\n", "now = datetime.datetime.now()\n", "now_str = now.strftime(\"%m_%d_%H_%M\")"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["WINDOW_SIZE = 120\n", "\n", "\n", "df = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/goal_wallclock.csv\")\n", "df2 = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/offroad_wallclock.csv\")\n", "df3 = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/collision_wallclock.csv\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["wallclock_time = df['performance/uptime'].rolling(window=WINDOW_SIZE).mean().dropna().values[:X_LIMIT]\n", "perc_goal_achieved = df['finetune - metrics/perc_goal_achieved'].rolling(window=WINDOW_SIZE).mean().dropna().values[:X_LIMIT] *100\n", "\n", "perc_offroad = df2['finetune - metrics/perc_off_road'].rolling(window=WINDOW_SIZE).mean().dropna().values[:X_LIMIT] *100\n", "perc_collided = df3['finetune - metrics/perc_veh_collisions'].rolling(window=WINDOW_SIZE).mean().dropna().values[:X_LIMIT] * 100\n", "\n", "\n", "wallclock_time = wallclock_time / 60"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"341.1405pt\" height=\"273.032672pt\" viewBox=\"0 0 341.1405 273.032672\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-01-30T21:40:22.093007</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 273.032672 \n", "L 341.1405 273.032672 \n", "L 341.1405 0 \n", "L 0 0 \n", "L 0 273.032672 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 54.9405 228.96 \n", "L 333.9405 228.96 \n", "L 333.9405 7.2 \n", "L 54.9405 7.2 \n", "L 54.9405 228.96 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m751b5da980\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"54.9405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(51.266156 247.236195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"92.1405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(88.466156 247.236195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"129.3405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 4 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(125.666156 247.236195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"166.5405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 6 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(162.866156 247.236195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"203.7405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 8 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(200.066156 247.236195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"240.9405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 10 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(233.591812 247.236195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"278.1405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 12 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(270.791813 247.236195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m751b5da980\" x=\"315.3405\" y=\"228.96\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 14 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(307.991813 247.236195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- Wallclock time (minutes) -->\n", "     <g style=\"fill: #262626\" transform=\"translate(116.416969 263.212266) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-57\" d=\"M 213 4666 \n", "L 850 4666 \n", "L 1831 722 \n", "L 2809 4666 \n", "L 3519 4666 \n", "L 4500 722 \n", "L 5478 4666 \n", "L 6119 4666 \n", "L 4947 0 \n", "L 4153 0 \n", "L 3169 4050 \n", "L 2175 0 \n", "L 1381 0 \n", "L 213 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-57\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"92.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"153.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"181.564453\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"209.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"264.328125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"292.111328\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"353.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"408.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"466.183594\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"497.970703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"537.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"564.962891\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"662.375\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"723.898438\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"755.685547\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"794.699219\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"892.111328\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"919.894531\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"983.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1046.652344\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1085.861328\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1147.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"1199.484375\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 54.9405 219.113775 \n", "L 333.9405 219.113775 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"maa52f73f41\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#maa52f73f41\" x=\"54.9405\" y=\"219.113775\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(38.091813 223.501873) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 54.9405 178.358873 \n", "L 333.9405 178.358873 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#maa52f73f41\" x=\"54.9405\" y=\"178.358873\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 182.74697) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 54.9405 137.60397 \n", "L 333.9405 137.60397 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#maa52f73f41\" x=\"54.9405\" y=\"137.60397\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 141.992068) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 54.9405 96.849068 \n", "L 333.9405 96.849068 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#maa52f73f41\" x=\"54.9405\" y=\"96.849068\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 101.237166) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 54.9405 56.094166 \n", "L 333.9405 56.094166 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#maa52f73f41\" x=\"54.9405\" y=\"56.094166\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 60.482263) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 54.9405 15.339263 \n", "L 333.9405 15.339263 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#maa52f73f41\" x=\"54.9405\" y=\"15.339263\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 100 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394437 19.727361) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- Performance -->\n", "     <g style=\"fill: #262626\" transform=\"translate(16.774031 157.999359) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"56.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"118.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"159.314453\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"194.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"255.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"295.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"392.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"453.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"517.134766\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"572.115234\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 82.981524 208.352445 \n", "L 88.337666 210.470575 \n", "L 93.776061 210.930641 \n", "L 99.294813 210.332359 \n", "L 104.909325 209.766108 \n", "L 110.577425 209.416992 \n", "L 116.310185 208.478776 \n", "L 122.092267 207.501327 \n", "L 127.946678 203.444257 \n", "L 133.867431 197.387706 \n", "L 139.877505 189.523871 \n", "L 145.969123 177.989213 \n", "L 152.180436 165.234166 \n", "L 158.510119 151.751226 \n", "L 164.984036 136.429727 \n", "L 171.646577 120.117177 \n", "L 178.120595 103.091665 \n", "L 184.420765 85.998989 \n", "L 190.517592 71.83908 \n", "L 196.421523 59.495116 \n", "L 202.115362 48.915893 \n", "L 207.582157 42.131433 \n", "L 212.797095 36.446306 \n", "L 217.74692 31.52845 \n", "L 222.403441 28.612743 \n", "L 226.715457 26.598015 \n", "L 231.057187 25.907501 \n", "L 235.410191 25.315784 \n", "L 239.77799 24.852636 \n", "L 244.153222 24.569414 \n", "L 248.541938 24.393366 \n", "L 252.94086 24.101299 \n", "L 257.350071 23.705452 \n", "L 261.765477 23.304075 \n", "L 266.174665 22.701614 \n", "L 270.589564 22.093872 \n", "L 275.008736 21.448131 \n", "L 279.431564 20.884251 \n", "L 283.858931 20.315887 \n", "L 288.285341 19.744882 \n", "L 292.705154 19.099449 \n", "L 297.124847 18.576988 \n", "L 301.548395 18.209336 \n", "L 305.975268 17.918314 \n", "L 310.401671 17.666537 \n", "L 314.831482 17.569923 \n", "L 319.26906 17.512321 \n", "L 323.713056 17.458033 \n", "L 328.152872 17.416946 \n", "L 332.605273 17.41822 \n", "L 337.06398 17.417544 \n", "L 341.531774 17.385122 \n", "L 342.1405 17.380792 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #4c72b0; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 82.981524 218.52688 \n", "L 88.337666 218.672752 \n", "L 93.776061 218.692472 \n", "L 99.294813 218.66426 \n", "L 104.909325 218.643015 \n", "L 110.577425 218.631873 \n", "L 116.310185 218.635549 \n", "L 122.092267 218.571002 \n", "L 127.946678 217.93341 \n", "L 133.867431 217.290936 \n", "L 139.877505 216.798867 \n", "L 145.969123 215.823533 \n", "L 152.180436 215.097446 \n", "L 158.510119 214.407466 \n", "L 164.984036 213.826067 \n", "L 171.646577 213.18068 \n", "L 178.120595 212.529098 \n", "L 184.420765 211.979549 \n", "L 190.517592 211.839842 \n", "L 196.421523 211.902527 \n", "L 202.115362 211.542331 \n", "L 207.582157 211.588925 \n", "L 212.797095 211.671019 \n", "L 217.74692 211.762257 \n", "L 222.403441 211.787173 \n", "L 226.715457 211.939529 \n", "L 231.057187 211.98839 \n", "L 235.410191 211.936822 \n", "L 239.77799 212.231596 \n", "L 244.153222 212.511088 \n", "L 248.541938 213.033913 \n", "L 252.94086 213.738163 \n", "L 257.350071 213.940381 \n", "L 261.765477 214.354344 \n", "L 266.174665 214.691261 \n", "L 270.589564 214.957874 \n", "L 275.008736 215.439475 \n", "L 279.431564 215.972088 \n", "L 283.858931 216.357544 \n", "L 288.285341 216.53385 \n", "L 292.705154 216.775357 \n", "L 297.124847 216.917777 \n", "L 301.548395 217.281517 \n", "L 305.975268 217.38077 \n", "L 310.401671 217.485515 \n", "L 314.831482 217.681389 \n", "L 319.26906 217.686935 \n", "L 323.713056 217.799507 \n", "L 328.152872 217.814743 \n", "L 332.605273 217.819171 \n", "L 337.06398 217.885236 \n", "L 341.531774 217.916219 \n", "L 342.1405 217.924726 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #c44e52; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <path d=\"M 82.981524 216.246609 \n", "L 88.337666 218.08232 \n", "L 93.776061 218.88 \n", "L 99.294813 218.864455 \n", "L 104.909325 218.837371 \n", "L 110.577425 218.829782 \n", "L 116.310185 218.67576 \n", "L 122.092267 218.493074 \n", "L 127.946678 217.684041 \n", "L 133.867431 217.108616 \n", "L 139.877505 216.7271 \n", "L 145.969123 216.418832 \n", "L 152.180436 216.042385 \n", "L 158.510119 215.796242 \n", "L 164.984036 215.578306 \n", "L 171.646577 215.4513 \n", "L 178.120595 215.488157 \n", "L 184.420765 215.595036 \n", "L 190.517592 216.310258 \n", "L 196.421523 216.775437 \n", "L 202.115362 217.04549 \n", "L 207.582157 217.239784 \n", "L 212.797095 217.45238 \n", "L 217.74692 217.628267 \n", "L 222.403441 217.878483 \n", "L 226.715457 217.92524 \n", "L 231.057187 217.98285 \n", "L 235.410191 217.950009 \n", "L 239.77799 217.948896 \n", "L 244.153222 218.023842 \n", "L 248.541938 218.065326 \n", "L 252.94086 218.152891 \n", "L 257.350071 218.214623 \n", "L 261.765477 218.203648 \n", "L 266.174665 218.090383 \n", "L 270.589564 218.094756 \n", "L 275.008736 218.062018 \n", "L 279.431564 218.010523 \n", "L 283.858931 217.995213 \n", "L 288.285341 217.923073 \n", "L 292.705154 217.928165 \n", "L 297.124847 217.917965 \n", "L 301.548395 217.961166 \n", "L 305.975268 218.047063 \n", "L 310.401671 218.015004 \n", "L 314.831482 218.058715 \n", "L 319.26906 218.119451 \n", "L 323.713056 218.275901 \n", "L 328.152872 218.346879 \n", "L 332.605273 218.414141 \n", "L 337.06398 218.468328 \n", "L 341.531774 218.487796 \n", "L 342.1405 218.486225 \n", "\" clip-path=\"url(#p164dd59020)\" style=\"fill: none; stroke: #ffa500; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 54.9405 228.96 \n", "L 54.9405 7.2 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 54.9405 228.96 \n", "L 333.9405 228.96 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_5\">\n", "     <path d=\"M 61.9405 59.234375 \n", "L 185.6905 59.234375 \n", "Q 187.6905 59.234375 187.6905 57.234375 \n", "L 187.6905 14.2 \n", "Q 187.6905 12.2 185.6905 12.2 \n", "L 61.9405 12.2 \n", "Q 59.9405 12.2 59.9405 14.2 \n", "L 59.9405 57.234375 \n", "Q 59.9405 59.234375 61.9405 59.234375 \n", "z\n", "\" style=\"fill: #ffffff; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_24\">\n", "     <path d=\"M 63.9405 20.298438 \n", "L 73.9405 20.298438 \n", "L 83.9405 20.298438 \n", "\" style=\"fill: none; stroke: #4c72b0; stroke-width: 2; stroke-linecap: round\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Goal achieved [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(91.9405 23.798438) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5b\" d=\"M 550 4863 \n", "L 1875 4863 \n", "L 1875 4416 \n", "L 1125 4416 \n", "L 1125 -397 \n", "L 1875 -397 \n", "L 1875 -844 \n", "L 550 -844 \n", "L 550 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-25\" d=\"M 4653 2053 \n", "Q 4381 2053 4226 1822 \n", "Q 4072 1591 4072 1178 \n", "Q 4072 772 4226 539 \n", "Q 4381 306 4653 306 \n", "Q 4919 306 5073 539 \n", "Q 5228 772 5228 1178 \n", "Q 5228 1588 5073 1820 \n", "Q 4919 2053 4653 2053 \n", "z\n", "M 4653 2450 \n", "Q 5147 2450 5437 2106 \n", "Q 5728 1763 5728 1178 \n", "Q 5728 594 5436 251 \n", "Q 5144 -91 4653 -91 \n", "Q 4153 -91 3862 251 \n", "Q 3572 594 3572 1178 \n", "Q 3572 1766 3864 2108 \n", "Q 4156 2450 4653 2450 \n", "z\n", "M 1428 4353 \n", "Q 1159 4353 1004 4120 \n", "Q 850 3888 850 3481 \n", "Q 850 3069 1003 2837 \n", "Q 1156 2606 1428 2606 \n", "Q 1700 2606 1854 2837 \n", "Q 2009 3069 2009 3481 \n", "Q 2009 3884 1853 4118 \n", "Q 1697 4353 1428 4353 \n", "z\n", "M 4250 4750 \n", "L 4750 4750 \n", "L 1831 -91 \n", "L 1331 -91 \n", "L 4250 4750 \n", "z\n", "M 1428 4750 \n", "Q 1922 4750 2215 4408 \n", "Q 2509 4066 2509 3481 \n", "Q 2509 2891 2217 2550 \n", "Q 1925 2209 1428 2209 \n", "Q 931 2209 642 2551 \n", "Q 353 2894 353 3481 \n", "Q 353 4063 643 4406 \n", "Q 934 4750 1428 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5d\" d=\"M 1947 4863 \n", "L 1947 -844 \n", "L 622 -844 \n", "L 622 -397 \n", "L 1369 -397 \n", "L 1369 4416 \n", "L 622 4416 \n", "L 622 4863 \n", "L 1947 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"138.671875\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"199.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"227.734375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"320.800781\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"375.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"439.160156\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"466.943359\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"528.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"587.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"649.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"712.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"744.433594\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"783.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"878.466797\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_25\">\n", "     <path d=\"M 63.9405 34.976562 \n", "L 73.9405 34.976562 \n", "L 83.9405 34.976562 \n", "\" style=\"fill: none; stroke: #c44e52; stroke-width: 2; stroke-linecap: round\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- Collided [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(91.9405 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"186.572266\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"214.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"277.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"339.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"402.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"434.619141\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"473.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"568.652344\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 63.9405 49.654688 \n", "L 73.9405 49.654688 \n", "L 83.9405 49.654688 \n", "\" style=\"fill: none; stroke: #ffa500; stroke-width: 2; stroke-linecap: round\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- Offroad [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(91.9405 53.154688) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4f\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"113.916016\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"149.121094\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"187.984375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"249.166016\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"310.445312\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"373.921875\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"405.708984\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"444.722656\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"539.742188\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p164dd59020\">\n", "   <rect x=\"54.9405\" y=\"7.2\" width=\"279\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 500x400 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1, figsize=(5, 4))\n", "ax.plot(wallclock_time, perc_goal_achieved, label=\"Goal achieved [%]\", linewidth=2)\n", "ax.plot(wallclock_time, perc_collided, label=\"Collided [%]\", color='r', linewidth=2)\n", "ax.plot(wallclock_time, perc_offroad, label=\"Offroad [%]\", color='orange', linewidth=2)\n", "ax.grid(axis='y', linestyle='-', color='lightgrey', alpha=0.3)\n", "ax.set_xlabel(\"Wallclock time (minutes)\")\n", "ax.set_ylabel(\"Performance\")\n", "ax.set_xlim([0, 15])\n", "ax.legend(loc=\"upper left\", fontsize=10, facecolor=\"white\", framealpha=1)\n", "sns.despine()\n", "\n", "plt.savefig(f'finetuning_backwards_driving.pdf', bbox_inches='tight', format='pdf')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Log "]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [], "source": ["df_ga_wallclock = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/reward.csv\")\n", "df_rew_step = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/goal_achieved.csv\")\n", "df_offroad = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/offroad.csv\")\n", "df_collided = pd.read_csv(\"/home/<USER>/daphne/gpudrive/examples/benchmarks/dataframes/finetuning/collided.csv\")\n", "\n", "wallclock_time = df_ga_wallclock['performance/uptime'].rolling(window=WINDOW_SIZE).mean().dropna().values / 60 / 60\n", "global_step = df_rew_step['global_step'].rolling(window=WINDOW_SIZE).mean().dropna().values\n", "\n", "reward_over_time = df_rew_step['PPO__R_10000__01_23_21_02_58_770 - metrics/mean_episode_reward_per_agent'].rolling(window=WINDOW_SIZE).mean().dropna().values\n", "goal_achieved = df_ga_wallclock['PPO__R_10000__01_23_21_02_58_770 - metrics/perc_goal_achieved'].rolling(window=WINDOW_SIZE).mean().dropna().values * 100\n", "offroad = df_offroad['PPO__R_10000__01_23_21_02_58_770 - metrics/perc_off_road'].rolling(window=WINDOW_SIZE).mean().dropna().values * 100\n", "collided = df_collided['PPO__R_10000__01_23_21_02_58_770 - metrics/perc_veh_collisions'].rolling(window=WINDOW_SIZE).mean().dropna().values * 100"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"849.943462pt\" height=\"234.86877pt\" viewBox=\"0 0 849.**********.86877\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-01-31T02:15:02.759964</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 234.86877 \n", "L 849.**********.86877 \n", "L 849.943462 0 \n", "L 0 0 \n", "L 0 234.86877 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 90.**********.796098 \n", "L 277.**********.796098 \n", "L 277.745212 11.588098 \n", "L 90.969938 11.588098 \n", "L 90.**********.796098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 277.**********.796098 \n", "L 277.745212 11.588098 \n", "\" clip-path=\"url(#p05cc6b3f70)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m81d99b1d62\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m81d99b1d62\" x=\"277.745212\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{9}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(267.581212 203.072293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"m7636d51c90\" d=\"M 0 0 \n", "L 0 4 \n", "\" style=\"stroke: #262626\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"90.969937\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{3\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(70.064437 200.972293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-d7\" d=\"M 4488 3438 \n", "L 3059 2003 \n", "L 4488 575 \n", "L 4116 197 \n", "L 2681 1631 \n", "L 1247 197 \n", "L 878 575 \n", "L 2303 2003 \n", "L 878 3438 \n", "L 1247 3816 \n", "L 2681 2381 \n", "L 4116 3816 \n", "L 4488 3438 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"135.598768\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- $\\mathdefault{4\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(114.693268 200.972293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"170.215578\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"198.499572\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- $\\mathdefault{6\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(177.594072 200.972293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"222.413347\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"243.128402\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"261.400375\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- Global step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(148.630669 225.048363) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"105.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"166.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"229.931641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"291.210938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"318.994141\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"350.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.613281\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <path d=\"M 90.969938 11.588098 \n", "L 277.745212 11.588098 \n", "\" clip-path=\"url(#p05cc6b3f70)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_11\">\n", "      <defs>\n", "       <path id=\"m90a19b5cb4\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m90a19b5cb4\" x=\"90.969938\" y=\"11.588098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(61.141938 15.976195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"m24cc2c6e37\" d=\"M 0 0 \n", "L -4 0 \n", "\" style=\"stroke: #262626\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"90.969938\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{9.7\\times10^{1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.594938 189.184195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" transform=\"translate(87.660156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(170.765625 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(274.037109 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(337.660156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(402.240234 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"90.969938\" y=\"155.55924\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{9.75\\times10^{1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(24.202938 159.947337) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" transform=\"translate(87.660156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(151.283203 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(234.388672 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(337.660156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(401.283203 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(465.863281 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"90.969938\" y=\"126.471931\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{9.8\\times10^{1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(31.017438 130.860029) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(92.785156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(175.890625 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(279.162109 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(342.785156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(407.365234 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"90.969938\" y=\"97.532651\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{9.85\\times10^{1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.625438 101.920749) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(92.785156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(156.408203 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(239.513672 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(342.785156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(406.408203 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(470.988281 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"90.969938\" y=\"68.739899\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- $\\mathdefault{9.9\\times10^{1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.670938 73.127997) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(95.410156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(178.515625 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(281.787109 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(345.410156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(409.990234 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"90.969938\" y=\"40.092199\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- $\\mathdefault{9.95\\times10^{1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394438 44.480297) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(95.410156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(159.033203 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(242.138672 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(345.410156 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(409.033203 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(473.613281 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Goal achieved [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(16.774031 155.994598) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5b\" d=\"M 550 4863 \n", "L 1875 4863 \n", "L 1875 4416 \n", "L 1125 4416 \n", "L 1125 -397 \n", "L 1875 -397 \n", "L 1875 -844 \n", "L 550 -844 \n", "L 550 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-25\" d=\"M 4653 2053 \n", "Q 4381 2053 4226 1822 \n", "Q 4072 1591 4072 1178 \n", "Q 4072 772 4226 539 \n", "Q 4381 306 4653 306 \n", "Q 4919 306 5073 539 \n", "Q 5228 772 5228 1178 \n", "Q 5228 1588 5073 1820 \n", "Q 4919 2053 4653 2053 \n", "z\n", "M 4653 2450 \n", "Q 5147 2450 5437 2106 \n", "Q 5728 1763 5728 1178 \n", "Q 5728 594 5436 251 \n", "Q 5144 -91 4653 -91 \n", "Q 4153 -91 3862 251 \n", "Q 3572 594 3572 1178 \n", "Q 3572 1766 3864 2108 \n", "Q 4156 2450 4653 2450 \n", "z\n", "M 1428 4353 \n", "Q 1159 4353 1004 4120 \n", "Q 850 3888 850 3481 \n", "Q 850 3069 1003 2837 \n", "Q 1156 2606 1428 2606 \n", "Q 1700 2606 1854 2837 \n", "Q 2009 3069 2009 3481 \n", "Q 2009 3884 1853 4118 \n", "Q 1697 4353 1428 4353 \n", "z\n", "M 4250 4750 \n", "L 4750 4750 \n", "L 1831 -91 \n", "L 1331 -91 \n", "L 4250 4750 \n", "z\n", "M 1428 4750 \n", "Q 1922 4750 2215 4408 \n", "Q 2509 4066 2509 3481 \n", "Q 2509 2891 2217 2550 \n", "Q 1925 2209 1428 2209 \n", "Q 931 2209 642 2551 \n", "Q 353 2894 353 3481 \n", "Q 353 4063 643 4406 \n", "Q 934 4750 1428 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5d\" d=\"M 1947 4863 \n", "L 1947 -844 \n", "L 622 -844 \n", "L 622 -397 \n", "L 1369 -397 \n", "L 1369 4416 \n", "L 622 4416 \n", "L 622 4863 \n", "L 1947 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"138.671875\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"199.951172\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"227.734375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"259.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"320.800781\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"375.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"439.160156\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"466.943359\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"528.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"587.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"649.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"712.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"744.433594\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"783.447266\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"878.466797\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 90.872465 130.437682 \n", "L 91.137725 129.096225 \n", "L 91.402524 128.820842 \n", "L 91.798851 130.332095 \n", "L 91.93073 130.364144 \n", "L 92.193563 129.361523 \n", "L 92.454245 128.582724 \n", "L 92.584417 128.656343 \n", "L 92.843304 129.546368 \n", "L 93.101201 129.235461 \n", "L 93.358084 127.37903 \n", "L 93.48636 126.34738 \n", "L 93.741482 126.310379 \n", "L 94.250517 124.129445 \n", "L 94.505522 122.446926 \n", "L 94.632313 122.828492 \n", "L 94.758996 122.773421 \n", "L 95.518569 119.198202 \n", "L 95.773119 119.957081 \n", "L 95.90106 119.683562 \n", "L 96.028894 119.828149 \n", "L 96.15718 119.725515 \n", "L 96.413974 118.425331 \n", "L 96.800804 117.422348 \n", "L 97.058692 116.887877 \n", "L 97.187743 117.972119 \n", "L 97.316684 118.030999 \n", "L 97.831371 115.079017 \n", "L 98.088081 113.803984 \n", "L 98.216289 114.384588 \n", "L 98.344391 114.066773 \n", "L 98.600279 112.992683 \n", "L 98.983304 112.523479 \n", "L 99.11077 112.542374 \n", "L 99.365391 113.532028 \n", "L 99.619603 111.95384 \n", "L 100.126825 109.448132 \n", "L 100.37982 110.092769 \n", "L 100.758545 108.206914 \n", "L 101.010516 107.106823 \n", "L 101.136347 106.785657 \n", "L 101.262079 107.380275 \n", "L 101.387708 107.248442 \n", "L 101.763982 104.759902 \n", "L 102.139352 103.119079 \n", "L 102.263736 103.561688 \n", "L 102.388014 103.519738 \n", "L 102.760228 101.173582 \n", "L 103.007861 100.221678 \n", "L 103.13101 100.575944 \n", "L 103.254585 100.527927 \n", "L 103.378059 100.203302 \n", "L 103.747878 97.941323 \n", "L 104.117858 97.061851 \n", "L 104.241155 97.625885 \n", "L 104.364356 97.332045 \n", "L 104.733375 95.538524 \n", "L 105.101529 93.707202 \n", "L 105.224067 94.191261 \n", "L 105.346497 93.90543 \n", "L 106.07905 89.856641 \n", "L 106.200805 90.262228 \n", "L 106.322466 90.217799 \n", "L 106.808179 87.234615 \n", "L 107.050472 86.590397 \n", "L 107.171483 87.06295 \n", "L 107.29292 86.998131 \n", "L 107.782362 85.211785 \n", "L 108.517963 83.661908 \n", "L 109.12917 82.524139 \n", "L 109.49371 81.526376 \n", "L 110.818309 77.639996 \n", "L 111.057504 76.765777 \n", "L 111.176963 77.038354 \n", "L 111.774402 75.245302 \n", "L 112.251507 73.906827 \n", "L 112.370554 73.913844 \n", "L 112.489506 73.589949 \n", "L 112.608369 73.61922 \n", "L 112.964388 73.170368 \n", "L 113.082865 73.292793 \n", "L 113.31954 72.976789 \n", "L 113.673875 71.636445 \n", "L 114.027421 70.293517 \n", "L 114.145098 70.293302 \n", "L 114.60998 69.054135 \n", "L 114.952593 68.077229 \n", "L 115.065655 68.406712 \n", "L 115.401444 67.853179 \n", "L 115.842239 65.990108 \n", "L 115.95225 66.290436 \n", "L 116.501114 65.182128 \n", "L 116.720126 64.132346 \n", "L 116.938826 64.379773 \n", "L 117.375286 63.651684 \n", "L 117.593053 62.871701 \n", "L 117.81052 63.249967 \n", "L 118.136123 63.073806 \n", "L 118.244506 62.876027 \n", "L 118.461065 61.995443 \n", "L 118.677319 62.173037 \n", "L 119.216648 61.001753 \n", "L 119.324286 60.626274 \n", "L 119.539344 60.914337 \n", "L 119.754093 60.916744 \n", "L 119.861353 60.852921 \n", "L 120.182719 59.966718 \n", "L 120.396599 60.319356 \n", "L 120.823491 60.700283 \n", "L 121.036495 60.338511 \n", "L 121.354035 60.859175 \n", "L 121.565376 61.101077 \n", "L 121.670946 61.04527 \n", "L 121.881873 60.738932 \n", "L 122.092496 60.945121 \n", "L 122.197699 61.273164 \n", "L 122.302834 61.159798 \n", "L 122.407901 61.230311 \n", "L 122.722694 60.982618 \n", "L 123.141405 61.751418 \n", "L 123.2459 61.942141 \n", "L 123.350328 62.384344 \n", "L 123.764571 62.222643 \n", "L 123.866808 62.319427 \n", "L 123.968518 62.233205 \n", "L 124.170347 62.353271 \n", "L 124.270476 62.248045 \n", "L 124.469158 62.837275 \n", "L 124.567718 62.788535 \n", "L 124.666213 62.678788 \n", "L 124.861645 62.806255 \n", "L 124.958582 62.657822 \n", "L 125.151819 61.928227 \n", "L 125.342986 62.205908 \n", "L 125.719138 62.085712 \n", "L 125.904154 61.918023 \n", "L 126.087141 62.536562 \n", "L 126.17787 62.56552 \n", "L 126.357805 62.787914 \n", "L 126.537962 62.364128 \n", "L 126.627958 62.187189 \n", "L 126.898993 63.139568 \n", "L 126.990146 63.379707 \n", "L 127.358751 62.735278 \n", "L 127.451889 62.715618 \n", "L 127.639347 63.089366 \n", "L 128.018947 62.677427 \n", "L 128.208838 62.921252 \n", "L 128.398505 62.960743 \n", "L 128.587955 62.756529 \n", "L 128.777154 62.420715 \n", "L 128.966145 62.851504 \n", "L 129.249217 62.987532 \n", "L 129.437643 62.519585 \n", "L 129.531775 62.611476 \n", "L 129.719864 62.8691 \n", "L 130.095404 62.357963 \n", "L 130.18915 62.005476 \n", "L 130.470044 62.245116 \n", "L 131.030316 61.815122 \n", "L 131.309707 62.009179 \n", "L 131.588613 61.821524 \n", "L 131.774265 61.673334 \n", "L 131.959689 62.174051 \n", "L 132.144875 62.152904 \n", "L 132.237383 62.116376 \n", "L 132.514574 61.306738 \n", "L 132.883406 61.454564 \n", "L 133.251352 60.669448 \n", "L 133.526729 60.945876 \n", "L 133.893154 60.167311 \n", "L 134.076043 60.578912 \n", "L 134.349975 61.120235 \n", "L 134.441184 61.121645 \n", "L 134.532348 60.86881 \n", "L 134.714521 60.255335 \n", "L 134.897759 60.596774 \n", "L 134.989938 60.682813 \n", "L 135.268722 59.997752 \n", "L 135.645662 59.161058 \n", "L 135.836342 59.3023 \n", "L 136.125127 58.852274 \n", "L 136.415924 59.131913 \n", "L 136.611614 59.043628 \n", "L 136.710003 59.273523 \n", "L 137.611661 57.428559 \n", "L 137.813842 57.473349 \n", "L 138.217427 56.979514 \n", "L 138.318154 56.70535 \n", "L 138.418813 56.744151 \n", "L 138.518984 56.708968 \n", "L 138.816618 56.817284 \n", "L 139.10991 56.618643 \n", "L 139.303033 56.993282 \n", "L 139.398881 56.986452 \n", "L 139.683589 57.423716 \n", "L 139.964454 57.330558 \n", "L 140.057826 57.282988 \n", "L 140.244397 57.344411 \n", "L 140.430737 57.378754 \n", "L 140.709823 56.929412 \n", "L 140.895587 56.912276 \n", "L 141.451501 56.131731 \n", "L 141.90996 56.88974 \n", "L 142.001328 56.928985 \n", "L 142.183898 57.631768 \n", "L 142.275095 57.503245 \n", "L 142.91196 58.269791 \n", "L 143.362859 57.591491 \n", "L 143.630201 57.394262 \n", "L 143.806827 57.520812 \n", "L 143.89466 57.558546 \n", "L 144.157871 56.535656 \n", "L 144.333092 56.490309 \n", "L 144.595537 56.995536 \n", "L 144.857532 56.377125 \n", "L 144.944764 56.246572 \n", "L 145.206151 57.012363 \n", "L 145.293177 56.950982 \n", "L 145.467089 55.864733 \n", "L 145.553975 55.87783 \n", "L 145.64081 55.604077 \n", "L 145.901011 56.179639 \n", "L 145.987646 56.152288 \n", "L 146.160777 55.970134 \n", "L 146.4201 56.173632 \n", "L 146.679003 56.333142 \n", "L 146.851385 55.743352 \n", "L 147.023563 55.730614 \n", "L 147.367362 56.208985 \n", "L 147.710431 55.226903 \n", "L 147.96725 55.613021 \n", "L 148.05276 55.638727 \n", "L 148.223641 55.1799 \n", "L 148.394344 55.121388 \n", "L 148.735219 55.974213 \n", "L 148.820319 55.671041 \n", "L 148.905368 55.722104 \n", "L 148.990372 55.61876 \n", "L 149.417049 56.533436 \n", "L 149.676919 55.890529 \n", "L 149.76422 56.094703 \n", "L 149.851863 56.023939 \n", "L 150.116814 56.66876 \n", "L 150.295141 56.061385 \n", "L 150.474819 55.903489 \n", "L 150.654679 56.3886 \n", "L 150.834326 56.395392 \n", "L 151.013368 55.828687 \n", "L 151.102234 55.873994 \n", "L 151.190666 55.754841 \n", "L 151.540058 56.639757 \n", "L 151.882535 56.557646 \n", "L 152.051575 56.998251 \n", "L 152.13564 57.020865 \n", "L 152.3044 56.578114 \n", "L 152.472982 56.519118 \n", "L 152.641385 56.176926 \n", "L 152.809616 56.905924 \n", "L 152.977662 56.372402 \n", "L 153.145529 56.435677 \n", "L 153.313206 56.32935 \n", "L 153.48106 56.975599 \n", "L 153.649495 56.740091 \n", "L 154.073393 57.838025 \n", "L 154.158641 58.178569 \n", "L 154.244222 57.69647 \n", "L 154.41524 58.330322 \n", "L 154.671407 58.359536 \n", "L 154.756709 58.240337 \n", "L 154.841967 58.502184 \n", "L 154.927179 58.014542 \n", "L 155.097464 58.399636 \n", "L 155.182537 58.282222 \n", "L 155.352542 58.540485 \n", "L 155.437482 58.587677 \n", "L 155.522372 58.426569 \n", "L 155.60722 57.960371 \n", "L 155.692018 58.110648 \n", "L 156.199793 58.094604 \n", "L 156.284262 57.735275 \n", "L 156.368311 57.940852 \n", "L 156.535912 57.802814 \n", "L 156.867958 57.741732 \n", "L 156.950203 57.852207 \n", "L 157.114182 58.468348 \n", "L 157.195927 58.051927 \n", "L 157.277259 58.178543 \n", "L 157.520241 58.125384 \n", "L 157.600652 58.151824 \n", "L 157.920738 59.191535 \n", "L 158.080353 58.816019 \n", "L 158.478675 59.272051 \n", "L 158.558214 59.344127 \n", "L 158.795467 58.476459 \n", "L 158.873735 58.260407 \n", "L 158.951596 58.591636 \n", "L 159.029047 58.500865 \n", "L 159.182734 58.446646 \n", "L 159.485265 58.183636 \n", "L 159.634129 58.931225 \n", "L 160.076673 57.374386 \n", "L 160.150312 57.66336 \n", "L 160.297482 57.516106 \n", "L 160.517982 56.990628 \n", "L 160.664815 56.866489 \n", "L 160.885903 57.337305 \n", "L 161.109958 56.724123 \n", "L 161.489906 57.320691 \n", "L 161.72104 56.799546 \n", "L 161.798244 56.693465 \n", "L 161.951802 56.983677 \n", "L 162.255919 56.951 \n", "L 162.331404 56.775928 \n", "L 162.406851 56.258541 \n", "L 162.482261 56.455392 \n", "L 162.55764 56.474508 \n", "L 162.783574 56.236081 \n", "L 162.934034 56.342568 \n", "L 163.009214 55.852031 \n", "L 163.159453 56.249865 \n", "L 163.384539 55.90891 \n", "L 163.534403 55.573688 \n", "L 163.609277 54.938167 \n", "L 163.684114 55.19736 \n", "L 163.908404 55.089156 \n", "L 164.132357 55.510487 \n", "L 164.20694 54.961305 \n", "L 164.281135 55.287729 \n", "L 164.355296 55.246014 \n", "L 164.50352 55.268941 \n", "L 164.799535 54.548464 \n", "L 165.021183 54.876939 \n", "L 165.316254 54.701189 \n", "L 165.389939 54.370309 \n", "L 165.463591 54.50512 \n", "L 165.537556 54.486449 \n", "L 165.68572 54.637477 \n", "L 165.834443 54.614633 \n", "L 165.909275 54.622722 \n", "L 166.209661 53.990048 \n", "L 166.285013 54.37002 \n", "L 166.360683 54.280397 \n", "L 166.512271 54.377485 \n", "L 166.588185 54.299311 \n", "L 166.817119 53.778588 \n", "L 166.970022 53.701543 \n", "L 167.122781 54.273222 \n", "L 167.351634 53.958933 \n", "L 167.504016 54.048029 \n", "L 167.580146 53.971027 \n", "L 167.732649 54.544748 \n", "L 167.886387 54.497428 \n", "L 168.195472 54.221577 \n", "L 168.503934 54.308311 \n", "L 168.657931 54.236521 \n", "L 168.811425 55.036595 \n", "L 168.887939 54.732422 \n", "L 168.96407 54.777384 \n", "L 169.268197 55.05958 \n", "L 169.571721 55.739485 \n", "L 169.723257 55.671007 \n", "L 169.874648 55.727999 \n", "L 169.950283 55.728903 \n", "L 170.101434 56.067874 \n", "L 170.546795 54.915596 \n", "L 170.692572 55.022693 \n", "L 170.837546 55.033561 \n", "L 171.054093 54.861874 \n", "L 171.125759 54.929522 \n", "L 171.340884 54.105216 \n", "L 171.483569 54.521492 \n", "L 171.55435 54.467237 \n", "L 171.83546 54.450837 \n", "L 171.905061 54.511345 \n", "L 171.974626 54.922131 \n", "L 172.113336 54.598772 \n", "L 172.251252 54.560078 \n", "L 172.3877 54.343243 \n", "L 172.656203 54.882777 \n", "L 172.983985 53.607159 \n", "L 173.11342 53.936765 \n", "L 173.557212 54.119181 \n", "L 173.743581 54.510399 \n", "L 173.867024 54.066848 \n", "L 173.928702 54.126641 \n", "L 173.990349 54.120593 \n", "L 174.236698 55.095987 \n", "L 174.298555 54.786719 \n", "L 174.360389 54.857295 \n", "L 174.422199 54.828549 \n", "L 174.483983 54.933921 \n", "L 174.669176 55.623742 \n", "L 174.977313 55.160189 \n", "L 175.161895 56.237931 \n", "L 175.223364 56.05907 \n", "L 175.530369 55.225838 \n", "L 175.653007 55.40024 \n", "L 175.959157 54.551026 \n", "L 176.081439 54.733497 \n", "L 176.142548 54.839719 \n", "L 176.390033 54.450218 \n", "L 176.64236 54.404771 \n", "L 176.834772 53.472705 \n", "L 177.028584 54.109087 \n", "L 177.157873 54.269877 \n", "L 177.287056 53.902274 \n", "L 177.35161 53.60484 \n", "L 177.416139 53.736031 \n", "L 177.738367 53.875998 \n", "L 177.867075 53.537828 \n", "L 177.931391 53.590329 \n", "L 178.126146 54.281424 \n", "L 178.257477 54.336772 \n", "L 178.390006 54.357341 \n", "L 178.726478 55.578231 \n", "L 178.863124 55.226123 \n", "L 178.931892 55.346023 \n", "L 179.138664 55.949307 \n", "L 179.207954 55.924512 \n", "L 179.347085 56.057837 \n", "L 179.627564 55.364407 \n", "L 179.697926 55.404263 \n", "L 179.768253 55.72921 \n", "L 179.838872 55.676415 \n", "L 179.909458 55.599801 \n", "L 180.122935 55.247354 \n", "L 180.266696 55.088653 \n", "L 180.339003 55.185324 \n", "L 180.411599 55.112938 \n", "L 180.484481 55.158886 \n", "L 180.704515 54.777461 \n", "L 180.777579 55.130559 \n", "L 180.850928 55.0686 \n", "L 180.92424 55.053319 \n", "L 180.997517 55.16817 \n", "L 181.289965 54.856345 \n", "L 181.362756 54.837886 \n", "L 181.508551 54.33548 \n", "L 181.581239 54.297917 \n", "L 181.653893 54.039538 \n", "L 181.726195 54.292749 \n", "L 181.798145 54.263048 \n", "L 181.941007 54.006206 \n", "L 182.222554 53.047177 \n", "L 182.360619 53.296805 \n", "L 182.702907 52.026931 \n", "L 182.771274 52.073657 \n", "L 182.83961 52.192025 \n", "L 182.976196 51.917375 \n", "L 183.249013 50.211825 \n", "L 183.317146 50.224904 \n", "L 183.521345 50.660594 \n", "L 183.657325 50.245008 \n", "L 183.725271 50.295273 \n", "L 183.793186 50.24849 \n", "L 184.06459 50.717625 \n", "L 184.267867 50.314949 \n", "L 184.335565 50.19927 \n", "L 184.538488 50.874102 \n", "L 184.606072 50.852653 \n", "L 184.808657 50.412785 \n", "L 184.876131 50.174268 \n", "L 184.943571 50.273703 \n", "L 185.078366 50.454448 \n", "L 185.414854 49.48736 \n", "L 185.48207 49.573478 \n", "L 185.683528 49.968447 \n", "L 185.88475 49.308597 \n", "L 186.219549 49.677563 \n", "L 186.353248 49.245825 \n", "L 186.486819 49.099537 \n", "L 186.68694 49.622847 \n", "L 186.753586 49.562139 \n", "L 186.952733 48.740841 \n", "L 187.01865 48.661362 \n", "L 187.215003 49.178806 \n", "L 187.280193 49.207572 \n", "L 187.345053 49.366332 \n", "L 187.409584 49.230758 \n", "L 187.474092 49.197061 \n", "L 187.603024 48.750562 \n", "L 187.66745 49.013312 \n", "L 187.731849 48.901369 \n", "L 187.860565 49.012816 \n", "L 188.053452 48.500587 \n", "L 188.117687 48.554792 \n", "L 188.438486 49.215177 \n", "L 188.502566 49.233719 \n", "L 188.566621 48.889213 \n", "L 188.63065 48.908791 \n", "L 188.822876 49.157198 \n", "L 189.016672 49.248669 \n", "L 189.147046 48.89775 \n", "L 189.477472 49.693442 \n", "L 189.544138 50.178561 \n", "L 189.744866 49.641538 \n", "L 189.945345 49.750451 \n", "L 190.145564 49.661251 \n", "L 190.212245 49.868184 \n", "L 190.278896 49.786072 \n", "L 190.345516 49.752476 \n", "L 190.544294 50.434118 \n", "L 190.610195 50.207526 \n", "L 190.676071 50.339606 \n", "L 191.00504 51.41734 \n", "L 191.070751 51.816419 \n", "L 191.136435 51.673511 \n", "L 191.464457 53.046529 \n", "L 191.59547 54.001251 \n", "L 191.726373 53.671735 \n", "L 191.857158 53.938845 \n", "L 191.987826 53.90043 \n", "L 192.11838 54.365447 \n", "L 192.379171 53.639494 \n", "L 192.509407 53.520482 \n", "L 192.639534 53.922143 \n", "L 192.769543 53.67363 \n", "L 192.89945 53.688389 \n", "L 193.02926 53.462998 \n", "L 193.158955 53.824375 \n", "L 193.54734 52.923851 \n", "L 193.676579 53.430091 \n", "L 193.87024 53.061943 \n", "L 194.063654 53.387403 \n", "L 194.192478 53.979155 \n", "L 194.256854 53.717133 \n", "L 194.321201 53.793251 \n", "L 194.385525 53.883744 \n", "L 194.514094 53.661223 \n", "L 194.57834 53.715074 \n", "L 194.707052 54.251622 \n", "L 194.966205 53.552205 \n", "L 195.031439 53.540919 \n", "L 195.293541 52.931519 \n", "L 195.490472 53.324105 \n", "L 195.752658 53.411492 \n", "L 195.818134 53.101715 \n", "L 195.883585 53.237583 \n", "L 196.014414 53.026719 \n", "L 196.340998 51.807114 \n", "L 196.40623 52.071881 \n", "L 196.666879 52.522126 \n", "L 196.73198 52.493652 \n", "L 196.797054 52.604633 \n", "L 196.862103 52.242217 \n", "L 196.926836 52.459522 \n", "L 197.314662 52.070706 \n", "L 197.379205 51.549113 \n", "L 197.443435 51.605931 \n", "L 197.956324 52.29603 \n", "L 198.084285 52.220248 \n", "L 198.276038 52.679249 \n", "L 199.103069 51.23216 \n", "L 199.29129 50.404334 \n", "L 199.353601 50.293277 \n", "L 199.477579 50.712201 \n", "L 199.785666 50.060398 \n", "L 199.847213 50.121302 \n", "L 200.03169 50.856697 \n", "L 200.093133 50.64838 \n", "L 200.15455 50.679545 \n", "L 200.522537 51.243094 \n", "L 200.644997 50.870983 \n", "L 200.767353 50.910587 \n", "L 200.828497 50.699807 \n", "L 200.889616 50.864154 \n", "L 201.011792 51.01334 \n", "L 201.255851 50.555762 \n", "L 201.316802 50.494321 \n", "L 201.377726 50.751444 \n", "L 201.438625 50.698739 \n", "L 201.499502 50.619665 \n", "L 201.681152 49.947065 \n", "L 201.800734 49.762595 \n", "L 201.919113 50.353438 \n", "L 201.977852 50.286356 \n", "L 202.209812 50.574175 \n", "L 202.267053 50.339155 \n", "L 202.380633 51.017516 \n", "L 202.550432 50.563345 \n", "L 202.606993 50.731643 \n", "L 202.663532 50.638066 \n", "L 202.720055 50.58365 \n", "L 202.833036 50.931354 \n", "L 202.945932 50.462001 \n", "L 203.002348 50.492406 \n", "L 203.114566 50.430985 \n", "L 203.170643 50.336864 \n", "L 203.282735 50.789341 \n", "L 203.338746 50.720142 \n", "L 203.394738 50.716455 \n", "L 203.674395 51.396817 \n", "L 203.730268 51.322122 \n", "L 204.065101 50.162625 \n", "L 204.176829 50.476799 \n", "L 204.232803 50.448832 \n", "L 204.51236 49.666694 \n", "L 204.624305 49.992338 \n", "L 204.680661 49.812362 \n", "L 204.907788 49.428671 \n", "L 204.964588 49.567871 \n", "L 205.021635 49.516333 \n", "L 205.078662 49.481403 \n", "L 205.25044 49.683713 \n", "L 205.366212 49.034176 \n", "L 205.482711 49.47785 \n", "L 205.715429 50.168737 \n", "L 205.889746 49.591639 \n", "L 206.064409 49.866938 \n", "L 206.122947 49.802369 \n", "L 206.181464 49.861916 \n", "L 206.357967 49.080649 \n", "L 206.535901 48.808176 \n", "L 206.654778 49.216402 \n", "L 206.892251 48.557067 \n", "L 207.129374 48.424674 \n", "L 207.306987 48.040528 \n", "L 207.366148 48.029239 \n", "L 207.602578 49.087106 \n", "L 207.720394 48.898171 \n", "L 207.837058 48.968464 \n", "L 207.894957 49.113289 \n", "L 207.952566 48.895679 \n", "L 208.009886 49.091295 \n", "L 208.066918 49.186837 \n", "L 208.123662 49.147209 \n", "L 208.180118 49.078342 \n", "L 208.347758 49.17486 \n", "L 208.403064 49.21451 \n", "L 208.458081 49.43852 \n", "L 208.568861 49.053134 \n", "L 208.624626 49.079268 \n", "L 208.736893 48.949192 \n", "L 208.964369 48.197572 \n", "L 209.137548 47.600952 \n", "L 209.254225 47.180692 \n", "L 209.371077 47.517884 \n", "L 209.545646 46.97282 \n", "L 209.603435 47.004746 \n", "L 209.718945 46.874263 \n", "L 209.776407 47.080949 \n", "L 209.83385 47.04583 \n", "L 209.948668 46.958607 \n", "L 210.178046 46.314261 \n", "L 210.292618 46.535339 \n", "L 210.349875 46.49351 \n", "L 210.40711 46.401072 \n", "L 210.578164 45.925867 \n", "L 210.860286 46.491234 \n", "L 210.972502 46.541465 \n", "L 211.08437 46.163468 \n", "L 211.140272 46.249859 \n", "L 211.419516 46.511029 \n", "L 211.531079 46.200825 \n", "L 211.866839 46.917709 \n", "L 211.923255 46.63478 \n", "L 211.979913 46.671884 \n", "L 212.150545 46.834738 \n", "L 212.320986 47.473053 \n", "L 212.604644 46.422969 \n", "L 212.717969 46.684716 \n", "L 213.055909 46.358672 \n", "L 213.277947 47.119978 \n", "L 213.333084 47.360395 \n", "L 213.388197 47.309156 \n", "L 213.498359 47.10091 \n", "L 213.718446 47.939244 \n", "L 213.883283 48.174112 \n", "L 213.938182 48.123527 \n", "L 214.047926 48.521814 \n", "L 214.102765 48.508573 \n", "L 214.157585 48.464686 \n", "L 214.376679 47.399237 \n", "L 214.486368 47.625744 \n", "L 214.541564 47.677762 \n", "L 214.596994 47.592802 \n", "L 214.652659 47.386145 \n", "L 214.708561 47.490259 \n", "L 214.821069 47.388125 \n", "L 214.934515 47.595364 \n", "L 214.991589 47.480099 \n", "L 215.106442 47.702594 \n", "L 215.222231 47.590925 \n", "L 215.39613 47.95793 \n", "L 215.569824 47.926413 \n", "L 215.685516 47.550873 \n", "L 215.74333 47.744127 \n", "L 216.032047 48.169311 \n", "L 216.14738 47.887917 \n", "L 216.320227 48.258956 \n", "L 216.607646 48.567345 \n", "L 216.665115 48.308661 \n", "L 216.722563 48.382787 \n", "L 216.893264 49.019557 \n", "L 216.949616 48.957214 \n", "L 217.005696 48.99095 \n", "L 217.336937 49.736915 \n", "L 217.446016 49.900566 \n", "L 217.609113 49.486461 \n", "L 217.662939 49.49099 \n", "L 217.716744 49.364273 \n", "L 217.77028 49.591893 \n", "L 217.823546 49.516737 \n", "L 217.876541 49.467555 \n", "L 218.033901 49.651023 \n", "L 218.291057 49.319013 \n", "L 218.546307 48.724691 \n", "L 218.648287 48.82659 \n", "L 218.85205 47.845815 \n", "L 218.953841 47.586839 \n", "L 219.004713 47.820371 \n", "L 219.05557 47.769352 \n", "L 219.208036 47.436283 \n", "L 219.309594 47.400265 \n", "L 219.46205 47.819231 \n", "L 219.513003 47.685402 \n", "L 219.925214 46.836441 \n", "L 219.97701 46.926278 \n", "L 220.028791 46.927182 \n", "L 220.080555 46.801604 \n", "L 220.132301 46.941961 \n", "L 220.235746 46.951463 \n", "L 220.287446 46.931022 \n", "L 220.390799 46.581567 \n", "L 220.442449 46.6229 \n", "L 220.751998 46.161628 \n", "L 220.906537 46.229624 \n", "L 221.112364 46.627738 \n", "L 221.215179 46.401553 \n", "L 221.266564 46.552953 \n", "L 221.317934 46.365867 \n", "L 221.523239 46.08609 \n", "L 221.574522 45.927761 \n", "L 221.625786 45.993487 \n", "L 221.830678 46.332481 \n", "L 221.933027 46.623403 \n", "L 222.086177 46.438418 \n", "L 222.237718 46.365015 \n", "L 222.338338 46.147601 \n", "L 222.438894 46.34692 \n", "L 222.489146 46.191408 \n", "L 222.539624 46.23835 \n", "L 222.640772 46.24376 \n", "L 222.844086 46.034051 \n", "L 222.894936 46.159242 \n", "L 222.946011 46.001801 \n", "L 223.100597 45.69711 \n", "L 223.152578 45.812316 \n", "L 223.204784 45.876537 \n", "L 223.415861 45.204755 \n", "L 223.63055 44.817164 \n", "L 223.684543 44.957443 \n", "L 223.73876 44.807107 \n", "L 224.068744 45.429481 \n", "L 224.236722 45.130966 \n", "L 224.462091 45.490687 \n", "L 224.629624 46.183213 \n", "L 224.684947 46.181234 \n", "L 224.794816 46.596137 \n", "L 224.904366 46.307082 \n", "L 225.013827 46.739686 \n", "L 225.068525 46.690739 \n", "L 225.17786 46.777818 \n", "L 225.341727 46.326069 \n", "L 225.396311 46.548818 \n", "L 225.668954 46.743789 \n", "L 225.777877 46.346416 \n", "L 225.941122 46.630937 \n", "L 226.104188 46.873375 \n", "L 226.212803 46.261308 \n", "L 226.267082 46.411007 \n", "L 226.321346 46.416616 \n", "L 226.646285 45.473438 \n", "L 226.699942 45.716826 \n", "L 226.912016 45.921339 \n", "L 226.964394 45.756238 \n", "L 227.01652 45.810071 \n", "L 227.068394 45.874227 \n", "L 227.273344 46.729875 \n", "L 227.374535 46.430241 \n", "L 227.47566 46.179359 \n", "L 227.526197 46.278211 \n", "L 227.677696 46.449176 \n", "L 227.728166 46.407748 \n", "L 227.829056 46.112298 \n", "L 227.929889 46.486086 \n", "L 227.980286 46.443872 \n", "L 228.131382 46.056063 \n", "L 228.282322 44.998766 \n", "L 228.534703 45.700269 \n", "L 228.687337 45.344623 \n", "L 228.738182 45.26353 \n", "L 228.890627 45.601469 \n", "L 228.941406 45.557184 \n", "L 229.144355 44.865029 \n", "L 229.347042 45.28192 \n", "L 229.548525 44.762473 \n", "L 229.598622 44.790924 \n", "L 229.796971 45.157674 \n", "L 229.895341 45.214557 \n", "L 229.993406 44.918055 \n", "L 230.042416 45.039769 \n", "L 230.091415 45.140614 \n", "L 230.140396 45.061242 \n", "L 230.238317 45.195315 \n", "L 230.287254 45.240664 \n", "L 230.336176 44.93711 \n", "L 230.385084 45.015538 \n", "L 230.482854 45.141407 \n", "L 230.531949 45.104167 \n", "L 230.58103 45.183321 \n", "L 230.98144 44.015266 \n", "L 231.08299 44.286427 \n", "L 231.336602 43.535445 \n", "L 231.387276 43.430451 \n", "L 231.488567 43.65713 \n", "L 231.741485 43.063225 \n", "L 231.792019 42.95844 \n", "L 231.893041 43.176278 \n", "L 232.145307 42.57682 \n", "L 232.195717 42.514312 \n", "L 232.29649 42.896013 \n", "L 232.346849 42.731621 \n", "L 232.496902 42.775099 \n", "L 232.595863 42.645691 \n", "L 232.841276 43.399131 \n", "L 232.988344 43.663104 \n", "L 233.283427 42.395115 \n", "L 233.332552 42.383596 \n", "L 233.526623 43.15829 \n", "L 233.574536 43.1431 \n", "L 233.716825 43.520681 \n", "L 233.764003 43.49348 \n", "L 234.046738 43.332375 \n", "L 234.093807 43.403773 \n", "L 234.234939 43.489904 \n", "L 234.375946 43.557634 \n", "L 234.610003 43.254818 \n", "L 234.656232 43.364318 \n", "L 234.702218 43.468727 \n", "L 234.793925 43.409191 \n", "L 234.839424 43.302879 \n", "L 235.107415 44.410879 \n", "L 235.195292 44.074768 \n", "L 235.238875 44.179887 \n", "L 235.282444 44.182463 \n", "L 235.41218 44.478958 \n", "L 235.455249 44.395388 \n", "L 235.498305 44.381871 \n", "L 235.541129 44.568541 \n", "L 235.626514 44.479625 \n", "L 235.669077 44.459894 \n", "L 235.753499 44.996382 \n", "L 235.795581 44.856512 \n", "L 235.963776 44.399287 \n", "L 236.047814 44.646089 \n", "L 236.089817 44.764207 \n", "L 236.257705 44.313667 \n", "L 236.341579 44.356383 \n", "L 236.425406 44.569179 \n", "L 236.551062 44.144986 \n", "L 236.592926 44.170146 \n", "L 236.676625 44.186433 \n", "L 236.760284 44.620435 \n", "L 236.802093 44.462518 \n", "L 236.969206 44.48836 \n", "L 237.010957 44.490879 \n", "L 237.094421 44.824233 \n", "L 237.136134 44.663322 \n", "L 237.219525 44.531361 \n", "L 237.261205 44.570606 \n", "L 237.344528 44.669889 \n", "L 237.427807 44.948049 \n", "L 237.594224 44.515536 \n", "L 237.677355 44.427541 \n", "L 237.718903 44.688775 \n", "L 237.802407 44.517433 \n", "L 238.014291 44.248336 \n", "L 238.275424 43.122815 \n", "L 238.319451 43.367013 \n", "L 238.452781 43.504515 \n", "L 238.679137 42.851648 \n", "L 238.91068 41.947894 \n", "L 238.957603 42.064333 \n", "L 239.192004 43.032994 \n", "L 239.332456 42.670363 \n", "L 239.426014 42.934921 \n", "L 239.472774 42.873059 \n", "L 239.612975 42.556569 \n", "L 239.70638 42.32977 \n", "L 239.847044 42.879032 \n", "L 239.89434 42.792861 \n", "L 239.941843 42.720252 \n", "L 240.037022 42.38099 \n", "L 240.084916 42.536397 \n", "L 240.133015 42.545353 \n", "L 240.181315 42.424916 \n", "L 240.229818 42.527599 \n", "L 240.327434 42.633745 \n", "L 240.376544 42.177887 \n", "L 240.425421 42.394701 \n", "L 240.474283 42.344902 \n", "L 240.571962 42.555311 \n", "L 240.620777 42.467244 \n", "L 240.864628 42.272967 \n", "L 240.913353 42.344394 \n", "L 241.108319 41.784331 \n", "L 241.15697 41.877669 \n", "L 241.444033 42.040483 \n", "L 241.491066 41.94668 \n", "L 241.676912 42.430217 \n", "L 241.722802 42.396116 \n", "L 241.859746 42.16966 \n", "L 241.996565 42.454951 \n", "L 242.133254 42.44864 \n", "L 242.224312 42.054218 \n", "L 242.497186 43.160773 \n", "L 242.588047 42.843201 \n", "L 242.814984 43.530058 \n", "L 242.860333 43.552055 \n", "L 242.905671 43.344281 \n", "L 242.950997 43.427453 \n", "L 243.041607 43.652918 \n", "L 243.086892 43.635221 \n", "L 243.222678 43.766781 \n", "L 243.267916 43.513345 \n", "L 243.312931 43.527576 \n", "L 243.446616 43.700741 \n", "L 243.664905 43.533747 \n", "L 243.877568 44.097721 \n", "L 243.919638 43.896477 \n", "L 244.003744 44.022667 \n", "L 244.213811 44.889938 \n", "L 244.339718 44.632938 \n", "L 244.381665 44.861338 \n", "L 244.423599 44.773115 \n", "L 244.674975 43.455805 \n", "L 244.758691 43.674178 \n", "L 244.842368 43.883744 \n", "L 244.884189 43.758229 \n", "L 244.967795 43.535941 \n", "L 245.009582 43.574581 \n", "L 245.176596 43.980909 \n", "L 245.218316 44.064318 \n", "L 245.34341 43.533765 \n", "L 245.468401 43.873521 \n", "L 245.677124 43.095021 \n", "L 245.760917 43.250129 \n", "L 245.845505 43.395721 \n", "L 246.015818 42.636166 \n", "L 246.058369 42.692667 \n", "L 246.100906 42.950164 \n", "L 246.185945 42.827435 \n", "L 246.397916 43.264472 \n", "L 246.524567 43.489705 \n", "L 246.651952 43.062777 \n", "L 246.779865 43.156519 \n", "L 246.951821 43.195508 \n", "L 246.994725 43.478662 \n", "L 247.037616 43.310705 \n", "L 247.251897 43.04633 \n", "L 247.294718 43.078897 \n", "L 247.337529 43.34136 \n", "L 247.380326 43.156245 \n", "L 247.592878 42.684214 \n", "L 247.635189 42.432341 \n", "L 247.677488 42.618278 \n", "L 247.71957 42.765971 \n", "L 247.761643 42.598047 \n", "L 247.971812 41.993955 \n", "L 248.01381 42.233158 \n", "L 248.055794 42.35504 \n", "L 248.09777 42.207914 \n", "L 248.181482 42.119074 \n", "L 248.223216 42.192525 \n", "L 248.347725 42.40905 \n", "L 248.555628 42.142998 \n", "L 248.59767 42.173479 \n", "L 248.639905 42.113982 \n", "L 248.941025 41.43266 \n", "L 248.984617 41.588249 \n", "L 249.028197 41.511679 \n", "L 249.28921 40.521511 \n", "L 249.33229 40.645483 \n", "L 249.375151 40.666398 \n", "L 249.502433 40.819303 \n", "L 249.669117 41.029267 \n", "L 249.832354 41.252726 \n", "L 249.913299 40.830501 \n", "L 249.953755 40.863898 \n", "L 249.994203 41.039568 \n", "L 250.034644 40.921912 \n", "L 250.277069 40.468015 \n", "L 250.478784 41.112138 \n", "L 250.599668 40.526639 \n", "L 250.639734 40.620675 \n", "L 250.87991 40.081915 \n", "L 250.919901 40.198253 \n", "L 251.041026 40.466556 \n", "L 251.163883 39.967746 \n", "L 251.288457 39.75225 \n", "L 251.457221 40.006059 \n", "L 251.542753 39.421171 \n", "L 251.628644 39.443612 \n", "L 251.801688 39.238977 \n", "L 252.018091 38.421692 \n", "L 252.230584 38.912595 \n", "L 252.355516 38.617744 \n", "L 252.396737 38.71679 \n", "L 252.437947 38.758592 \n", "L 252.561519 38.551715 \n", "L 252.727117 38.520999 \n", "L 252.851442 38.733189 \n", "L 253.01453 38.554533 \n", "L 253.054774 38.407279 \n", "L 253.134635 38.455762 \n", "L 253.174447 38.635714 \n", "L 253.214051 38.570221 \n", "L 253.450279 37.876308 \n", "L 253.489814 37.893939 \n", "L 253.687332 38.403613 \n", "L 253.805721 38.109359 \n", "L 253.963438 38.431292 \n", "L 254.002841 38.507288 \n", "L 254.042232 38.301527 \n", "L 254.081615 38.399428 \n", "L 254.319694 39.348756 \n", "L 254.360002 39.145558 \n", "L 254.400496 39.283405 \n", "L 254.647406 39.63042 \n", "L 254.7312 39.366691 \n", "L 254.773179 39.479091 \n", "L 254.815149 39.58743 \n", "L 254.857109 39.513245 \n", "L 254.899058 39.450426 \n", "L 254.98292 39.73677 \n", "L 255.024835 39.570634 \n", "L 255.066542 39.268897 \n", "L 255.108043 39.557497 \n", "L 255.19042 39.481687 \n", "L 255.392725 39.167999 \n", "L 255.550845 39.779936 \n", "L 255.589858 39.761926 \n", "L 255.628862 39.621505 \n", "L 255.667855 39.725005 \n", "L 255.74581 40.041756 \n", "L 255.901604 40.700818 \n", "L 255.940526 40.469127 \n", "L 256.018343 40.343829 \n", "L 256.096131 40.501547 \n", "L 256.290465 39.63051 \n", "L 256.368138 39.815328 \n", "L 256.523365 40.426767 \n", "L 256.63969 40.165478 \n", "L 256.835314 40.786053 \n", "L 256.914859 40.542437 \n", "L 256.954911 40.702586 \n", "L 257.035567 40.738856 \n", "L 257.157938 41.359128 \n", "L 257.199095 41.25538 \n", "L 257.240433 41.137295 \n", "L 257.364383 41.455217 \n", "L 257.488226 41.561472 \n", "L 257.570735 41.178298 \n", "L 257.61197 41.363223 \n", "L 257.736765 41.286233 \n", "L 257.820874 41.404632 \n", "L 257.990107 40.983481 \n", "L 258.074652 40.878231 \n", "L 258.116907 40.978082 \n", "L 258.285232 41.226864 \n", "L 258.53247 40.724093 \n", "L 258.614405 40.929842 \n", "L 258.778131 41.075902 \n", "L 258.859928 40.835321 \n", "L 258.982553 41.197341 \n", "L 259.023405 41.134024 \n", "L 259.105073 41.32795 \n", "L 259.186692 40.755889 \n", "L 259.268268 40.852058 \n", "L 259.47203 40.397087 \n", "L 259.512562 40.425342 \n", "L 259.751485 40.933045 \n", "L 259.790593 40.905674 \n", "L 259.984272 41.048651 \n", "L 260.022939 41.010645 \n", "L 260.140029 40.746804 \n", "L 260.179425 40.849636 \n", "L 260.219002 40.8328 \n", "L 260.25876 40.715662 \n", "L 260.298698 40.800176 \n", "L 260.338817 40.857451 \n", "L 260.379117 40.612314 \n", "L 260.459488 40.782725 \n", "L 260.734476 39.77073 \n", "L 260.811241 39.931447 \n", "L 260.924877 39.893639 \n", "L 261.03728 39.607523 \n", "L 261.184343 39.846967 \n", "L 261.328214 39.694593 \n", "L 261.434028 40.150829 \n", "L 261.468908 40.138185 \n", "L 261.608342 39.602505 \n", "L 261.678013 39.826049 \n", "L 261.712834 39.623575 \n", "L 261.747649 39.578207 \n", "L 261.885693 38.747205 \n", "L 261.919714 39.000055 \n", "L 261.95354 39.02207 \n", "L 261.98717 38.935732 \n", "L 262.152383 38.826761 \n", "L 262.24917 39.360148 \n", "L 262.281232 39.191231 \n", "L 262.313287 39.176508 \n", "L 262.50606 40.043828 \n", "L 262.571392 39.828883 \n", "L 262.771736 40.106748 \n", "L 262.839779 40.016575 \n", "L 262.873788 39.972677 \n", "L 262.907791 40.055352 \n", "L 263.07487 40.394137 \n", "L 263.17278 40.51075 \n", "L 263.237079 40.976468 \n", "L 263.268935 40.895591 \n", "L 263.428125 40.353902 \n", "L 263.523568 40.991187 \n", "L 263.586976 40.869259 \n", "L 263.618386 40.680977 \n", "L 263.680623 40.976408 \n", "L 263.803906 41.363524 \n", "L 263.834713 41.348919 \n", "L 263.927097 41.424679 \n", "L 263.957881 41.379954 \n", "L 264.082062 41.310305 \n", "L 264.113555 41.361824 \n", "L 264.240213 41.69394 \n", "L 264.398384 41.20682 \n", "L 264.493777 41.429722 \n", "L 264.525934 41.278052 \n", "L 264.558271 41.209581 \n", "L 264.590788 41.247169 \n", "L 264.756056 41.64458 \n", "L 264.857357 41.53286 \n", "L 265.029014 42.361241 \n", "L 265.098662 42.251092 \n", "L 265.411712 41.467966 \n", "L 265.480643 41.724443 \n", "L 265.51482 41.610689 \n", "L 265.616755 41.219564 \n", "L 265.684681 41.300445 \n", "L 265.787072 41.675296 \n", "L 265.821558 41.582698 \n", "L 265.961261 41.253373 \n", "L 266.139852 40.72231 \n", "L 266.212523 40.415304 \n", "L 266.24894 40.566409 \n", "L 266.35814 40.373537 \n", "L 266.467261 39.844624 \n", "L 266.503617 39.907161 \n", "L 266.685267 40.689198 \n", "L 266.793602 40.342751 \n", "L 266.972153 41.009455 \n", "L 267.221775 41.652089 \n", "L 267.293574 41.247784 \n", "L 267.329737 41.337062 \n", "L 267.438176 41.667713 \n", "L 267.474305 41.649599 \n", "L 267.510423 41.611465 \n", "L 267.618729 41.209384 \n", "L 267.654814 41.431399 \n", "L 267.727504 41.386356 \n", "L 268.139867 40.179285 \n", "L 268.178553 40.195674 \n", "L 268.294558 40.510935 \n", "L 268.333211 40.450837 \n", "L 268.371857 40.437369 \n", "L 268.48774 40.197753 \n", "L 268.641397 40.718126 \n", "L 268.717984 40.928455 \n", "L 268.756174 40.883727 \n", "L 268.794354 40.810804 \n", "L 268.832526 40.928362 \n", "L 269.023231 41.337795 \n", "L 269.099452 41.219089 \n", "L 269.289844 41.838533 \n", "L 269.365933 41.840758 \n", "L 269.403963 41.665319 \n", "L 269.441988 41.825638 \n", "L 269.595796 42.081056 \n", "L 269.634675 42.039053 \n", "L 269.752328 41.815058 \n", "L 270.031326 42.620082 \n", "L 270.189546 42.405227 \n", "L 270.306214 42.704827 \n", "L 270.53447 44.013386 \n", "L 270.943434 44.904352 \n", "L 271.465986 44.120634 \n", "L 271.50422 44.286537 \n", "L 271.542622 44.009675 \n", "L 271.658847 43.76622 \n", "L 271.658847 43.76622 \n", "\" clip-path=\"url(#p05cc6b3f70)\" style=\"fill: none; stroke: #4c72b0; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 90.**********.796098 \n", "L 90.969938 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 90.969937 184.796098 \n", "L 277.**********.796098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 368.387063 184.796098 \n", "L 555.162337 184.796098 \n", "L 555.162337 11.588098 \n", "L 368.387063 11.588098 \n", "L 368.387063 184.796098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 555.162337 184.796098 \n", "L 555.162337 11.588098 \n", "\" clip-path=\"url(#pa3fd31ed85)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m81d99b1d62\" x=\"555.162337\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- $\\mathdefault{10^{9}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(544.998337 203.072293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"368.387062\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- $\\mathdefault{3\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(347.481562 200.972293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"413.015893\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- $\\mathdefault{4\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(392.110393 200.972293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"447.632703\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"475.916697\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- $\\mathdefault{6\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(455.011197 200.972293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"499.830472\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"520.545527\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"538.8175\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- Global step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(426.047794 225.048363) scale(0.126 -0.126)\">\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"105.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"166.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"229.931641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"291.210938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"318.994141\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"350.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.613281\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"368.387063\" y=\"132.503206\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- $\\mathdefault{2\\times10^{0}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(319.176063 136.891304) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"368.387063\" y=\"79.602471\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- $\\mathdefault{3\\times10^{0}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(319.176063 83.990569) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"368.387063\" y=\"42.068802\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- $\\mathdefault{4\\times10^{0}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(319.176063 46.4569) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"368.387063\" y=\"12.955426\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- Off-road [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(312.555656 136.580754) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4f\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"113.916016\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"143.621094\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"179.705078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"218.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"279.75\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"341.029297\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"404.505859\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"436.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"475.306641\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"570.326172\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_32\">\n", "    <path d=\"M 368.28959 115.717251 \n", "L 368.55485 116.791127 \n", "L 368.819649 117.216438 \n", "L 368.95188 117.174971 \n", "L 369.215976 116.657373 \n", "L 369.47961 116.487315 \n", "L 369.610688 116.326003 \n", "L 369.87137 116.489848 \n", "L 370.001542 115.709224 \n", "L 370.131036 115.820539 \n", "L 370.260429 115.442833 \n", "L 370.518326 115.737776 \n", "L 370.646824 115.553725 \n", "L 370.903485 116.305559 \n", "L 371.031096 116.064196 \n", "L 371.158607 116.119648 \n", "L 371.286023 116.030493 \n", "L 371.540536 116.678316 \n", "L 371.667642 116.70713 \n", "L 371.795196 117.282243 \n", "L 371.922647 117.199362 \n", "L 372.176121 116.755875 \n", "L 372.302694 116.743217 \n", "L 372.555542 116.394869 \n", "L 372.682367 116.306021 \n", "L 372.809084 115.884482 \n", "L 373.318185 112.605772 \n", "L 373.574305 111.442399 \n", "L 373.831099 111.793708 \n", "L 374.08855 112.051477 \n", "L 374.346652 112.732992 \n", "L 374.475817 112.908759 \n", "L 374.604868 112.551248 \n", "L 374.733809 112.774264 \n", "L 375.119982 114.138229 \n", "L 375.248496 114.151396 \n", "L 375.505206 114.552052 \n", "L 375.761516 114.203765 \n", "L 376.017404 113.881471 \n", "L 376.145189 114.212092 \n", "L 376.272863 114.199828 \n", "L 376.527895 113.885975 \n", "L 376.782516 113.859786 \n", "L 377.163683 114.37029 \n", "L 377.4173 114.463347 \n", "L 377.54395 114.374351 \n", "L 377.670496 114.030725 \n", "L 378.049532 114.351331 \n", "L 378.427641 113.811266 \n", "L 378.553472 113.580956 \n", "L 378.679204 113.030376 \n", "L 378.930361 112.977517 \n", "L 379.055784 112.669362 \n", "L 379.306331 112.799956 \n", "L 379.431454 112.805651 \n", "L 379.556477 113.179707 \n", "L 379.805139 112.445331 \n", "L 379.929315 112.451545 \n", "L 380.177353 112.008334 \n", "L 380.424986 112.127502 \n", "L 380.795184 110.906829 \n", "L 380.918556 111.142147 \n", "L 381.041824 110.954437 \n", "L 381.781481 108.765957 \n", "L 382.027592 109.633174 \n", "L 382.1505 109.594037 \n", "L 382.396028 110.058344 \n", "L 382.518654 110.372784 \n", "L 382.641192 110.331595 \n", "L 382.885957 110.689783 \n", "L 383.130328 110.716851 \n", "L 383.374323 111.166425 \n", "L 383.496175 110.997828 \n", "L 383.739591 109.927004 \n", "L 383.982637 110.082268 \n", "L 384.104017 109.624371 \n", "L 384.346499 109.606359 \n", "L 384.954696 109.001438 \n", "L 385.199487 109.359632 \n", "L 385.321991 109.42123 \n", "L 385.567203 109.845866 \n", "L 385.689923 110.263152 \n", "L 385.935088 109.75697 \n", "L 386.05753 109.71398 \n", "L 386.179869 109.539182 \n", "L 386.302106 109.827764 \n", "L 386.546295 109.378306 \n", "L 386.668241 109.032211 \n", "L 386.910835 108.045201 \n", "L 387.031993 107.909187 \n", "L 387.394895 108.135462 \n", "L 387.515672 108.123906 \n", "L 387.755952 108.312334 \n", "L 387.995876 108.428954 \n", "L 388.115699 108.697036 \n", "L 388.235434 108.616309 \n", "L 388.474629 108.978729 \n", "L 388.713953 108.30648 \n", "L 388.83373 108.177988 \n", "L 389.430267 106.287917 \n", "L 389.549493 106.148398 \n", "L 390.025494 103.787342 \n", "L 390.49999 102.699897 \n", "L 390.736665 103.169762 \n", "L 390.854866 103.107852 \n", "L 391.091 103.469369 \n", "L 391.562223 104.608803 \n", "L 392.256085 106.30359 \n", "L 392.369718 106.189046 \n", "L 392.48278 106.133627 \n", "L 392.707208 106.391142 \n", "L 393.039606 107.275447 \n", "L 393.80862 109.371681 \n", "L 393.918239 109.353852 \n", "L 394.683409 112.294757 \n", "L 394.901331 113.436402 \n", "L 395.010178 114.041193 \n", "L 395.11895 113.957894 \n", "L 396.633773 119.174863 \n", "L 396.741411 118.859009 \n", "L 397.063881 118.979162 \n", "L 397.278478 118.876172 \n", "L 397.599844 119.960908 \n", "L 397.706818 119.852863 \n", "L 397.813724 119.987126 \n", "L 398.027319 120.374238 \n", "L 398.134003 120.437416 \n", "L 398.45362 120.928931 \n", "L 399.193569 121.795512 \n", "L 399.404347 121.500735 \n", "L 399.614824 121.456489 \n", "L 399.719959 121.660417 \n", "L 399.930026 121.34319 \n", "L 400.767453 125.147469 \n", "L 400.871813 125.987015 \n", "L 400.97564 125.914225 \n", "L 401.486821 128.925914 \n", "L 401.687601 129.953328 \n", "L 401.787203 129.834778 \n", "L 403.042992 136.202558 \n", "L 403.321279 137.07255 \n", "L 403.504266 136.846646 \n", "L 403.685219 137.036693 \n", "L 403.77493 136.955262 \n", "L 403.955087 137.319678 \n", "L 404.045083 137.22386 \n", "L 404.135025 136.700517 \n", "L 404.225371 136.808375 \n", "L 404.407271 136.627408 \n", "L 404.498826 137.042443 \n", "L 404.683131 136.202798 \n", "L 404.775876 136.261727 \n", "L 404.962547 136.094763 \n", "L 405.056472 136.357028 \n", "L 405.150788 136.301605 \n", "L 405.625963 136.182079 \n", "L 405.81563 135.951058 \n", "L 405.910383 136.054711 \n", "L 406.00508 136.532319 \n", "L 406.194279 135.808255 \n", "L 406.38327 135.2838 \n", "L 406.477681 135.424044 \n", "L 406.666342 134.833931 \n", "L 406.760585 135.303822 \n", "L 406.854768 135.25522 \n", "L 407.042973 134.700569 \n", "L 407.136989 134.896382 \n", "L 407.324865 134.749614 \n", "L 407.418727 134.840765 \n", "L 407.606275 135.410329 \n", "L 407.887169 134.744776 \n", "L 408.167551 133.98129 \n", "L 408.354198 133.703424 \n", "L 408.447441 132.982278 \n", "L 408.540631 133.337115 \n", "L 408.633757 133.242258 \n", "L 409.005738 134.598506 \n", "L 409.376814 132.945457 \n", "L 409.562 132.521513 \n", "L 409.931699 132.848855 \n", "L 410.300531 133.658196 \n", "L 410.576574 134.445426 \n", "L 410.760322 134.211172 \n", "L 411.127177 134.394124 \n", "L 411.310279 135.171421 \n", "L 411.401751 135.041884 \n", "L 411.493168 135.119538 \n", "L 412.040587 133.73163 \n", "L 412.314884 132.226549 \n", "L 412.499614 132.13126 \n", "L 412.592543 132.07213 \n", "L 412.779528 132.560703 \n", "L 412.873577 132.676223 \n", "L 413.062787 133.257478 \n", "L 413.157943 133.211138 \n", "L 413.349364 133.012355 \n", "L 413.445625 133.352531 \n", "L 413.930712 131.599199 \n", "L 414.028739 131.607806 \n", "L 414.127128 131.0595 \n", "L 414.725019 132.064876 \n", "L 414.82592 131.953533 \n", "L 414.927175 132.066576 \n", "L 415.028786 132.473181 \n", "L 415.331956 131.464257 \n", "L 415.432886 131.589869 \n", "L 415.533753 131.334688 \n", "L 415.634552 131.410784 \n", "L 416.816006 128.194976 \n", "L 416.911384 128.235172 \n", "L 417.006284 128.350318 \n", "L 417.194671 128.177994 \n", "L 417.381579 128.329229 \n", "L 417.661522 129.825624 \n", "L 418.126948 130.726518 \n", "L 418.21986 130.724171 \n", "L 418.498244 130.24219 \n", "L 418.683554 130.362465 \n", "L 418.776117 130.339028 \n", "L 418.96067 129.887415 \n", "L 419.052661 129.906199 \n", "L 419.418453 130.718939 \n", "L 419.601023 130.399909 \n", "L 419.965487 131.733291 \n", "L 420.056469 131.471971 \n", "L 420.329085 131.953495 \n", "L 420.600422 133.637511 \n", "L 420.869284 134.496814 \n", "L 420.958533 134.5555 \n", "L 421.135663 134.235507 \n", "L 421.223952 134.216629 \n", "L 421.399571 134.430523 \n", "L 421.574996 133.936765 \n", "L 421.662631 134.040598 \n", "L 421.837748 133.764573 \n", "L 422.187379 134.931591 \n", "L 422.274657 134.89838 \n", "L 422.710302 134.158031 \n", "L 423.057935 136.036373 \n", "L 423.144719 136.026261 \n", "L 423.664392 138.947961 \n", "L 423.750832 138.899888 \n", "L 424.096128 137.799346 \n", "L 424.354626 138.679116 \n", "L 424.440688 138.838327 \n", "L 424.526705 138.539586 \n", "L 424.784487 140.218312 \n", "L 425.041854 141.777835 \n", "L 425.127556 142.119678 \n", "L 425.213214 142.040492 \n", "L 425.29882 142.093555 \n", "L 425.469885 141.629903 \n", "L 425.640766 142.308452 \n", "L 425.726141 142.487875 \n", "L 425.811469 142.399453 \n", "L 425.896753 142.113732 \n", "L 426.152344 143.160998 \n", "L 426.237444 143.109081 \n", "L 426.577369 142.124707 \n", "L 426.66263 142.083514 \n", "L 427.445282 144.767837 \n", "L 427.533939 144.681956 \n", "L 427.622934 144.932445 \n", "L 427.801936 144.279143 \n", "L 428.251451 140.96656 \n", "L 428.341191 141.178112 \n", "L 428.870482 140.047236 \n", "L 428.957183 140.271139 \n", "L 429.04345 140.23524 \n", "L 429.214684 139.844232 \n", "L 429.29966 139.995324 \n", "L 429.384205 139.86401 \n", "L 429.4687 140.067731 \n", "L 429.552765 139.974328 \n", "L 429.721525 140.244193 \n", "L 429.805838 140.064471 \n", "L 430.226741 138.317776 \n", "L 430.394787 138.434364 \n", "L 430.562654 138.9294 \n", "L 430.730331 138.604644 \n", "L 430.814092 138.179296 \n", "L 430.982234 138.655163 \n", "L 431.150959 138.344059 \n", "L 431.405222 139.339329 \n", "L 431.490518 139.351059 \n", "L 431.832365 140.134454 \n", "L 432.00319 140.34579 \n", "L 432.173834 139.902891 \n", "L 432.259092 139.722876 \n", "L 432.854607 142.039346 \n", "L 433.024345 142.976465 \n", "L 433.109143 142.782489 \n", "L 433.616918 142.074879 \n", "L 433.701387 142.23586 \n", "L 433.785436 141.712958 \n", "L 433.953037 142.005374 \n", "L 434.367328 141.084456 \n", "L 434.531307 140.166424 \n", "L 434.613052 140.155487 \n", "L 434.937366 138.179782 \n", "L 435.257999 134.48538 \n", "L 435.975339 131.28556 \n", "L 436.054833 131.449837 \n", "L 436.29086 131.279948 \n", "L 436.368721 131.302508 \n", "L 436.751928 132.739931 \n", "L 436.90239 132.705653 \n", "L 437.125089 132.087228 \n", "L 437.198894 132.292864 \n", "L 437.27267 132.230565 \n", "L 437.346414 132.217079 \n", "L 437.420126 132.316963 \n", "L 437.493798 132.149575 \n", "L 437.567437 131.629624 \n", "L 437.641039 131.873892 \n", "L 437.861642 132.987653 \n", "L 438.008542 133.453237 \n", "L 438.08194 133.495278 \n", "L 438.155304 133.01908 \n", "L 438.229001 133.099286 \n", "L 438.303028 133.13467 \n", "L 438.377384 133.397858 \n", "L 438.452071 133.334985 \n", "L 438.527083 133.323397 \n", "L 438.602421 133.455886 \n", "L 438.754073 133.219553 \n", "L 438.907031 133.57029 \n", "L 439.215369 132.793521 \n", "L 439.445286 131.796682 \n", "L 439.59752 132.238301 \n", "L 439.673044 132.167035 \n", "L 439.748529 132.176068 \n", "L 439.974765 132.788049 \n", "L 440.426339 134.131657 \n", "L 440.801664 131.898915 \n", "L 440.951528 131.777847 \n", "L 441.250805 130.519564 \n", "L 441.325529 130.499359 \n", "L 441.549482 131.397874 \n", "L 441.624065 131.850225 \n", "L 441.69826 131.7401 \n", "L 441.84655 132.002538 \n", "L 442.068727 131.666101 \n", "L 442.733379 129.624381 \n", "L 442.807064 129.857213 \n", "L 443.102845 129.192473 \n", "L 443.177049 129.259279 \n", "L 443.401196 130.200538 \n", "L 443.55139 130.09951 \n", "L 443.626786 130.074944 \n", "L 443.777808 129.487769 \n", "L 443.929396 129.940246 \n", "L 444.234244 131.835201 \n", "L 444.310717 131.836203 \n", "L 444.539906 132.535427 \n", "L 444.692515 133.477142 \n", "L 444.768759 133.341588 \n", "L 444.844968 133.278745 \n", "L 444.997271 133.649056 \n", "L 445.380841 133.009268 \n", "L 445.458133 133.200605 \n", "L 445.612597 132.905217 \n", "L 445.689772 132.97529 \n", "L 445.766908 132.894129 \n", "L 446.76126 136.38969 \n", "L 446.83716 136.318583 \n", "L 447.064631 137.251867 \n", "L 447.291773 138.018052 \n", "L 447.367408 138.065013 \n", "L 447.443005 137.948898 \n", "L 448.036824 140.284858 \n", "L 448.686448 145.130438 \n", "L 448.758009 145.19466 \n", "L 449.042227 143.539232 \n", "L 449.182613 143.11896 \n", "L 449.391751 141.686764 \n", "L 449.461291 142.308552 \n", "L 449.5996 142.819856 \n", "L 449.736784 143.645674 \n", "L 449.804825 143.5822 \n", "L 450.205395 145.631956 \n", "L 450.336011 145.364508 \n", "L 450.40111 145.444486 \n", "L 450.658863 144.190393 \n", "L 450.722481 144.331794 \n", "L 450.785734 144.2824 \n", "L 450.911828 144.277585 \n", "L 450.974337 144.336364 \n", "L 451.036821 144.13856 \n", "L 451.098944 144.307361 \n", "L 451.407474 144.908615 \n", "L 451.530694 144.27163 \n", "L 451.592269 144.383704 \n", "L 451.777514 145.351364 \n", "L 451.839324 145.334063 \n", "L 452.14798 146.3396 \n", "L 452.271262 147.070478 \n", "L 452.640489 150.16967 \n", "L 452.886143 153.084587 \n", "L 452.947494 153.054999 \n", "L 453.437435 155.443937 \n", "L 453.498564 155.262281 \n", "L 453.869786 156.381798 \n", "L 454.574998 152.948157 \n", "L 454.639601 153.146433 \n", "L 454.768735 153.316425 \n", "L 454.962236 152.801064 \n", "L 455.026684 152.863695 \n", "L 455.219858 152.048661 \n", "L 455.413133 151.303009 \n", "L 455.478052 151.363698 \n", "L 455.608787 151.093022 \n", "L 455.674602 151.205962 \n", "L 456.143603 147.638579 \n", "L 456.555789 146.21682 \n", "L 456.694661 146.420508 \n", "L 456.904186 147.540159 \n", "L 456.974292 147.558861 \n", "L 457.326583 150.118825 \n", "L 457.468609 150.541386 \n", "L 457.54006 151.189331 \n", "L 457.611798 151.021723 \n", "L 457.683821 150.976446 \n", "L 457.828724 150.406868 \n", "L 457.901606 150.636928 \n", "L 458.048222 149.948718 \n", "L 458.12164 149.92752 \n", "L 458.341365 149.2835 \n", "L 458.414642 149.114934 \n", "L 458.487884 149.176145 \n", "L 458.634265 149.076278 \n", "L 459.071018 149.852987 \n", "L 459.14332 149.609798 \n", "L 459.358132 150.21098 \n", "L 459.42904 150.010892 \n", "L 459.569813 150.408761 \n", "L 459.639679 150.281668 \n", "L 459.846258 150.7233 \n", "L 459.983203 150.562946 \n", "L 460.188399 149.625497 \n", "L 460.325044 149.430757 \n", "L 461.006475 143.870278 \n", "L 461.346063 140.946603 \n", "L 461.617261 139.783656 \n", "L 461.684992 139.57599 \n", "L 461.887999 138.407172 \n", "L 462.023197 137.833261 \n", "L 462.090754 137.877176 \n", "L 462.428109 135.107301 \n", "L 463.167754 137.310691 \n", "L 463.301875 137.482997 \n", "L 463.368892 137.442266 \n", "L 463.569775 137.022777 \n", "L 463.636674 136.955303 \n", "L 463.703541 137.299811 \n", "L 463.770373 137.160432 \n", "L 463.903944 137.324143 \n", "L 463.970682 137.003982 \n", "L 464.435775 139.879065 \n", "L 464.501357 139.832217 \n", "L 464.955697 142.386143 \n", "L 465.020149 142.162811 \n", "L 465.084575 142.211631 \n", "L 465.213345 142.33444 \n", "L 465.27769 142.460047 \n", "L 465.342013 142.370968 \n", "L 465.470577 142.694711 \n", "L 465.66321 141.814422 \n", "L 465.791502 141.482738 \n", "L 465.855611 141.552029 \n", "L 466.175751 143.162101 \n", "L 466.304524 143.442383 \n", "L 466.498846 144.688726 \n", "L 466.564171 144.64499 \n", "L 466.828207 144.694978 \n", "L 466.894597 144.283257 \n", "L 466.961263 144.453162 \n", "L 467.295673 145.639377 \n", "L 467.36247 145.590585 \n", "L 467.495977 145.934621 \n", "L 467.696021 145.615218 \n", "L 467.762641 145.830399 \n", "L 467.895489 145.46342 \n", "L 467.961419 145.591803 \n", "L 468.093196 146.831484 \n", "L 468.159041 146.804384 \n", "L 468.487876 148.520172 \n", "L 468.684852 150.335659 \n", "L 468.947101 152.014114 \n", "L 469.143498 154.147071 \n", "L 469.208905 154.375152 \n", "L 469.470241 155.922541 \n", "L 470.381493 163.735999 \n", "L 470.446385 163.578856 \n", "L 470.705655 161.368878 \n", "L 471.029098 160.280427 \n", "L 471.158284 160.491568 \n", "L 471.351859 159.942849 \n", "L 471.416331 160.120885 \n", "L 471.480779 159.954871 \n", "L 471.738326 158.244581 \n", "L 472.059689 156.607477 \n", "L 472.124177 156.75878 \n", "L 472.188641 156.922944 \n", "L 472.51377 155.291874 \n", "L 472.579237 155.350597 \n", "L 472.644965 155.362747 \n", "L 472.710666 155.497369 \n", "L 472.973183 154.584827 \n", "L 473.038744 154.55644 \n", "L 473.431539 153.254132 \n", "L 473.888557 152.430302 \n", "L 474.279228 150.523808 \n", "L 474.473343 149.537317 \n", "L 474.537993 149.491423 \n", "L 474.602614 149.595893 \n", "L 474.667213 149.554359 \n", "L 474.731787 149.450094 \n", "L 474.79633 149.830488 \n", "L 475.053092 147.925722 \n", "L 475.181314 147.599634 \n", "L 475.245385 147.672373 \n", "L 475.565354 146.257616 \n", "L 475.629272 146.291532 \n", "L 475.75703 146.106081 \n", "L 476.330621 141.574694 \n", "L 476.894704 140.318427 \n", "L 476.956373 140.348412 \n", "L 477.07963 140.501797 \n", "L 477.141222 140.36879 \n", "L 477.202791 140.591612 \n", "L 477.264338 140.524426 \n", "L 477.448815 140.085167 \n", "L 477.510258 139.899956 \n", "L 477.755778 138.54365 \n", "L 477.8171 138.168117 \n", "L 478.062122 139.010591 \n", "L 478.184478 139.737598 \n", "L 478.367839 140.949446 \n", "L 478.428917 141.066985 \n", "L 478.672976 142.10915 \n", "L 478.794851 142.124919 \n", "L 479.217859 145.210455 \n", "L 479.277197 145.173644 \n", "L 479.394977 145.962392 \n", "L 479.453418 145.854139 \n", "L 479.797758 146.732893 \n", "L 479.967557 147.727421 \n", "L 480.13718 147.610731 \n", "L 480.193681 147.145604 \n", "L 480.250161 147.243652 \n", "L 480.30662 147.608194 \n", "L 480.363057 147.431205 \n", "L 480.475591 146.931361 \n", "L 480.531691 147.089886 \n", "L 480.755871 148.286878 \n", "L 480.811863 148.153834 \n", "L 480.867833 148.183096 \n", "L 481.537962 150.867206 \n", "L 481.705886 151.697642 \n", "L 481.76182 151.642142 \n", "L 482.097786 149.436514 \n", "L 482.268095 148.298123 \n", "L 482.381713 147.932546 \n", "L 482.495787 148.693412 \n", "L 482.552793 148.574765 \n", "L 482.610053 148.536985 \n", "L 482.667565 148.66131 \n", "L 482.725326 148.619956 \n", "L 482.841598 148.397744 \n", "L 483.306871 151.561534 \n", "L 483.423246 151.535464 \n", "L 483.540072 151.285317 \n", "L 483.657353 150.942859 \n", "L 484.071903 148.282063 \n", "L 484.427979 149.874299 \n", "L 484.487248 150.1594 \n", "L 484.66493 151.586097 \n", "L 484.783273 152.080946 \n", "L 484.960629 151.653102 \n", "L 485.019703 151.627337 \n", "L 485.078754 151.787976 \n", "L 485.484043 150.499033 \n", "L 485.540787 150.590052 \n", "L 485.70929 150.986624 \n", "L 485.764883 150.934767 \n", "L 485.875206 150.600759 \n", "L 485.930471 150.862807 \n", "L 485.985986 150.792776 \n", "L 486.041751 150.646854 \n", "L 486.154018 150.920323 \n", "L 486.324258 150.512312 \n", "L 486.381494 150.684741 \n", "L 486.438975 150.556092 \n", "L 486.612888 149.773732 \n", "L 486.67135 149.90159 \n", "L 486.788202 149.649866 \n", "L 486.846592 149.910865 \n", "L 486.904692 149.733738 \n", "L 486.962771 149.560293 \n", "L 487.13607 150.207244 \n", "L 487.193532 150.098627 \n", "L 487.423168 151.109361 \n", "L 487.48052 150.92577 \n", "L 487.824235 148.798094 \n", "L 487.881452 149.160211 \n", "L 487.938381 148.955513 \n", "L 488.051912 148.929927 \n", "L 488.108518 148.793419 \n", "L 488.164838 148.937764 \n", "L 488.221136 148.881567 \n", "L 488.836641 151.531469 \n", "L 489.003953 153.83507 \n", "L 489.227533 156.438169 \n", "L 489.34038 156.791151 \n", "L 489.397038 156.43937 \n", "L 489.453938 156.619361 \n", "L 489.681318 156.104547 \n", "L 489.738111 156.168659 \n", "L 490.021769 157.935193 \n", "L 490.078443 157.767442 \n", "L 490.304676 158.809517 \n", "L 490.417022 159.069169 \n", "L 490.750209 160.617477 \n", "L 490.805322 160.526944 \n", "L 490.915484 160.239372 \n", "L 491.190539 156.818027 \n", "L 491.300408 155.965279 \n", "L 491.57471 154.236427 \n", "L 491.684292 154.973032 \n", "L 491.739058 154.943163 \n", "L 491.793804 155.134275 \n", "L 491.84853 154.870894 \n", "L 491.903493 155.029064 \n", "L 492.125686 156.290775 \n", "L 492.181822 155.932307 \n", "L 492.238194 155.810257 \n", "L 492.408714 154.452179 \n", "L 492.466021 154.630882 \n", "L 492.523567 154.508048 \n", "L 492.581344 154.595681 \n", "L 492.755315 153.924569 \n", "L 492.871174 153.59373 \n", "L 493.218245 152.048948 \n", "L 493.506849 152.459376 \n", "L 494.024771 149.527257 \n", "L 494.08224 149.806016 \n", "L 494.310389 148.773528 \n", "L 494.478627 149.201842 \n", "L 494.589417 149.628833 \n", "L 494.863141 147.630767 \n", "L 494.917778 147.675405 \n", "L 495.451026 144.299857 \n", "L 495.554588 144.377305 \n", "L 495.605969 144.273852 \n", "L 495.708182 144.78327 \n", "L 495.759266 144.632926 \n", "L 496.014431 143.695231 \n", "L 496.218256 144.667155 \n", "L 496.269175 144.595606 \n", "L 496.32008 144.850102 \n", "L 496.828207 140.718891 \n", "L 497.135502 139.52437 \n", "L 497.187112 139.386326 \n", "L 498.117576 145.077952 \n", "L 498.683689 142.841266 \n", "L 498.837746 143.766444 \n", "L 498.889065 143.744718 \n", "L 499.145388 143.912422 \n", "L 499.452432 145.480872 \n", "L 499.553913 144.918884 \n", "L 499.654843 144.985095 \n", "L 499.705162 145.026393 \n", "L 499.805749 144.567425 \n", "L 500.00721 145.903575 \n", "L 500.210349 147.056352 \n", "L 500.414437 147.418463 \n", "L 500.465967 147.287569 \n", "L 500.517722 147.453728 \n", "L 500.674337 147.979961 \n", "L 500.726993 147.699955 \n", "L 500.779877 147.479262 \n", "L 500.993664 149.006733 \n", "L 501.155885 149.538065 \n", "L 501.210325 149.54076 \n", "L 501.374987 150.063369 \n", "L 501.430317 149.773431 \n", "L 501.597636 150.691312 \n", "L 501.653847 150.609902 \n", "L 501.935324 149.001347 \n", "L 502.157137 148.7821 \n", "L 502.266727 149.693922 \n", "L 502.321491 149.330072 \n", "L 502.376231 149.315214 \n", "L 502.594985 150.408274 \n", "L 502.758852 152.065126 \n", "L 502.813436 151.757188 \n", "L 503.14055 153.268747 \n", "L 503.249436 153.054687 \n", "L 503.358247 153.527034 \n", "L 503.412623 153.497622 \n", "L 503.629928 154.175482 \n", "L 503.738471 154.002042 \n", "L 503.792713 153.858271 \n", "L 504.009497 154.754344 \n", "L 504.433645 153.819117 \n", "L 504.537139 153.296497 \n", "L 504.588504 153.325418 \n", "L 505.145291 157.097763 \n", "L 505.397411 155.517234 \n", "L 505.447792 155.614438 \n", "L 505.799982 153.400375 \n", "L 505.850458 153.429139 \n", "L 506.002722 153.966403 \n", "L 506.053599 153.884554 \n", "L 506.307752 152.066514 \n", "L 506.409295 152.236394 \n", "L 506.662858 151.118261 \n", "L 506.71352 151.198929 \n", "L 506.764167 151.230886 \n", "L 506.814796 151.784701 \n", "L 506.915537 151.693765 \n", "L 507.015747 151.635355 \n", "L 507.115191 151.951382 \n", "L 507.312466 150.875647 \n", "L 507.50854 151.243072 \n", "L 507.557521 151.144265 \n", "L 507.606491 151.258438 \n", "L 507.753301 151.690102 \n", "L 507.802209 151.22773 \n", "L 507.851101 151.466741 \n", "L 507.949074 151.695806 \n", "L 507.998155 151.594328 \n", "L 508.449347 150.295923 \n", "L 508.500115 150.581072 \n", "L 508.855055 147.674732 \n", "L 509.108061 149.345459 \n", "L 509.15861 149.287732 \n", "L 509.612842 150.855714 \n", "L 509.663238 150.629575 \n", "L 509.713615 150.773733 \n", "L 509.763974 151.119185 \n", "L 509.814084 150.701002 \n", "L 509.963629 151.109973 \n", "L 510.062102 150.897646 \n", "L 510.1112 151.330457 \n", "L 510.160283 151.191352 \n", "L 510.307439 150.729146 \n", "L 510.55308 151.220265 \n", "L 510.602252 151.479245 \n", "L 510.798559 150.593262 \n", "L 510.991661 150.641198 \n", "L 511.13395 149.884565 \n", "L 511.369678 152.141484 \n", "L 511.416778 152.255915 \n", "L 511.605033 153.456047 \n", "L 511.652064 153.320684 \n", "L 511.746084 154.173908 \n", "L 511.840046 154.849478 \n", "L 511.887008 154.725045 \n", "L 512.027128 153.602988 \n", "L 512.165316 154.572631 \n", "L 512.256549 154.657094 \n", "L 512.346829 155.175041 \n", "L 512.568599 156.653083 \n", "L 512.612417 156.6033 \n", "L 512.656 156.263344 \n", "L 512.699569 156.599751 \n", "L 512.786222 156.801873 \n", "L 512.829305 156.658605 \n", "L 512.91543 156.730643 \n", "L 513.001065 157.341594 \n", "L 513.043639 157.255107 \n", "L 513.086202 157.273147 \n", "L 513.170624 156.987877 \n", "L 513.212706 157.278317 \n", "L 513.254771 157.069088 \n", "L 513.338867 156.644237 \n", "L 513.464939 156.213078 \n", "L 513.506942 156.674646 \n", "L 513.59091 156.383714 \n", "L 513.67483 156.608794 \n", "L 513.968187 158.972721 \n", "L 514.051906 159.152185 \n", "L 514.09375 159.113913 \n", "L 514.219218 160.119187 \n", "L 514.261013 160.024242 \n", "L 514.67833 154.793534 \n", "L 514.803299 154.387577 \n", "L 514.844932 154.437477 \n", "L 514.928162 154.772469 \n", "L 514.969762 154.673301 \n", "L 515.136028 154.092539 \n", "L 515.177785 154.531014 \n", "L 515.219532 154.241185 \n", "L 515.474419 151.359708 \n", "L 515.517631 151.383434 \n", "L 515.561049 151.5688 \n", "L 515.604675 151.501168 \n", "L 515.825256 149.867865 \n", "L 515.869906 149.488861 \n", "L 515.914761 149.912264 \n", "L 515.959825 149.919182 \n", "L 516.142156 150.970489 \n", "L 516.188258 150.84608 \n", "L 516.609129 147.800716 \n", "L 516.702778 148.227483 \n", "L 516.889899 150.011162 \n", "L 517.217075 153.64177 \n", "L 517.406457 155.549241 \n", "L 517.454147 155.52481 \n", "L 518.330478 151.266969 \n", "L 518.379188 151.637237 \n", "L 518.427884 151.45863 \n", "L 518.574095 150.563361 \n", "L 518.622518 150.577121 \n", "L 518.766398 150.438544 \n", "L 518.813893 150.622342 \n", "L 518.861158 150.486199 \n", "L 518.908191 150.08008 \n", "L 518.954996 150.409731 \n", "L 519.094037 150.331875 \n", "L 519.139927 150.355512 \n", "L 519.276871 149.803866 \n", "L 519.368099 149.595511 \n", "L 519.550379 149.911442 \n", "L 519.686944 149.397438 \n", "L 519.732441 149.560501 \n", "L 520.050584 152.685055 \n", "L 520.141374 153.324054 \n", "L 520.186747 153.126986 \n", "L 520.368122 153.031152 \n", "L 520.504017 152.633106 \n", "L 520.549291 152.722862 \n", "L 520.639803 153.087945 \n", "L 520.90785 155.926271 \n", "L 521.2103 158.671378 \n", "L 521.420869 160.012697 \n", "L 521.630936 158.588435 \n", "L 521.756843 158.820554 \n", "L 521.79879 158.612219 \n", "L 521.966463 160.721124 \n", "L 522.008352 160.585815 \n", "L 522.802209 156.905483 \n", "L 522.843872 156.953956 \n", "L 523.052444 158.047956 \n", "L 523.094249 158.064504 \n", "L 523.136045 157.912399 \n", "L 523.178042 158.110856 \n", "L 523.305223 159.491598 \n", "L 523.39038 159.280969 \n", "L 523.432943 159.206469 \n", "L 523.60307 160.354536 \n", "L 523.772798 159.546085 \n", "L 523.984096 161.250091 \n", "L 524.026488 161.161359 \n", "L 524.19699 162.009832 \n", "L 524.239947 161.894986 \n", "L 524.368946 161.703585 \n", "L 524.41185 161.768382 \n", "L 524.454741 162.275552 \n", "L 524.540487 162.043741 \n", "L 524.711843 161.697902 \n", "L 524.797451 161.944883 \n", "L 525.052314 164.719662 \n", "L 525.094613 164.632954 \n", "L 525.136695 164.755005 \n", "L 525.178768 165.231427 \n", "L 525.220827 164.840067 \n", "L 525.304906 164.790086 \n", "L 525.55686 165.56217 \n", "L 525.598607 165.466805 \n", "L 525.76485 165.009069 \n", "L 525.806329 164.973146 \n", "L 525.930905 164.419442 \n", "L 525.972753 164.8535 \n", "L 526.05703 164.579395 \n", "L 526.142086 164.451227 \n", "L 526.184909 164.757138 \n", "L 526.227928 164.568053 \n", "L 526.314548 164.438155 \n", "L 526.401742 164.052199 \n", "L 526.445322 164.316294 \n", "L 526.532446 164.518967 \n", "L 526.57599 164.419315 \n", "L 526.749415 163.837649 \n", "L 527.003332 164.737959 \n", "L 527.127373 166.321042 \n", "L 527.289955 167.706761 \n", "L 527.573027 164.888278 \n", "L 527.653815 164.910189 \n", "L 527.694194 165.132185 \n", "L 527.774914 164.96194 \n", "L 527.936216 165.765475 \n", "L 528.176999 166.760588 \n", "L 528.257034 166.849647 \n", "L 528.297035 166.580021 \n", "L 528.337026 165.930553 \n", "L 528.417583 166.120896 \n", "L 528.581008 167.206074 \n", "L 528.622342 166.814686 \n", "L 528.663867 167.054331 \n", "L 528.789583 167.734799 \n", "L 528.831868 167.584549 \n", "L 528.917015 167.946198 \n", "L 528.959878 167.895362 \n", "L 529.132012 167.346418 \n", "L 529.262197 167.947434 \n", "L 529.435216 168.757513 \n", "L 529.47814 168.662737 \n", "L 529.73121 170.155003 \n", "L 530.020014 171.972205 \n", "L 530.185761 170.611681 \n", "L 530.268567 170.744984 \n", "L 530.309657 170.86549 \n", "L 530.511934 169.133063 \n", "L 530.591572 168.87115 \n", "L 530.67057 168.915578 \n", "L 530.709955 168.898399 \n", "L 530.749332 168.716726 \n", "L 530.7885 168.922031 \n", "L 530.827857 169.344832 \n", "L 530.906939 169.282382 \n", "L 530.985974 168.632051 \n", "L 531.025478 168.680005 \n", "L 531.143929 168.921721 \n", "L 531.26229 168.675519 \n", "L 531.459357 169.650514 \n", "L 531.49874 169.637663 \n", "L 531.538113 169.90713 \n", "L 531.577476 169.587227 \n", "L 531.617028 169.590619 \n", "L 532.190304 166.314287 \n", "L 532.232274 166.376829 \n", "L 532.483667 168.732402 \n", "L 532.525168 168.59779 \n", "L 532.56646 168.680522 \n", "L 532.648421 168.325862 \n", "L 532.689089 168.33519 \n", "L 532.769803 168.463272 \n", "L 532.80985 168.342628 \n", "L 532.96797 166.758537 \n", "L 533.006983 166.675592 \n", "L 533.862893 156.853334 \n", "L 533.901695 156.971825 \n", "L 533.979276 157.147625 \n", "L 534.095568 156.416244 \n", "L 534.134508 156.50254 \n", "L 534.173633 156.552634 \n", "L 534.292118 156.420435 \n", "L 534.412271 156.762853 \n", "L 534.575063 156.097736 \n", "L 534.698885 154.610621 \n", "L 534.864082 153.65055 \n", "L 534.946612 153.509692 \n", "L 535.112109 152.720385 \n", "L 535.15389 153.003761 \n", "L 535.195853 152.614025 \n", "L 535.364941 151.801705 \n", "L 535.407232 151.767877 \n", "L 535.576276 149.834083 \n", "L 535.826603 147.183707 \n", "L 535.908608 146.489318 \n", "L 536.03153 145.335848 \n", "L 536.072481 145.373249 \n", "L 536.236158 145.582347 \n", "L 536.317939 145.010754 \n", "L 536.358815 145.280168 \n", "L 536.522198 145.765438 \n", "L 536.644611 146.641818 \n", "L 536.685393 146.634495 \n", "L 536.889155 147.752728 \n", "L 537.050063 147.117765 \n", "L 537.089781 147.43327 \n", "L 537.16861 147.226029 \n", "L 537.285333 146.080497 \n", "L 537.324031 146.133914 \n", "L 537.715823 147.850874 \n", "L 538.074035 150.588823 \n", "L 538.379669 154.982833 \n", "L 538.454405 154.491558 \n", "L 538.49147 154.529812 \n", "L 538.673803 156.766306 \n", "L 538.780807 156.523429 \n", "L 538.920901 157.204729 \n", "L 539.060307 157.898567 \n", "L 539.095138 157.887935 \n", "L 539.129959 157.886024 \n", "L 539.370665 158.867471 \n", "L 539.569508 160.862865 \n", "L 539.762462 163.590818 \n", "L 539.95576 166.020803 \n", "L 539.988517 166.154081 \n", "L 540.458968 173.726097 \n", "L 540.557463 173.121365 \n", "L 540.589905 173.34999 \n", "L 540.622153 173.488259 \n", "L 540.749753 175.787698 \n", "L 540.813422 175.47166 \n", "L 540.84525 175.45188 \n", "L 541.004101 173.942518 \n", "L 541.128578 172.533252 \n", "L 541.251838 172.753005 \n", "L 541.28264 172.636644 \n", "L 541.436738 174.900089 \n", "L 541.467873 174.756456 \n", "L 541.594024 174.417224 \n", "L 541.720623 175.029822 \n", "L 541.752259 174.978757 \n", "L 541.783887 175.23647 \n", "L 541.847125 174.888394 \n", "L 541.910902 174.73013 \n", "L 542.106539 175.862964 \n", "L 542.342722 174.069093 \n", "L 542.411488 174.431561 \n", "L 542.446139 174.259847 \n", "L 542.480966 174.358546 \n", "L 542.515787 174.484671 \n", "L 542.724551 176.529622 \n", "L 542.759321 176.589934 \n", "L 542.794083 176.923007 \n", "L 542.8634 176.537657 \n", "L 543.204197 173.691422 \n", "L 543.629648 171.3357 \n", "L 543.738875 171.699677 \n", "L 543.920742 172.694412 \n", "L 543.957091 172.087416 \n", "L 544.029759 172.298017 \n", "L 544.06608 172.302573 \n", "L 544.710699 169.982256 \n", "L 544.783016 168.970971 \n", "L 544.855301 169.298991 \n", "L 545.254969 172.349543 \n", "L 545.292096 172.388752 \n", "L 545.442323 173.22581 \n", "L 545.634355 172.078064 \n", "L 545.673023 171.845235 \n", "L 545.750336 172.001692 \n", "L 545.827618 171.917776 \n", "L 546.135109 167.527034 \n", "L 546.211479 167.182036 \n", "L 546.440356 163.788711 \n", "L 546.478471 163.552128 \n", "L 546.516577 163.620916 \n", "L 546.706969 165.226244 \n", "L 546.897309 167.036505 \n", "L 546.935676 166.856022 \n", "L 546.974214 167.076755 \n", "L 547.012921 166.825014 \n", "L 547.0518 166.849842 \n", "L 547.090851 167.182603 \n", "L 547.130068 166.699811 \n", "L 547.288624 166.062898 \n", "L 547.328507 166.127271 \n", "L 547.408421 166.200189 \n", "L 547.488291 166.518577 \n", "L 547.567402 167.067763 \n", "L 547.606671 166.671365 \n", "L 547.800173 165.210069 \n", "L 547.838309 165.406939 \n", "L 547.876258 165.495612 \n", "L 548.063368 164.263456 \n", "L 548.100546 164.465523 \n", "L 548.32344 163.706697 \n", "L 548.434777 164.396657 \n", "L 548.583119 164.867375 \n", "L 548.657236 165.061188 \n", "L 548.807145 165.51575 \n", "L 548.883111 165.104379 \n", "L 548.921345 165.340125 \n", "L 548.959747 165.318795 \n", "L 548.998319 165.398425 \n", "L 549.037061 165.435507 \n", "L 549.075972 165.665658 \n", "L 549.075972 165.665658 \n", "\" clip-path=\"url(#pa3fd31ed85)\" style=\"fill: none; stroke: #ffa500; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 368.387063 184.796098 \n", "L 368.387063 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 368.387062 184.796098 \n", "L 555.162337 184.796098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 645.804188 184.796098 \n", "L 832.579462 184.796098 \n", "L 832.579462 11.588098 \n", "L 645.804188 11.588098 \n", "L 645.804188 184.796098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_33\">\n", "      <path d=\"M 832.579462 184.796098 \n", "L 832.579462 11.588098 \n", "\" clip-path=\"url(#p241e7f9d2a)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m81d99b1d62\" x=\"832.579462\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- $\\mathdefault{10^{9}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(822.415462 203.072293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"645.804187\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- $\\mathdefault{3\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(624.898687 200.972293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"690.433018\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- $\\mathdefault{4\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(669.527518 200.972293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"725.049828\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"753.333822\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- $\\mathdefault{6\\times10^{8}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(732.428322 200.972293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"777.247597\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"797.962652\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m7636d51c90\" x=\"816.234625\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_27\">\n", "     <!-- Global step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(703.464919 225.048363) scale(0.126 -0.126)\">\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"105.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"166.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"229.931641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"291.210938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"318.994141\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"350.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.613281\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"645.804188\" y=\"184.796098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- $\\mathdefault{2\\times10^{0}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(596.593188 189.184195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"645.804188\" y=\"83.475913\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- $\\mathdefault{3\\times10^{0}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(596.593188 87.86401) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m24cc2c6e37\" x=\"645.804188\" y=\"11.588098\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- $\\mathdefault{4\\times10^{0}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(596.593188 15.976195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(314.580078 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_31\">\n", "     <!-- Collided [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(589.972781 136.47641) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"186.572266\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"214.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"277.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"339.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"402.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"434.619141\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"473.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"568.652344\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_45\">\n", "    <path d=\"M 645.706715 54.091936 \n", "L 645.971975 55.644323 \n", "L 646.104433 55.652682 \n", "L 646.236774 55.512828 \n", "L 646.369005 55.137258 \n", "L 646.633101 54.094585 \n", "L 646.76498 53.746951 \n", "L 646.896735 53.989004 \n", "L 647.027813 53.337738 \n", "L 647.15821 53.368727 \n", "L 647.288495 53.782262 \n", "L 647.677554 52.705811 \n", "L 647.806839 52.406733 \n", "L 648.063949 52.428886 \n", "L 648.192334 52.768727 \n", "L 648.448221 51.090301 \n", "L 648.703148 49.284335 \n", "L 648.957661 48.960274 \n", "L 649.084767 49.048984 \n", "L 649.212321 50.203424 \n", "L 649.339772 50.133881 \n", "L 649.466563 49.480168 \n", "L 649.593246 49.607493 \n", "L 649.719819 49.439237 \n", "L 649.846297 49.469218 \n", "L 649.972667 49.625785 \n", "L 650.099492 49.97177 \n", "L 650.226209 49.824947 \n", "L 650.863144 45.533961 \n", "L 650.99143 45.466967 \n", "L 651.120156 45.909018 \n", "L 651.635054 45.526913 \n", "L 651.892942 44.896641 \n", "L 652.021993 44.010059 \n", "L 652.150934 44.191258 \n", "L 652.408486 45.816992 \n", "L 652.665621 46.403908 \n", "L 652.794023 47.056264 \n", "L 652.922331 47.06434 \n", "L 653.050539 46.483707 \n", "L 653.562314 46.907426 \n", "L 654.072383 45.475919 \n", "L 654.199641 44.857948 \n", "L 654.961075 45.280656 \n", "L 655.21407 44.41645 \n", "L 655.970597 46.298422 \n", "L 656.096329 45.580785 \n", "L 656.472909 47.106283 \n", "L 656.598232 47.42369 \n", "L 656.723456 47.226937 \n", "L 656.848579 47.414078 \n", "L 656.973602 47.940833 \n", "L 657.222264 46.791735 \n", "L 657.34644 47.494205 \n", "L 657.594478 46.794584 \n", "L 657.842111 47.064935 \n", "L 659.075405 39.75591 \n", "L 659.198606 40.153387 \n", "L 659.321713 40.974414 \n", "L 659.444717 40.902292 \n", "L 659.567625 40.047494 \n", "L 659.813153 40.234103 \n", "L 659.935779 39.906769 \n", "L 660.180747 38.411621 \n", "L 660.303082 38.837042 \n", "L 660.669496 37.677901 \n", "L 660.791448 37.492973 \n", "L 661.035055 36.616306 \n", "L 661.156716 36.785022 \n", "L 661.399762 38.142469 \n", "L 661.521142 37.873117 \n", "L 661.763624 38.868003 \n", "L 661.884722 38.816285 \n", "L 662.005733 38.325816 \n", "L 662.12717 38.999691 \n", "L 662.24954 39.031587 \n", "L 662.371821 38.865365 \n", "L 662.861515 37.533802 \n", "L 662.984328 37.510755 \n", "L 663.107048 37.186862 \n", "L 663.229678 36.209724 \n", "L 663.719231 37.607221 \n", "L 663.841373 37.403267 \n", "L 664.085366 37.711299 \n", "L 664.32796 37.022421 \n", "L 664.570185 37.38374 \n", "L 664.81202 38.342388 \n", "L 664.932797 38.18884 \n", "L 665.532824 39.23931 \n", "L 665.772204 39.775943 \n", "L 665.891754 39.948634 \n", "L 666.011213 39.722204 \n", "L 666.250855 40.02467 \n", "L 666.489145 39.558813 \n", "L 666.847392 40.162975 \n", "L 666.966618 40.099615 \n", "L 667.085757 39.647843 \n", "L 667.204804 39.637142 \n", "L 667.561391 38.833607 \n", "L 667.798638 38.759345 \n", "L 668.035499 38.250956 \n", "L 668.271991 37.508932 \n", "L 669.328879 39.400057 \n", "L 669.44423 39.719775 \n", "L 669.786843 39.16824 \n", "L 670.012403 37.762974 \n", "L 670.124333 37.783755 \n", "L 670.346495 37.072147 \n", "L 670.456731 36.97801 \n", "L 670.566406 36.598979 \n", "L 670.7865 37.219025 \n", "L 670.896432 37.017863 \n", "L 671.006284 37.368562 \n", "L 671.225745 39.027052 \n", "L 671.335364 39.118166 \n", "L 671.554376 40.308956 \n", "L 671.663763 40.207709 \n", "L 672.318456 43.825407 \n", "L 672.427303 44.52783 \n", "L 672.64477 44.06184 \n", "L 672.861913 44.799746 \n", "L 672.970373 44.658932 \n", "L 673.295315 46.216979 \n", "L 673.403479 45.672659 \n", "L 673.619589 47.249273 \n", "L 674.050898 50.418683 \n", "L 674.158536 50.504683 \n", "L 674.373594 49.219764 \n", "L 674.481006 49.279158 \n", "L 674.695603 50.050128 \n", "L 675.016969 50.670382 \n", "L 675.123943 50.427689 \n", "L 675.444444 51.180324 \n", "L 675.551128 50.69212 \n", "L 675.657741 50.979497 \n", "L 675.976665 52.483666 \n", "L 676.082512 52.542776 \n", "L 676.293988 53.395321 \n", "L 676.610694 56.282418 \n", "L 676.716123 56.36804 \n", "L 677.031949 56.298323 \n", "L 677.347151 57.140525 \n", "L 677.556944 58.427173 \n", "L 677.871087 59.072199 \n", "L 678.08015 58.906125 \n", "L 678.184578 58.748468 \n", "L 678.288938 59.983245 \n", "L 678.392765 59.765481 \n", "L 678.598821 60.331604 \n", "L 678.903946 61.730425 \n", "L 679.004597 62.175235 \n", "L 679.104726 62.113494 \n", "L 679.303408 61.291283 \n", "L 679.598438 61.963938 \n", "L 679.792832 62.158432 \n", "L 679.889707 62.538622 \n", "L 680.081913 61.420778 \n", "L 680.177236 61.546919 \n", "L 680.272042 61.926995 \n", "L 680.460117 61.665545 \n", "L 680.553388 61.703918 \n", "L 680.646151 61.446751 \n", "L 680.738404 61.679583 \n", "L 680.830152 61.448002 \n", "L 680.921391 61.771895 \n", "L 681.102344 61.56721 \n", "L 681.192055 61.263328 \n", "L 681.282163 61.580524 \n", "L 681.55215 60.417349 \n", "L 681.733243 60.131503 \n", "L 681.824396 59.99496 \n", "L 681.915951 60.324845 \n", "L 682.007905 60.198091 \n", "L 682.473597 62.026412 \n", "L 682.853197 65.006119 \n", "L 682.948172 64.990883 \n", "L 683.516833 66.592307 \n", "L 683.705923 65.479047 \n", "L 683.800395 65.388603 \n", "L 684.17771 66.868651 \n", "L 684.271893 66.675819 \n", "L 684.460098 65.900029 \n", "L 684.648081 66.295535 \n", "L 684.74199 66.207006 \n", "L 684.835852 66.280032 \n", "L 685.0234 68.226208 \n", "L 685.210719 67.163189 \n", "L 685.397811 67.199292 \n", "L 685.491271 66.947133 \n", "L 685.584676 67.067796 \n", "L 685.678025 67.565875 \n", "L 685.771323 67.4912 \n", "L 685.864566 67.050571 \n", "L 686.23698 69.06551 \n", "L 686.329948 68.690191 \n", "L 686.422863 68.902437 \n", "L 686.701255 67.604435 \n", "L 686.88656 66.737737 \n", "L 686.979125 67.122463 \n", "L 687.071633 67.005591 \n", "L 687.256486 68.382459 \n", "L 687.533349 67.132448 \n", "L 687.717656 67.81218 \n", "L 687.809729 67.627105 \n", "L 687.901741 67.699539 \n", "L 687.993699 67.540563 \n", "L 688.085602 66.95283 \n", "L 688.727404 71.185913 \n", "L 688.910293 69.994655 \n", "L 689.275434 68.833602 \n", "L 689.366598 69.396655 \n", "L 689.457712 69.186101 \n", "L 689.640205 67.867177 \n", "L 689.824188 67.771968 \n", "L 690.009668 67.999751 \n", "L 690.290702 69.558181 \n", "L 690.86275 72.888565 \n", "L 690.959377 72.64848 \n", "L 691.055945 71.863008 \n", "L 691.152874 72.578137 \n", "L 691.250174 72.321326 \n", "L 691.347837 72.453909 \n", "L 691.544253 73.448798 \n", "L 691.742109 74.817679 \n", "L 691.841581 75.351169 \n", "L 691.941412 75.30269 \n", "L 692.0416 75.208211 \n", "L 692.243045 75.769626 \n", "L 692.445911 76.905631 \n", "L 692.547034 76.614188 \n", "L 692.648092 77.351179 \n", "L 692.749081 77.023192 \n", "L 693.253063 78.6098 \n", "L 693.452928 80.021999 \n", "L 693.94416 82.549197 \n", "L 694.040964 81.647012 \n", "L 694.705278 83.706837 \n", "L 694.892076 83.518754 \n", "L 695.171844 85.158203 \n", "L 695.264987 85.226558 \n", "L 695.544073 86.293949 \n", "L 695.636985 86.332692 \n", "L 695.729837 85.814224 \n", "L 695.822629 86.013471 \n", "L 695.915369 85.566268 \n", "L 696.193242 85.89472 \n", "L 696.469786 84.487842 \n", "L 696.561314 84.961022 \n", "L 696.835578 84.686271 \n", "L 697.018148 83.029623 \n", "L 697.200486 83.443232 \n", "L 697.382612 83.128652 \n", "L 697.655395 83.450923 \n", "L 697.74621 83.244525 \n", "L 698.017547 85.485908 \n", "L 698.286409 87.28601 \n", "L 698.375658 87.498467 \n", "L 698.552788 87.385037 \n", "L 698.816696 88.912504 \n", "L 698.904431 88.626244 \n", "L 699.079756 89.404465 \n", "L 699.254873 88.685497 \n", "L 699.342353 88.879648 \n", "L 699.517172 88.834478 \n", "L 699.953326 85.734866 \n", "L 700.040401 85.896278 \n", "L 700.127427 86.324002 \n", "L 700.301339 87.648614 \n", "L 700.388225 87.796141 \n", "L 700.47506 87.597907 \n", "L 700.561844 86.673944 \n", "L 700.648577 86.871297 \n", "L 700.821896 87.306392 \n", "L 700.995027 88.90811 \n", "L 701.081517 88.822693 \n", "L 701.167957 89.142943 \n", "L 701.25435 88.164206 \n", "L 701.340693 88.17874 \n", "L 701.513253 87.339956 \n", "L 701.599466 87.257864 \n", "L 701.94383 84.512749 \n", "L 702.029804 84.636669 \n", "L 702.201612 85.219347 \n", "L 702.287444 86.228212 \n", "L 702.373233 86.025296 \n", "L 702.630339 84.552294 \n", "L 702.88701 84.623702 \n", "L 703.057891 85.351959 \n", "L 703.143266 85.392163 \n", "L 703.313878 84.671878 \n", "L 703.569469 86.259132 \n", "L 704.079755 82.842471 \n", "L 704.165358 82.865933 \n", "L 704.59847 81.706652 \n", "L 704.686113 81.864297 \n", "L 704.951064 80.742733 \n", "L 705.040059 81.148231 \n", "L 705.129391 81.003601 \n", "L 705.309069 81.707554 \n", "L 705.399025 81.367838 \n", "L 705.758316 82.998778 \n", "L 705.936484 82.642106 \n", "L 706.112912 82.822192 \n", "L 706.80133 88.322527 \n", "L 706.885825 88.596003 \n", "L 707.054293 90.501204 \n", "L 707.13865 90.45348 \n", "L 707.222963 90.485885 \n", "L 707.307232 90.067865 \n", "L 707.391457 90.529048 \n", "L 707.643866 89.817334 \n", "L 707.811912 90.883809 \n", "L 707.895869 90.780734 \n", "L 708.147456 91.678648 \n", "L 708.31531 90.975454 \n", "L 708.399359 91.207215 \n", "L 708.992891 86.855655 \n", "L 709.078472 87.239989 \n", "L 709.334923 85.919935 \n", "L 709.590959 86.009935 \n", "L 709.676217 85.533278 \n", "L 709.761429 85.999756 \n", "L 709.846593 85.648918 \n", "L 710.271732 87.931574 \n", "L 710.69571 90.21597 \n", "L 711.118512 95.547301 \n", "L 711.202561 94.966509 \n", "L 711.286571 95.086225 \n", "L 711.370162 95.626878 \n", "L 711.453708 95.456311 \n", "L 711.619548 95.578441 \n", "L 711.784453 96.42299 \n", "L 711.866651 95.989063 \n", "L 712.273664 98.035381 \n", "L 712.354491 98.049458 \n", "L 712.515269 97.299435 \n", "L 712.754988 98.638254 \n", "L 712.914603 99.512311 \n", "L 713.153724 98.756067 \n", "L 713.233346 98.670191 \n", "L 713.392464 99.78993 \n", "L 713.551043 100.806566 \n", "L 713.629717 100.413206 \n", "L 713.707985 100.566902 \n", "L 713.940343 102.599775 \n", "L 714.319515 104.101277 \n", "L 714.468379 104.054267 \n", "L 714.542214 104.113741 \n", "L 714.616019 104.492169 \n", "L 714.837251 104.004107 \n", "L 714.910923 104.286413 \n", "L 715.131732 102.10982 \n", "L 715.205266 102.253843 \n", "L 715.352232 101.497794 \n", "L 715.499065 100.991017 \n", "L 715.572429 99.652037 \n", "L 715.646126 99.764738 \n", "L 715.720153 99.660661 \n", "L 715.794509 99.909021 \n", "L 716.019546 98.081459 \n", "L 716.171198 97.503772 \n", "L 716.247514 97.472843 \n", "L 716.40112 96.594465 \n", "L 716.478044 96.817957 \n", "L 716.55529 96.672914 \n", "L 716.786052 95.343329 \n", "L 716.862411 95.284854 \n", "L 717.090169 96.088193 \n", "L 717.165654 95.923996 \n", "L 717.241101 96.37705 \n", "L 717.467233 94.930833 \n", "L 717.542545 94.867501 \n", "L 717.69307 94.215897 \n", "L 717.768284 94.252244 \n", "L 717.843464 94.770786 \n", "L 717.918603 94.397584 \n", "L 718.293739 96.896344 \n", "L 718.443527 97.34295 \n", "L 718.66793 95.851252 \n", "L 718.742654 95.721727 \n", "L 718.966607 94.181896 \n", "L 719.04119 94.944907 \n", "L 719.189546 94.539712 \n", "L 719.411828 94.948131 \n", "L 719.485852 94.665012 \n", "L 719.559835 95.04135 \n", "L 719.633785 94.979663 \n", "L 720.003035 92.196496 \n", "L 720.150504 91.216287 \n", "L 720.224189 91.543617 \n", "L 720.445728 89.164137 \n", "L 720.668693 88.013465 \n", "L 720.818321 87.398902 \n", "L 721.043911 88.045512 \n", "L 721.119263 87.885859 \n", "L 721.346521 88.761696 \n", "L 721.498662 89.356673 \n", "L 721.957031 86.324155 \n", "L 722.10964 86.990835 \n", "L 722.185884 86.905843 \n", "L 722.262093 87.168642 \n", "L 722.566899 85.679196 \n", "L 722.720637 86.534481 \n", "L 722.797966 86.437886 \n", "L 723.184033 88.846764 \n", "L 723.338184 89.26297 \n", "L 723.79832 93.432851 \n", "L 723.874409 93.206374 \n", "L 723.950459 93.586665 \n", "L 724.02647 93.420992 \n", "L 724.254285 92.468459 \n", "L 724.330145 92.943998 \n", "L 724.557507 91.510096 \n", "L 724.633223 91.791698 \n", "L 724.86013 90.671031 \n", "L 725.085655 92.242112 \n", "L 725.234109 91.127692 \n", "L 725.307766 91.352686 \n", "L 725.381045 91.300706 \n", "L 725.526822 90.772805 \n", "L 726.175134 94.639619 \n", "L 726.246662 94.131156 \n", "L 726.317819 93.435734 \n", "L 726.529732 94.448385 \n", "L 726.66971 93.955828 \n", "L 726.739311 93.928595 \n", "L 727.085502 92.401775 \n", "L 727.289623 91.510521 \n", "L 727.423875 91.645892 \n", "L 727.68801 93.14666 \n", "L 728.139606 96.09816 \n", "L 728.202859 96.009142 \n", "L 728.453946 97.275776 \n", "L 728.516069 97.058749 \n", "L 728.701274 98.355956 \n", "L 728.886222 96.261576 \n", "L 728.947819 96.482668 \n", "L 729.009394 96.592106 \n", "L 729.256449 95.108713 \n", "L 729.811563 88.442629 \n", "L 729.873118 87.833496 \n", "L 729.934646 87.866897 \n", "L 729.996145 87.723537 \n", "L 730.303268 91.104893 \n", "L 730.487257 91.522895 \n", "L 730.671027 92.873838 \n", "L 730.73223 92.799733 \n", "L 730.793407 92.919246 \n", "L 730.915689 91.727805 \n", "L 731.038216 91.622615 \n", "L 731.47661 87.706428 \n", "L 731.540445 87.599423 \n", "L 731.798146 84.522353 \n", "L 731.862834 84.821672 \n", "L 732.056726 84.040782 \n", "L 732.121306 84.158433 \n", "L 732.572617 82.173432 \n", "L 732.701325 82.504799 \n", "L 732.895177 81.200352 \n", "L 733.025912 82.234297 \n", "L 733.091727 82.173536 \n", "L 733.357964 79.891682 \n", "L 733.560728 77.887279 \n", "L 733.628905 78.252729 \n", "L 733.834884 76.716596 \n", "L 733.972914 77.047749 \n", "L 734.181335 78.33677 \n", "L 734.251176 78.271726 \n", "L 734.391417 78.940374 \n", "L 734.461814 79.073117 \n", "L 734.532176 79.031427 \n", "L 735.028923 82.470646 \n", "L 735.173253 83.448104 \n", "L 735.391897 85.298589 \n", "L 735.538765 85.869253 \n", "L 735.75849 84.755221 \n", "L 735.831767 84.708345 \n", "L 735.978215 84.933099 \n", "L 736.488143 83.177566 \n", "L 736.560445 82.056231 \n", "L 736.632395 82.623592 \n", "L 736.775257 83.437339 \n", "L 736.846165 83.183622 \n", "L 737.056804 81.933884 \n", "L 737.126008 82.041444 \n", "L 737.537157 84.790237 \n", "L 737.605524 84.070677 \n", "L 737.67386 84.171541 \n", "L 737.878692 85.266306 \n", "L 737.946911 84.829825 \n", "L 738.015102 84.797942 \n", "L 738.083263 84.515817 \n", "L 738.355595 81.403755 \n", "L 738.831026 76.341264 \n", "L 739.034386 76.038074 \n", "L 739.102117 76.08967 \n", "L 739.440322 74.830222 \n", "L 739.710381 76.145892 \n", "L 739.777821 75.757608 \n", "L 739.845234 75.940553 \n", "L 740.181862 78.758401 \n", "L 740.450653 80.1975 \n", "L 740.786017 81.838567 \n", "L 740.853008 81.935386 \n", "L 741.053799 81.185256 \n", "L 741.120666 81.596921 \n", "L 741.187498 81.504668 \n", "L 741.387807 80.634319 \n", "L 741.587836 82.766216 \n", "L 741.65445 83.974533 \n", "L 741.720731 83.935756 \n", "L 741.8529 84.75084 \n", "L 741.918482 84.050677 \n", "L 741.984036 84.309556 \n", "L 742.243834 84.242811 \n", "L 742.372822 84.955791 \n", "L 742.566099 83.991899 \n", "L 742.694815 83.868314 \n", "L 742.759138 83.954794 \n", "L 742.823436 83.622309 \n", "L 742.887702 84.418447 \n", "L 743.080335 83.398113 \n", "L 743.144492 83.241991 \n", "L 743.850922 76.537691 \n", "L 743.915971 76.709977 \n", "L 744.245332 75.284348 \n", "L 744.311722 75.392352 \n", "L 744.378388 75.318331 \n", "L 744.445326 76.152984 \n", "L 744.512235 76.004384 \n", "L 744.579116 76.283685 \n", "L 744.779595 75.502234 \n", "L 745.312614 79.048018 \n", "L 745.378544 79.033624 \n", "L 745.576166 79.543738 \n", "L 745.83929 78.040827 \n", "L 745.905001 78.059347 \n", "L 746.167582 80.175175 \n", "L 746.298707 80.82363 \n", "L 746.756757 85.136501 \n", "L 747.017868 86.97861 \n", "L 747.408735 90.685699 \n", "L 747.66876 91.690114 \n", "L 747.7337 91.691829 \n", "L 747.86351 91.953907 \n", "L 747.993205 91.396232 \n", "L 748.058007 91.651267 \n", "L 748.12278 91.568538 \n", "L 748.187521 91.404783 \n", "L 748.252237 91.783777 \n", "L 748.316927 91.752383 \n", "L 748.38159 91.804963 \n", "L 748.446223 91.619292 \n", "L 748.768984 94.179992 \n", "L 749.026728 94.660718 \n", "L 749.091104 95.191824 \n", "L 749.155451 94.905581 \n", "L 749.219775 94.858021 \n", "L 749.348344 94.998433 \n", "L 749.476814 94.414425 \n", "L 749.541302 94.97204 \n", "L 749.605766 94.947715 \n", "L 749.670494 94.859523 \n", "L 749.800455 95.592636 \n", "L 749.865689 95.560401 \n", "L 749.930895 95.498988 \n", "L 750.06209 94.948485 \n", "L 750.127791 95.062365 \n", "L 750.324722 93.598457 \n", "L 750.586908 92.028577 \n", "L 750.652384 92.927643 \n", "L 750.717835 92.1219 \n", "L 750.783264 92.264182 \n", "L 750.97938 93.119572 \n", "L 751.175248 95.612959 \n", "L 751.24048 95.275253 \n", "L 751.696353 97.287196 \n", "L 751.825791 96.840432 \n", "L 752.148912 97.409856 \n", "L 752.213455 98.247282 \n", "L 752.277685 97.690507 \n", "L 752.406065 98.924995 \n", "L 752.53434 98.832033 \n", "L 752.598439 98.873078 \n", "L 752.66251 98.658164 \n", "L 752.726555 99.007144 \n", "L 752.790574 98.886793 \n", "L 752.854568 98.974945 \n", "L 752.982479 98.254254 \n", "L 753.046397 98.272699 \n", "L 753.110288 98.553723 \n", "L 753.301809 99.737153 \n", "L 753.365598 99.634708 \n", "L 753.684124 103.2014 \n", "L 753.747746 103.044302 \n", "L 754.373498 96.198497 \n", "L 754.558347 97.972706 \n", "L 754.681463 98.778935 \n", "L 754.86594 98.248605 \n", "L 754.927383 98.33713 \n", "L 755.172903 96.606055 \n", "L 755.234225 96.396569 \n", "L 755.418031 97.110613 \n", "L 755.662747 95.529267 \n", "L 755.723866 95.367001 \n", "L 755.784964 95.447706 \n", "L 756.029125 96.615542 \n", "L 756.090101 96.548866 \n", "L 756.151052 96.220716 \n", "L 756.211976 95.245894 \n", "L 756.272875 95.546189 \n", "L 756.333752 95.597674 \n", "L 756.515402 96.443357 \n", "L 756.634984 96.029787 \n", "L 756.812102 95.056887 \n", "L 756.928683 94.986857 \n", "L 756.986523 95.176691 \n", "L 757.044062 94.989387 \n", "L 757.101303 94.977127 \n", "L 757.158242 95.251088 \n", "L 757.441243 93.043113 \n", "L 757.497782 93.478075 \n", "L 757.554305 93.53538 \n", "L 757.610806 93.052114 \n", "L 757.667286 93.264057 \n", "L 758.004893 96.822989 \n", "L 758.060949 96.064503 \n", "L 758.116985 96.125874 \n", "L 758.172996 96.623208 \n", "L 758.228988 96.220202 \n", "L 758.340908 95.979009 \n", "L 758.508645 94.696431 \n", "L 758.564518 94.6303 \n", "L 758.732024 95.011442 \n", "L 758.899351 95.049697 \n", "L 758.955087 94.599669 \n", "L 759.011079 94.747541 \n", "L 759.067053 94.839703 \n", "L 759.402455 92.472097 \n", "L 759.458555 92.679312 \n", "L 759.514911 93.125177 \n", "L 759.742038 91.237351 \n", "L 760.08469 89.003056 \n", "L 760.142451 89.163377 \n", "L 760.316961 86.328062 \n", "L 760.375176 86.360216 \n", "L 760.433367 86.510574 \n", "L 760.491533 86.35443 \n", "L 760.723996 84.216362 \n", "L 760.782057 82.768111 \n", "L 760.840371 83.271263 \n", "L 760.898659 83.208566 \n", "L 761.133222 84.938152 \n", "L 761.370151 85.646381 \n", "L 761.489028 83.594707 \n", "L 761.548429 83.871624 \n", "L 761.667166 83.12562 \n", "L 761.726501 83.2752 \n", "L 761.904373 82.94092 \n", "L 762.02285 84.113432 \n", "L 762.259539 82.669733 \n", "L 762.31866 82.685235 \n", "L 762.377754 82.312931 \n", "L 762.436828 82.617213 \n", "L 762.613119 84.016964 \n", "L 762.786816 84.841345 \n", "L 762.844136 84.743133 \n", "L 763.014368 86.100096 \n", "L 763.347596 88.728202 \n", "L 763.403111 88.59715 \n", "L 763.458876 88.64573 \n", "L 763.514887 88.678347 \n", "L 763.741383 90.502544 \n", "L 763.798619 91.571051 \n", "L 763.8561 91.389083 \n", "L 763.913827 91.191114 \n", "L 764.146912 92.440016 \n", "L 764.379896 95.318879 \n", "L 764.49545 96.154957 \n", "L 764.6681 97.048661 \n", "L 764.840293 97.438296 \n", "L 765.069593 95.963505 \n", "L 765.126868 96.101621 \n", "L 765.184125 95.99975 \n", "L 765.24136 95.811878 \n", "L 765.298577 96.331104 \n", "L 765.355506 95.920743 \n", "L 765.412414 95.860237 \n", "L 765.469037 96.328559 \n", "L 765.525643 95.948238 \n", "L 765.694536 96.359083 \n", "L 765.91862 97.592121 \n", "L 765.974522 97.562377 \n", "L 766.142122 98.625943 \n", "L 766.309558 100.846755 \n", "L 766.365329 100.756074 \n", "L 766.421078 100.216314 \n", "L 766.476808 100.737487 \n", "L 767.155236 105.986646 \n", "L 767.325483 107.889724 \n", "L 767.382199 108.00883 \n", "L 767.665467 112.180926 \n", "L 767.721801 112.134556 \n", "L 767.778114 112.183224 \n", "L 767.890159 111.810162 \n", "L 768.056783 109.64732 \n", "L 768.222447 108.600099 \n", "L 768.277537 108.641437 \n", "L 768.332609 108.838334 \n", "L 768.607664 106.787264 \n", "L 768.662609 107.120087 \n", "L 768.717533 107.02345 \n", "L 768.827315 106.039339 \n", "L 769.101417 109.408987 \n", "L 769.210929 109.017529 \n", "L 769.431244 106.093158 \n", "L 770.114472 100.907759 \n", "L 770.17244 100.790595 \n", "L 770.519766 97.668273 \n", "L 770.693134 96.061558 \n", "L 771.154477 94.30846 \n", "L 771.26961 93.879177 \n", "L 771.384403 93.417729 \n", "L 771.499365 94.224795 \n", "L 771.556813 93.976943 \n", "L 771.783866 92.95475 \n", "L 771.895752 92.68792 \n", "L 772.006542 93.049794 \n", "L 772.061526 92.792551 \n", "L 772.116493 93.212841 \n", "L 772.171187 93.08942 \n", "L 772.225862 92.715942 \n", "L 772.280266 93.072421 \n", "L 772.443363 94.182097 \n", "L 772.497189 93.731717 \n", "L 772.550994 94.471005 \n", "L 772.60453 93.994886 \n", "L 772.763513 94.537114 \n", "L 772.920068 95.849105 \n", "L 772.971713 95.761739 \n", "L 773.023094 95.382901 \n", "L 773.176391 96.367075 \n", "L 773.227458 96.31523 \n", "L 773.27851 96.486688 \n", "L 773.329541 96.38925 \n", "L 773.431556 96.237439 \n", "L 773.584447 96.99886 \n", "L 773.635381 96.946722 \n", "L 773.991479 94.860565 \n", "L 774.143844 93.018987 \n", "L 774.194596 93.159219 \n", "L 774.245332 92.662594 \n", "L 774.2963 93.014268 \n", "L 774.759464 100.897138 \n", "L 774.81126 100.758129 \n", "L 774.966551 101.284716 \n", "L 775.018281 101.20955 \n", "L 775.069996 100.603325 \n", "L 775.121696 100.896297 \n", "L 775.173381 101.114623 \n", "L 775.225049 101.093868 \n", "L 775.534701 98.37517 \n", "L 775.740787 95.679803 \n", "L 775.843732 95.323756 \n", "L 776.357489 99.238945 \n", "L 776.511284 98.76861 \n", "L 776.613727 98.406949 \n", "L 776.920427 100.887996 \n", "L 777.273144 104.356675 \n", "L 777.323396 103.893438 \n", "L 777.373874 104.180822 \n", "L 777.475022 104.368373 \n", "L 777.627474 104.776524 \n", "L 777.678336 104.617899 \n", "L 777.831562 103.052451 \n", "L 777.883092 103.019719 \n", "L 777.934847 102.699026 \n", "L 777.986828 103.136538 \n", "L 778.091462 101.647356 \n", "L 778.144118 101.976492 \n", "L 778.250111 102.199781 \n", "L 778.4648 103.541171 \n", "L 778.518793 103.706863 \n", "L 778.57301 103.563838 \n", "L 778.62745 103.517351 \n", "L 778.682116 103.680301 \n", "L 778.902994 102.328554 \n", "L 778.958767 102.685404 \n", "L 779.127164 101.652538 \n", "L 779.296341 100.52852 \n", "L 779.408293 99.587458 \n", "L 779.463874 99.901087 \n", "L 779.629066 99.872993 \n", "L 779.683852 100.443309 \n", "L 779.738616 99.909514 \n", "L 779.848077 99.090856 \n", "L 779.902775 99.173659 \n", "L 779.957452 99.131442 \n", "L 780.06675 98.826757 \n", "L 780.175977 99.425375 \n", "L 780.230561 98.854342 \n", "L 780.394199 100.797666 \n", "L 780.503204 100.65471 \n", "L 780.557675 101.46364 \n", "L 780.612127 101.391534 \n", "L 780.666561 100.98731 \n", "L 780.829748 102.247273 \n", "L 780.884103 102.04189 \n", "L 781.047053 101.190836 \n", "L 781.101332 101.2285 \n", "L 781.155596 101.43155 \n", "L 781.209838 101.238391 \n", "L 781.318267 101.227595 \n", "L 781.426622 101.920877 \n", "L 781.902644 97.384097 \n", "L 782.158197 99.445961 \n", "L 782.259356 100.841483 \n", "L 782.30991 100.581071 \n", "L 782.612869 98.394795 \n", "L 782.764139 95.155652 \n", "L 782.814536 95.237781 \n", "L 783.116572 97.640953 \n", "L 783.217107 97.455245 \n", "L 783.318277 97.178069 \n", "L 783.572432 94.886704 \n", "L 783.724877 93.229333 \n", "L 783.775656 93.049142 \n", "L 783.82642 93.586163 \n", "L 783.877165 93.195153 \n", "L 784.029302 92.782018 \n", "L 784.231921 94.767699 \n", "L 784.282301 94.309838 \n", "L 784.332662 94.518955 \n", "L 784.432872 96.452734 \n", "L 784.482719 96.218904 \n", "L 784.729591 94.592014 \n", "L 784.778632 94.543297 \n", "L 784.925665 93.601634 \n", "L 784.974646 93.441164 \n", "L 785.023616 93.723285 \n", "L 785.072567 93.461934 \n", "L 785.121504 93.315567 \n", "L 785.170426 93.983282 \n", "L 785.219334 93.289207 \n", "L 785.317104 93.025155 \n", "L 785.366199 93.18205 \n", "L 785.514087 93.908292 \n", "L 785.613761 94.951285 \n", "L 785.663924 94.64477 \n", "L 785.866472 93.325604 \n", "L 785.91724 93.494782 \n", "L 785.967991 93.531189 \n", "L 786.12016 92.480524 \n", "L 786.170852 92.58963 \n", "L 786.221526 92.756686 \n", "L 786.322817 91.643903 \n", "L 786.575735 94.705732 \n", "L 786.777775 95.909527 \n", "L 786.828244 95.62021 \n", "L 786.929134 95.018254 \n", "L 786.979557 95.089988 \n", "L 787.231209 93.549016 \n", "L 787.479227 90.715045 \n", "L 787.528325 90.815886 \n", "L 787.577408 90.897456 \n", "L 787.626473 91.213145 \n", "L 787.724564 91.180278 \n", "L 787.822594 91.530506 \n", "L 788.068535 94.083865 \n", "L 788.117677 94.489647 \n", "L 788.456457 91.958238 \n", "L 788.551075 91.787509 \n", "L 788.880988 97.714577 \n", "L 788.928057 98.30976 \n", "L 788.975114 97.534727 \n", "L 789.022158 97.649654 \n", "L 789.069189 97.600241 \n", "L 789.304133 95.87114 \n", "L 789.351081 95.907922 \n", "L 789.628175 97.564516 \n", "L 789.673674 97.694736 \n", "L 789.763954 97.022643 \n", "L 789.808739 97.251675 \n", "L 789.897595 97.425546 \n", "L 790.289499 95.656816 \n", "L 790.503327 96.633328 \n", "L 790.545656 96.358357 \n", "L 790.629831 96.217954 \n", "L 790.882064 93.875283 \n", "L 791.427176 91.099661 \n", "L 791.636343 92.69959 \n", "L 791.845207 94.830993 \n", "L 792.053775 92.830339 \n", "L 792.137121 92.87417 \n", "L 792.178778 92.888237 \n", "L 792.220424 92.607759 \n", "L 792.262057 92.79943 \n", "L 792.428474 93.699211 \n", "L 792.553153 94.785523 \n", "L 792.678617 97.150919 \n", "L 792.720785 97.000445 \n", "L 792.76316 96.991632 \n", "L 792.934756 97.787596 \n", "L 793.197937 101.097845 \n", "L 793.69821 106.196508 \n", "L 793.791853 105.904882 \n", "L 793.979404 103.735635 \n", "L 794.166706 102.134558 \n", "L 794.213492 102.403007 \n", "L 794.260264 102.224148 \n", "L 794.353771 102.400493 \n", "L 794.493932 103.210334 \n", "L 794.587312 104.01576 \n", "L 794.6342 103.939116 \n", "L 794.823582 104.735677 \n", "L 795.161684 101.852574 \n", "L 795.308533 101.959218 \n", "L 795.503827 103.633567 \n", "L 795.552612 103.294489 \n", "L 795.845009 102.653475 \n", "L 795.893686 102.027903 \n", "L 796.135797 104.328719 \n", "L 796.183523 104.267068 \n", "L 796.231018 104.807499 \n", "L 796.278283 104.037547 \n", "L 796.325316 104.297635 \n", "L 796.418696 104.698956 \n", "L 796.465043 104.633882 \n", "L 796.557052 104.606548 \n", "L 796.785224 106.032826 \n", "L 796.876392 107.267568 \n", "L 796.967504 107.254302 \n", "L 797.013039 107.217607 \n", "L 797.149566 105.629118 \n", "L 797.331436 103.93939 \n", "L 797.376874 104.148277 \n", "L 797.422297 103.970011 \n", "L 797.649234 102.233166 \n", "L 797.875857 101.059618 \n", "L 797.921142 101.455939 \n", "L 798.011678 101.369378 \n", "L 798.056928 101.147792 \n", "L 798.102166 101.455125 \n", "L 798.147181 101.497999 \n", "L 798.280866 103.836778 \n", "L 798.669733 108.469462 \n", "L 798.753888 109.145426 \n", "L 798.795946 108.665658 \n", "L 798.837994 108.446527 \n", "L 799.00607 107.16166 \n", "L 799.299774 104.742884 \n", "L 799.341686 104.986319 \n", "L 799.383588 105.07869 \n", "L 799.509225 106.475325 \n", "L 799.634785 106.315921 \n", "L 799.718439 107.360011 \n", "L 799.885608 108.876335 \n", "L 800.052566 109.818165 \n", "L 800.219334 110.756281 \n", "L 800.511374 113.580156 \n", "L 800.55317 112.836518 \n", "L 800.595167 112.992622 \n", "L 800.935156 115.174141 \n", "L 801.147667 115.790172 \n", "L 801.189923 115.391234 \n", "L 801.232166 115.733158 \n", "L 801.486202 118.831825 \n", "L 801.528781 118.610863 \n", "L 801.614115 118.499859 \n", "L 801.700016 119.111345 \n", "L 801.742945 118.800512 \n", "L 801.786071 118.719347 \n", "L 802.128968 115.425043 \n", "L 802.171779 114.89823 \n", "L 802.257157 116.254671 \n", "L 802.299721 115.922804 \n", "L 802.427128 115.615797 \n", "L 802.469439 115.660752 \n", "L 802.55382 114.502142 \n", "L 802.595893 115.168319 \n", "L 802.764052 116.690427 \n", "L 802.806062 117.231992 \n", "L 802.890044 116.90168 \n", "L 802.93202 117.087685 \n", "L 802.973985 116.783082 \n", "L 803.140691 115.619839 \n", "L 803.34803 113.407793 \n", "L 803.389878 113.890422 \n", "L 803.43192 113.68068 \n", "L 803.688266 111.14335 \n", "L 803.731673 110.897605 \n", "L 803.906015 108.276972 \n", "L 804.12346 109.71086 \n", "L 804.378678 112.067797 \n", "L 804.503367 113.326677 \n", "L 804.544498 112.856828 \n", "L 804.585415 113.138749 \n", "L 804.666604 113.810836 \n", "L 804.70708 113.639917 \n", "L 804.788005 113.100827 \n", "L 805.313034 106.484059 \n", "L 805.433918 106.449408 \n", "L 805.674159 107.800258 \n", "L 805.875276 106.718557 \n", "L 806.505921 101.994687 \n", "L 806.679322 102.699633 \n", "L 806.722696 102.573937 \n", "L 806.766056 102.758442 \n", "L 806.809204 102.831101 \n", "L 806.852341 102.425087 \n", "L 806.937976 102.573562 \n", "L 806.980474 102.597484 \n", "L 807.106691 103.244745 \n", "L 807.313399 105.001124 \n", "L 807.395769 105.05781 \n", "L 807.478494 104.622017 \n", "L 807.561367 105.283248 \n", "L 807.602886 104.609371 \n", "L 807.685692 104.685538 \n", "L 807.767659 105.006064 \n", "L 807.808325 104.675194 \n", "L 807.84878 105.027275 \n", "L 807.889024 105.148953 \n", "L 807.929059 104.718207 \n", "L 807.968885 105.143429 \n", "L 808.008697 105.252138 \n", "L 808.244982 107.845716 \n", "L 808.324064 107.496099 \n", "L 808.442603 105.837007 \n", "L 808.482097 105.909824 \n", "L 808.718849 104.589346 \n", "L 808.561054 106.011095 \n", "L 808.758273 104.641159 \n", "L 808.797688 104.823115 \n", "L 808.837091 104.55419 \n", "L 808.876482 104.752229 \n", "L 809.034153 103.193594 \n", "L 809.153944 102.353972 \n", "L 809.194252 102.50175 \n", "L 809.357361 101.931524 \n", "L 809.398607 101.961479 \n", "L 809.440038 102.526696 \n", "L 809.481656 102.189093 \n", "L 809.775245 100.915792 \n", "L 809.81717 100.975028 \n", "L 809.859085 101.84772 \n", "L 809.900792 101.6842 \n", "L 809.942293 100.77308 \n", "L 809.983585 101.468494 \n", "L 810.02467 101.46262 \n", "L 810.065546 101.294926 \n", "L 810.226975 102.62215 \n", "L 810.266814 102.444765 \n", "L 810.306446 102.300374 \n", "L 810.657976 104.432636 \n", "L 810.69692 104.401264 \n", "L 810.813688 105.839766 \n", "L 811.163557 109.095432 \n", "L 811.202388 108.118975 \n", "L 811.280018 108.362647 \n", "L 811.357615 108.423784 \n", "L 811.396401 108.319886 \n", "L 811.435177 107.886856 \n", "L 811.512693 108.06303 \n", "L 811.551633 108.096043 \n", "L 811.709243 109.277113 \n", "L 811.749109 109.765966 \n", "L 811.829396 109.620821 \n", "L 811.869817 109.782488 \n", "L 811.910423 109.643108 \n", "L 811.951214 109.468594 \n", "L 811.992188 109.47973 \n", "L 812.033345 109.749458 \n", "L 812.074683 109.426278 \n", "L 812.198633 108.64611 \n", "L 812.404985 110.618229 \n", "L 812.487637 110.116637 \n", "L 812.739765 106.931103 \n", "L 812.824357 105.921721 \n", "L 813.07766 101.392429 \n", "L 813.407695 97.772816 \n", "L 813.530543 98.132071 \n", "L 813.571468 98.064893 \n", "L 813.694178 99.425355 \n", "L 813.77594 99.097959 \n", "L 813.816803 99.061928 \n", "L 813.857655 99.136169 \n", "L 813.939323 99.547746 \n", "L 814.020942 100.766004 \n", "L 814.061736 100.728676 \n", "L 814.102518 100.730558 \n", "L 814.387141 101.891718 \n", "L 814.66375 103.101832 \n", "L 814.702458 102.87267 \n", "L 814.741156 103.30713 \n", "L 814.896037 104.475561 \n", "L 814.935066 104.253498 \n", "L 815.173067 102.737792 \n", "L 815.213367 102.954706 \n", "L 815.72145 91.829918 \n", "L 816.126796 87.751295 \n", "L 816.197932 88.223051 \n", "L 816.442592 89.865861 \n", "L 816.477432 89.754573 \n", "L 816.82142 93.463082 \n", "L 816.986633 95.742576 \n", "L 817.019091 95.542547 \n", "L 817.051352 95.564791 \n", "L 817.211632 97.353386 \n", "L 817.24367 97.307512 \n", "L 818.00703 106.274451 \n", "L 818.039278 106.092566 \n", "L 818.071329 106.222404 \n", "L 818.135033 107.398956 \n", "L 818.166878 107.006159 \n", "L 818.452636 101.76846 \n", "L 818.73056 97.047633 \n", "L 818.979479 95.774017 \n", "L 819.011149 95.788824 \n", "L 819.106107 97.323197 \n", "L 819.657662 108.768195 \n", "L 819.691607 108.659554 \n", "L 819.759847 108.746428 \n", "L 819.863264 110.325044 \n", "L 820.002528 113.209736 \n", "L 820.037326 112.856334 \n", "L 820.072117 113.548055 \n", "L 820.106899 113.580705 \n", "L 820.211208 114.849468 \n", "L 820.280525 114.255283 \n", "L 820.314893 114.183769 \n", "L 820.587014 110.067165 \n", "L 820.621322 109.922137 \n", "L 820.690471 110.486783 \n", "L 820.725309 110.05682 \n", "L 820.866417 107.829949 \n", "L 820.938029 108.018626 \n", "L 821.01035 108.85286 \n", "L 821.08319 108.6283 \n", "L 821.228773 109.335633 \n", "L 821.446884 106.792582 \n", "L 821.555821 107.457066 \n", "L 821.591933 107.34 \n", "L 821.663579 106.518614 \n", "L 821.735009 106.598771 \n", "L 821.770711 107.239039 \n", "L 821.806403 106.471993 \n", "L 821.842088 106.402457 \n", "L 821.949094 105.475581 \n", "L 821.984745 105.868883 \n", "L 822.056025 105.545016 \n", "L 822.091836 105.600683 \n", "L 822.200141 103.858069 \n", "L 822.308555 104.32237 \n", "L 822.672094 108.611428 \n", "L 822.746518 108.289424 \n", "L 822.783988 108.032381 \n", "L 822.859448 109.296668 \n", "L 822.9356 108.887007 \n", "L 822.974117 109.301393 \n", "L 823.012803 109.051841 \n", "L 823.05148 108.452096 \n", "L 823.128808 108.646066 \n", "L 823.167461 109.176857 \n", "L 823.244743 109.099185 \n", "L 823.36042 107.229802 \n", "L 823.743087 100.42508 \n", "L 823.819357 100.411137 \n", "L 823.895596 100.538725 \n", "L 824.009888 99.808245 \n", "L 824.086034 99.989468 \n", "L 824.124094 99.800229 \n", "L 824.162143 100.153778 \n", "L 824.238213 100.771024 \n", "L 824.276238 100.219389 \n", "L 824.507976 100.761022 \n", "L 824.586578 100.146548 \n", "L 824.825546 97.527539 \n", "L 824.865576 97.872976 \n", "L 824.905416 97.695388 \n", "L 825.331144 92.876527 \n", "L 825.703439 89.711905 \n", "L 825.740565 89.747398 \n", "L 825.777684 90.158 \n", "L 825.851902 90.067647 \n", "L 825.888998 90.033181 \n", "L 825.963171 90.015983 \n", "L 826.000244 90.235201 \n", "L 826.186539 88.950593 \n", "L 826.22427 89.147453 \n", "L 826.262168 88.795658 \n", "L 826.300236 88.706633 \n", "L 826.415444 89.313844 \n", "L 826.454186 89.08768 \n", "L 826.493097 89.036769 \n", "L 826.493097 89.036769 \n", "\" clip-path=\"url(#p241e7f9d2a)\" style=\"fill: none; stroke: #c44e52; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 645.804188 184.796098 \n", "L 645.804188 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 645.804187 184.796098 \n", "L 832.579462 184.796098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p05cc6b3f70\">\n", "   <rect x=\"90.969938\" y=\"11.588098\" width=\"186.775275\" height=\"173.208\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa3fd31ed85\">\n", "   <rect x=\"368.387063\" y=\"11.588098\" width=\"186.775275\" height=\"173.208\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p241e7f9d2a\">\n", "   <rect x=\"645.804188\" y=\"11.588098\" width=\"186.775275\" height=\"173.208\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x350 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1, 3, figsize=(12, 3.5))\n", "axs[0].plot(global_step, goal_achieved, color='b', linewidth=2)\n", "axs[0].grid(True, color='lightgrey', alpha=0.3)\n", "axs[0].set_xlabel(\"Global step\", labelpad=10)\n", "axs[0].set_ylabel(\"Goal achieved [%]\")\n", "axs[0].set_yscale('log')\n", "axs[0].set_xscale('log')\n", "axs[0].set_xlim([3e8, 1e9])\n", "axs[0].set_ylim([97, 100])\n", "plt.gca().yaxis.set_major_formatter(FuncFormatter(format_func))\n", "\n", "axs[1].plot(global_step, offroad, color='orange', linewidth=2)\n", "axs[1].grid(True, color='lightgrey', alpha=0.3)\n", "axs[1].set_xlabel(\"Global step\", labelpad=10)\n", "axs[1].set_ylabel(\"Off-road [%]\")\n", "axs[1].set_yscale('log')\n", "axs[1].set_xscale('log')\n", "axs[1].set_xlim([3e8, 1e9])\n", "\n", "\n", "axs[2].plot(global_step, collided, color='r', linewidth=2)\n", "axs[2].grid(True, color='lightgrey', alpha=0.3)\n", "axs[2].set_xlabel(\"Global step\", labelpad=10)\n", "axs[2].set_ylabel(\"Collided [%]\")\n", "axs[2].set_yscale('log')\n", "axs[2].set_xscale('log')\n", "axs[2].set_xlim([3e8, 1e9])\n", "axs[2].set_ylim([2, 4])\n", "\n", "sns.despine()\n", "plt.tight_layout()\n", "plt.savefig(f'loglog_train_performance.pdf', bbox_inches='tight', format='pdf')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "gdrive", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}