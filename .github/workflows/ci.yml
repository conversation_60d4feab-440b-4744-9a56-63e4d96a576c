name: Continuous Integration

on:
  push:
    branches: [main]
  pull_request:

jobs:
  build-and-smoketest:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build Docker image
        run: docker build --tag gpudrive:latest .

      - name: Run smoke test inside Docker container
        run: |
          docker run --rm gpudrive:latest /bin/bash -c "
            echo 'Dummy cuda' \
            && ln -s /usr/local/cuda/lib64/stubs/libcuda.so /usr/local/cuda/lib64/stubs/libcuda.so.1 \
            && export LD_LIBRARY_PATH=/usr/local/cuda/lib64/stubs/:$LD_LIBRARY_PATH \
            && echo 'Modifications to run without extra data' \
            && sed -i 's|train_path: \"data/processed/training\"|train_path: \"data/processed/examples\"|g' examples/experimental/config/visualization_config.yaml \
            && sed -i '/# Load policy/{N;N;N;N;N;N;s|# Load policy\n    policy = load_policy(\n        path_to_cpt=config.cpt_path,\n        model_name=config.cpt_name,\n        device=config.device,\n        env=env,\n    )|from gpudrive.networks.late_fusion import NeuralNet\n    policy = NeuralNet.from_pretrained(\"daphne-cornelisse/policy_S10_000_02_27\")|}' examples/experimental/viz_rollouts.py \
            && echo 'Modifications to run without GPU' \
            && sed -i 's/device=\"cuda\"/device=\"cpu\"/g' gpudrive/datatypes/observation.py \
            && python examples/experimental/viz_rollouts.py
          "
