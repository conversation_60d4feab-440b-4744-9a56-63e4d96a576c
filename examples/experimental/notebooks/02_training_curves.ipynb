{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Dependencies\n", "import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "import warnings\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import datetime\n", "\n", "sns.set(\"notebook\", font_scale=1.05, rc={\"figure.figsize\": (10, 5)})\n", "sns.set_style(\"ticks\", rc={\"figure.facecolor\": \"none\", \"axes.facecolor\": \"none\"})\n", "%config InlineBackend.figure_format = 'svg'\n", "warnings.filterwarnings(\"ignore\")\n", "plt.set_loglevel(\"WARNING\")\n", "mpl.rcParams[\"lines.markersize\"] = 8\n", "\n", "# generate datetime string\n", "now = datetime.datetime.now()\n", "now_str = now.strftime(\"%m_%d_%H_%M\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["WINDOW_SIZE = 250\n", "\n", "PATH = \"...\"\n", "\n", "# Load data\n", "df_goal = pd.read_csv(PATH + \"perc_goal_uptime.csv\")\n", "df_collisions = pd.read_csv(PATH + \"perc_collisions_step.csv\")\n", "df_reward = pd.read_csv(PATH + \"mean_rew_step.csv\")\n", "\n", "df_goal"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["global_step = df_reward['global_step'].values\n", "time = df_goal['performance/uptime'].values / 3600\n", "\n", "reward = df_reward['PPO____S_1000__02_26_08_54_58_289 - metrics/mean_episode_reward_per_agent'].rolling(window=WINDOW_SIZE).mean().values\n", "offroad = df_collisions['PPO____S_1000__02_26_08_54_58_289 - metrics/perc_off_road'].rolling(window=WINDOW_SIZE).mean().values * 100\n", "collision = df_collisions['PPO____S_1000__02_26_08_54_58_289 - metrics/perc_veh_collisions'].rolling(window=WINDOW_SIZE).mean().values * 100\n", "goal_achieved = df_goal['PPO____S_1000__02_26_08_54_58_289 - metrics/perc_goal_achieved'].rolling(window=WINDOW_SIZE).mean().values * 100\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"850.821119pt\" height=\"238.86477pt\" viewBox=\"0 0 850.**********.86477\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-02-27T11:52:47.530985</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.0, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 238.86477 \n", "L 850.**********.86477 \n", "L 850.821119 0 \n", "L 0 0 \n", "L 0 238.86477 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 58.**********.792098 \n", "L 278.**********.792098 \n", "L 278.817234 11.588098 \n", "L 58.611234 11.588098 \n", "L 58.**********.792098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 58.**********.792098 \n", "L 58.611234 11.588098 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m96752845a8\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"58.611234\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(54.936891 207.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 104.487484 188.792098 \n", "L 104.487484 11.588098 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"104.487484\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(100.813141 207.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 150.363734 188.792098 \n", "L 150.363734 11.588098 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"150.363734\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 10 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(143.015047 207.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 196.239984 188.792098 \n", "L 196.239984 11.588098 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"196.239984\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 15 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(188.891297 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 242.116234 188.792098 \n", "L 242.116234 11.588098 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"242.116234\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(234.767547 207.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- Wall-clock-time [h] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(109.654687 229.044363) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-57\" d=\"M 213 4666 \n", "L 850 4666 \n", "L 1831 722 \n", "L 2809 4666 \n", "L 3519 4666 \n", "L 4500 722 \n", "L 5478 4666 \n", "L 6119 4666 \n", "L 4947 0 \n", "L 4153 0 \n", "L 3169 4050 \n", "L 2175 0 \n", "L 1381 0 \n", "L 213 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5b\" d=\"M 550 4863 \n", "L 1875 4863 \n", "L 1875 4416 \n", "L 1125 4416 \n", "L 1125 -397 \n", "L 1875 -397 \n", "L 1875 -844 \n", "L 550 -844 \n", "L 550 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5d\" d=\"M 1947 4863 \n", "L 1947 -844 \n", "L 622 -844 \n", "L 622 -397 \n", "L 1369 -397 \n", "L 1369 4416 \n", "L 622 4416 \n", "L 622 4863 \n", "L 1947 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-57\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"92.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"153.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"181.564453\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"209.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"245.431641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"300.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"328.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"389.376953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"444.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"502.267578\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"538.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"577.560547\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"605.34375\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"702.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"764.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"796.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"835.080078\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"898.458984\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 58.611234 180.353812 \n", "L 278.817234 180.353812 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <defs>\n", "       <path id=\"mb74aca4dfb\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"58.611234\" y=\"180.353812\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394438 184.74191) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 58.611234 138.162383 \n", "L 278.817234 138.162383 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"58.611234\" y=\"138.162383\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.85 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394438 142.550481) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 58.611234 95.970955 \n", "L 278.817234 95.970955 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"58.611234\" y=\"95.970955\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.90 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394438 100.359052) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 58.611234 53.779526 \n", "L 278.817234 53.779526 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"58.611234\" y=\"53.779526\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.95 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394438 58.167624) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 58.611234 11.588098 \n", "L 278.817234 11.588098 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"58.611234\" y=\"11.588098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.00 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394438 15.976195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- Average reward -->\n", "     <g style=\"fill: #262626\" transform=\"translate(16.774031 149.880363) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-41\" d=\"M 2188 4044 \n", "L 1331 1722 \n", "L 3047 1722 \n", "L 2188 4044 \n", "z\n", "M 1831 4666 \n", "L 2547 4666 \n", "L 4325 0 \n", "L 3669 0 \n", "L 3244 1197 \n", "L 1141 1197 \n", "L 716 0 \n", "L 50 0 \n", "L 1831 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-41\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"62.533203\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"121.712891\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"183.236328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"224.349609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"285.628906\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"349.105469\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"410.628906\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"442.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"481.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"542.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"624.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"685.869141\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"725.232422\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_21\">\n", "    <path d=\"M 67.442037 114.354445 \n", "L 67.56645 113.205689 \n", "L 67.772107 111.803516 \n", "L 67.794033 111.986521 \n", "L 67.999902 113.262104 \n", "L 68.288991 116.380558 \n", "L 68.621831 119.713493 \n", "L 68.811019 122.066615 \n", "L 69.569602 129.091956 \n", "L 69.673741 130.02427 \n", "L 69.928501 132.584395 \n", "L 70.208162 134.93071 \n", "L 70.316377 135.511249 \n", "L 71.089264 143.562224 \n", "L 71.218326 144.362815 \n", "L 71.460252 145.443993 \n", "L 71.526033 145.278142 \n", "L 71.54958 145.479447 \n", "L 71.614986 145.528397 \n", "L 71.68078 145.79151 \n", "L 71.770575 146.156467 \n", "L 71.794268 145.950251 \n", "L 72.446817 148.412901 \n", "L 72.608362 150.094649 \n", "L 72.652325 150.274822 \n", "L 72.789782 151.841034 \n", "L 73.831717 157.803257 \n", "L 73.855069 157.775944 \n", "L 73.924559 158.084944 \n", "L 73.947707 157.48361 \n", "L 74.040518 157.531789 \n", "L 74.088259 157.29272 \n", "L 74.15655 157.352326 \n", "L 74.180503 157.50577 \n", "L 74.204062 157.462203 \n", "L 74.318223 156.664009 \n", "L 74.340925 156.755336 \n", "L 74.364575 156.553029 \n", "L 74.433833 156.847223 \n", "L 74.527291 157.296527 \n", "L 74.716826 158.267733 \n", "L 74.928428 158.938637 \n", "L 74.950828 158.625131 \n", "L 75.022274 159.014161 \n", "L 75.045098 159.044538 \n", "L 75.067846 158.825418 \n", "L 75.115376 159.168447 \n", "L 75.326312 160.179378 \n", "L 75.34976 160.010617 \n", "L 75.372027 160.28932 \n", "L 75.535938 161.836472 \n", "L 75.603996 161.76273 \n", "L 75.813987 162.519258 \n", "L 75.907386 162.834234 \n", "L 75.954997 162.628596 \n", "L 75.977604 162.387659 \n", "L 76.048794 162.780497 \n", "L 76.304656 164.290766 \n", "L 76.328876 164.341872 \n", "L 76.49286 165.145561 \n", "L 76.705092 165.659856 \n", "L 76.728788 165.578727 \n", "L 76.753011 165.805212 \n", "L 76.777125 165.766718 \n", "L 76.940895 166.294705 \n", "L 77.297547 167.357382 \n", "L 77.345041 167.500188 \n", "L 77.416828 167.437123 \n", "L 77.463491 167.418779 \n", "L 77.581955 167.941671 \n", "L 77.653753 167.812314 \n", "L 77.699852 167.862143 \n", "L 78.124749 170.705939 \n", "L 78.456081 173.327858 \n", "L 78.621387 173.505407 \n", "L 78.644672 173.355443 \n", "L 78.692844 173.74602 \n", "L 78.787468 174.364111 \n", "L 78.9309 174.135411 \n", "L 78.95412 174.138851 \n", "L 78.977595 173.796696 \n", "L 79.073125 173.815227 \n", "L 79.145899 173.782421 \n", "L 79.168825 173.914984 \n", "L 79.310269 173.516696 \n", "L 79.381818 173.47266 \n", "L 79.690634 172.131487 \n", "L 79.832701 172.113075 \n", "L 79.856776 171.971824 \n", "L 79.904765 172.21041 \n", "L 79.928199 172.228778 \n", "L 80.259594 169.449369 \n", "L 80.2835 169.427701 \n", "L 82.011295 150.480221 \n", "L 82.509142 145.335421 \n", "L 84.302389 127.588587 \n", "L 84.5845 124.011277 \n", "L 86.282613 108.16011 \n", "L 86.521337 106.427282 \n", "L 86.992932 103.763069 \n", "L 87.041034 103.616232 \n", "L 87.207184 103.423217 \n", "L 87.255002 103.641723 \n", "L 87.325938 103.501414 \n", "L 87.466835 103.423886 \n", "L 87.490205 103.485622 \n", "L 87.538331 103.374989 \n", "L 87.584542 103.312939 \n", "L 87.63298 103.411624 \n", "L 87.656173 103.352 \n", "L 87.773932 103.315205 \n", "L 87.797466 103.406813 \n", "L 87.868789 103.347713 \n", "L 88.010463 103.088188 \n", "L 88.152631 102.756582 \n", "L 89.143347 100.271313 \n", "L 89.166713 100.364167 \n", "L 89.214864 100.268326 \n", "L 89.473476 99.75132 \n", "L 89.497553 99.780216 \n", "L 89.568319 99.693929 \n", "L 89.804956 99.184064 \n", "L 89.969249 98.548042 \n", "L 90.441307 97.650784 \n", "L 90.724151 97.061794 \n", "L 90.794464 96.863334 \n", "L 91.030629 96.31469 \n", "L 91.054034 96.407034 \n", "L 91.148746 96.386802 \n", "L 91.196553 96.53807 \n", "L 91.266866 96.420739 \n", "L 91.433106 96.105386 \n", "L 91.480286 96.182015 \n", "L 91.527616 96.08072 \n", "L 91.669078 95.807015 \n", "L 91.740005 95.876215 \n", "L 91.763541 95.926772 \n", "L 91.811631 95.831349 \n", "L 91.929263 95.630662 \n", "L 92.023537 95.514443 \n", "L 92.070173 95.554502 \n", "L 92.188959 95.334957 \n", "L 92.353316 94.934784 \n", "L 92.518107 94.7912 \n", "L 92.589586 94.860884 \n", "L 92.612708 94.808492 \n", "L 92.753671 94.625573 \n", "L 92.824248 94.723799 \n", "L 92.918715 94.651488 \n", "L 93.107962 94.170744 \n", "L 93.131345 93.876982 \n", "L 93.226187 93.931098 \n", "L 93.510872 94.053595 \n", "L 93.700038 93.470293 \n", "L 93.723297 93.532405 \n", "L 93.770564 93.44603 \n", "L 93.960491 93.050211 \n", "L 94.008819 93.087064 \n", "L 94.07933 93.31907 \n", "L 94.12622 93.191187 \n", "L 94.243428 92.799373 \n", "L 94.267175 92.841003 \n", "L 94.385751 92.957798 \n", "L 94.432101 92.81364 \n", "L 94.502741 92.90878 \n", "L 94.596582 92.849901 \n", "L 94.573432 92.976229 \n", "L 94.619766 92.865951 \n", "L 94.667316 92.927403 \n", "L 94.690736 92.8337 \n", "L 94.831515 92.569593 \n", "L 94.761256 92.861085 \n", "L 94.855595 92.575897 \n", "L 95.208674 91.924382 \n", "L 95.232424 91.947481 \n", "L 95.256756 92.049174 \n", "L 95.35088 92.012397 \n", "L 95.609593 91.54743 \n", "L 95.633607 91.611926 \n", "L 96.129837 92.5134 \n", "L 96.154209 92.49785 \n", "L 96.177274 92.418597 \n", "L 96.248733 92.503926 \n", "L 96.29538 92.473092 \n", "L 96.318811 92.594337 \n", "L 96.531854 93.024911 \n", "L 96.626346 93.045485 \n", "L 96.791943 93.36241 \n", "L 96.839678 93.323491 \n", "L 97.099004 92.917224 \n", "L 97.241556 92.985512 \n", "L 97.264511 92.928376 \n", "L 97.382208 92.93957 \n", "L 97.429993 93.068555 \n", "L 97.499928 92.992229 \n", "L 97.547772 93.106747 \n", "L 97.736323 92.837528 \n", "L 97.876942 93.05139 \n", "L 98.253245 93.712289 \n", "L 98.41857 94.167411 \n", "L 98.441678 94.125996 \n", "L 98.46543 94.224228 \n", "L 98.654274 94.766562 \n", "L 98.913671 94.709013 \n", "L 99.031722 95.027236 \n", "L 99.079714 94.959834 \n", "L 99.174244 94.876388 \n", "L 99.220628 94.962787 \n", "L 99.268477 95.036128 \n", "L 99.291698 94.934879 \n", "L 99.409817 94.715791 \n", "L 99.481419 94.796269 \n", "L 99.599343 94.956041 \n", "L 99.694646 94.822542 \n", "L 99.717869 94.860294 \n", "L 99.741532 94.871154 \n", "L 99.765668 94.816658 \n", "L 99.929733 94.501025 \n", "L 99.977225 94.427105 \n", "L 100.047825 94.513217 \n", "L 100.236588 94.512269 \n", "L 100.330907 94.505407 \n", "L 100.590633 94.069819 \n", "L 100.897359 93.939965 \n", "L 101.133828 94.165356 \n", "L 101.15681 94.052807 \n", "L 101.376648 93.618984 \n", "L 101.470733 93.482734 \n", "L 101.517922 93.532889 \n", "L 101.542423 93.618233 \n", "L 101.588526 93.47353 \n", "L 101.659795 93.29885 \n", "L 101.706507 93.412687 \n", "L 102.62418 91.857323 \n", "L 102.647782 91.905784 \n", "L 102.695287 91.775716 \n", "L 102.835921 91.38054 \n", "L 103.165735 90.841428 \n", "L 103.188918 90.868844 \n", "L 103.236838 90.80415 \n", "L 103.354763 90.358685 \n", "L 103.425371 90.396202 \n", "L 103.448662 90.419035 \n", "L 103.495016 90.367048 \n", "L 103.706938 90.216979 \n", "L 103.776882 90.138285 \n", "L 103.988425 89.585965 \n", "L 104.340222 88.183654 \n", "L 104.387198 88.143444 \n", "L 104.504415 87.780335 \n", "L 104.528158 87.847312 \n", "L 105.821683 85.415503 \n", "L 105.845734 85.5298 \n", "L 105.869165 85.402357 \n", "L 105.939863 85.447379 \n", "L 106.19796 85.078904 \n", "L 106.292014 85.015594 \n", "L 106.43324 84.813551 \n", "L 106.504057 84.77575 \n", "L 106.550843 84.8332 \n", "L 106.880666 85.489507 \n", "L 106.99808 85.255202 \n", "L 107.021178 85.312072 \n", "L 107.068789 85.430473 \n", "L 107.138738 85.353979 \n", "L 107.209013 85.259547 \n", "L 107.256556 85.347531 \n", "L 107.350669 85.302033 \n", "L 107.420535 85.105794 \n", "L 107.490653 85.162478 \n", "L 107.866518 85.181496 \n", "L 108.078044 85.378675 \n", "L 108.218567 85.259374 \n", "L 108.383119 85.102084 \n", "L 108.406337 85.14793 \n", "L 108.429794 85.071983 \n", "L 108.523677 84.862422 \n", "L 108.570714 84.895865 \n", "L 108.593972 84.920519 \n", "L 108.641471 84.873662 \n", "L 108.664632 84.801115 \n", "L 108.711438 84.900362 \n", "L 108.875928 85.181278 \n", "L 109.064559 84.951043 \n", "L 109.135287 84.980117 \n", "L 109.158609 85.084324 \n", "L 109.229435 84.936381 \n", "L 109.252677 85.002649 \n", "L 109.55811 84.436823 \n", "L 109.675562 84.326771 \n", "L 109.721951 84.378894 \n", "L 109.745974 84.325761 \n", "L 109.769261 84.206574 \n", "L 109.863024 84.29244 \n", "L 110.14563 84.58897 \n", "L 110.192645 84.472087 \n", "L 110.968681 84.181835 \n", "L 111.039281 84.20813 \n", "L 111.062957 84.184414 \n", "L 111.180427 84.133138 \n", "L 111.203855 84.178574 \n", "L 111.369128 84.258033 \n", "L 111.39266 84.37988 \n", "L 111.463463 84.227191 \n", "L 111.510776 84.112089 \n", "L 111.534124 83.965968 \n", "L 111.628207 84.014589 \n", "L 111.698934 84.065281 \n", "L 111.722391 83.946407 \n", "L 111.816469 83.875253 \n", "L 111.839606 83.942065 \n", "L 111.933611 84.239325 \n", "L 112.004108 84.087835 \n", "L 112.23919 84.355687 \n", "L 112.380721 84.588088 \n", "L 112.592468 84.043204 \n", "L 112.616276 84.070262 \n", "L 112.757614 83.693942 \n", "L 112.780869 83.760447 \n", "L 112.804719 83.864689 \n", "L 112.874955 83.739705 \n", "L 112.945796 83.498855 \n", "L 113.016836 83.677904 \n", "L 113.204856 83.623131 \n", "L 113.228077 83.69358 \n", "L 113.275467 83.770668 \n", "L 113.345308 83.653016 \n", "L 113.580983 83.673938 \n", "L 113.604335 83.607177 \n", "L 113.67544 83.711019 \n", "L 113.910842 83.789706 \n", "L 113.935007 83.724817 \n", "L 113.958164 83.674462 \n", "L 114.004969 83.829972 \n", "L 114.052253 83.725285 \n", "L 114.217098 83.845256 \n", "L 114.26372 83.888157 \n", "L 114.334468 83.820346 \n", "L 114.499245 83.894043 \n", "L 114.946368 84.422114 \n", "L 115.135336 84.306114 \n", "L 115.181589 84.363857 \n", "L 115.229296 84.325195 \n", "L 115.275584 84.283106 \n", "L 115.34636 84.342881 \n", "L 115.463465 84.729275 \n", "L 115.487324 84.708079 \n", "L 115.534615 84.595931 \n", "L 115.606016 84.668143 \n", "L 115.771045 84.7056 \n", "L 115.794735 84.664348 \n", "L 115.865458 84.729165 \n", "L 116.149122 84.428618 \n", "L 116.290051 84.426328 \n", "L 116.431589 84.552457 \n", "L 116.549522 84.497524 \n", "L 116.809911 84.298167 \n", "L 116.99881 84.326169 \n", "L 117.188345 84.09541 \n", "L 117.258853 84.205498 \n", "L 117.283086 84.097598 \n", "L 117.306514 84.02197 \n", "L 117.330054 84.149735 \n", "L 117.448588 84.440107 \n", "L 117.496204 84.41796 \n", "L 117.614514 84.254549 \n", "L 117.638194 84.324256 \n", "L 117.732974 84.202705 \n", "L 117.827293 83.94555 \n", "L 117.874859 84.081307 \n", "L 117.945787 84.177169 \n", "L 117.99257 84.104392 \n", "L 118.347904 83.869029 \n", "L 118.442482 83.882509 \n", "L 118.560221 83.719417 \n", "L 118.608115 83.790888 \n", "L 118.844325 83.880044 \n", "L 119.05659 83.67184 \n", "L 119.26898 83.998526 \n", "L 119.457905 84.146058 \n", "L 119.693128 84.03776 \n", "L 119.763697 83.960606 \n", "L 119.952354 83.824163 \n", "L 120.023616 83.762039 \n", "L 120.070228 83.839184 \n", "L 120.188574 83.868965 \n", "L 120.377242 83.9289 \n", "L 121.912844 81.393679 \n", "L 122.150649 81.239281 \n", "L 122.340355 80.956689 \n", "L 122.743217 80.739376 \n", "L 122.908215 80.428021 \n", "L 123.310904 79.669192 \n", "L 123.405753 79.464001 \n", "L 123.429142 79.481595 \n", "L 123.571742 79.421565 \n", "L 123.595519 79.450718 \n", "L 123.713489 79.425535 \n", "L 123.998049 78.80909 \n", "L 124.045898 78.829691 \n", "L 124.06939 78.804822 \n", "L 124.282418 78.511175 \n", "L 124.519674 78.309115 \n", "L 124.779319 77.710541 \n", "L 124.944375 77.3808 \n", "L 126.034443 74.248195 \n", "L 126.057717 74.274 \n", "L 126.081486 74.24793 \n", "L 126.27087 73.894609 \n", "L 126.318194 73.838246 \n", "L 126.389692 73.883623 \n", "L 126.436587 73.914466 \n", "L 126.460541 73.876891 \n", "L 126.674344 73.579734 \n", "L 126.698012 73.616009 \n", "L 126.769363 73.523601 \n", "L 127.172007 72.647502 \n", "L 127.479865 71.803238 \n", "L 127.929429 70.685094 \n", "L 128.189877 70.077248 \n", "L 128.594309 69.017059 \n", "L 128.949358 68.404199 \n", "L 128.996732 68.328576 \n", "L 129.233531 67.824469 \n", "L 129.753987 67.292861 \n", "L 130.037698 66.677828 \n", "L 130.155989 66.56 \n", "L 130.439697 65.91001 \n", "L 130.486664 65.930274 \n", "L 130.557691 65.798644 \n", "L 130.818008 65.314444 \n", "L 130.960045 65.040002 \n", "L 131.219856 64.644536 \n", "L 131.267519 64.551524 \n", "L 131.575683 64.01994 \n", "L 131.95475 63.216831 \n", "L 132.19164 62.505325 \n", "L 132.262865 62.209854 \n", "L 132.522486 61.40681 \n", "L 132.664302 61.3572 \n", "L 132.85377 61.187101 \n", "L 132.8775 61.215022 \n", "L 132.901512 61.162775 \n", "L 133.066862 60.934851 \n", "L 133.090485 60.949732 \n", "L 133.302962 60.865138 \n", "L 133.42105 60.769327 \n", "L 133.610597 60.603104 \n", "L 133.846746 60.420008 \n", "L 134.082344 59.961505 \n", "L 134.105834 59.977441 \n", "L 134.153059 59.879243 \n", "L 134.624869 59.310355 \n", "L 134.671886 59.370495 \n", "L 134.719112 59.265776 \n", "L 135.025264 58.847057 \n", "L 135.308288 58.211316 \n", "L 135.662518 56.991549 \n", "L 135.709458 56.889287 \n", "L 135.946567 56.414554 \n", "L 136.183323 55.810312 \n", "L 136.230527 55.743175 \n", "L 136.420129 55.536945 \n", "L 136.561927 55.460146 \n", "L 136.774849 55.153678 \n", "L 136.916009 55.126641 \n", "L 137.199173 54.703098 \n", "L 137.646865 53.905487 \n", "L 137.788327 53.695168 \n", "L 138.212895 53.082806 \n", "L 138.426142 52.888587 \n", "L 140.936488 48.403096 \n", "L 142.012762 47.391038 \n", "L 142.40882 46.75027 \n", "L 142.52584 46.728998 \n", "L 142.642723 46.692178 \n", "L 143.411015 45.908231 \n", "L 143.480805 45.894536 \n", "L 143.504012 45.850899 \n", "L 143.806193 45.42408 \n", "L 143.898967 45.339309 \n", "L 144.131968 45.147953 \n", "L 144.248064 45.004801 \n", "L 144.433779 44.77158 \n", "L 146.295517 43.04402 \n", "L 146.552349 43.009257 \n", "L 147.088242 42.387371 \n", "L 147.29875 42.212034 \n", "L 147.43888 42.11033 \n", "L 148.019968 41.78805 \n", "L 148.252251 41.489884 \n", "L 149.271044 40.20529 \n", "L 149.410062 40.136548 \n", "L 149.781888 39.935498 \n", "L 149.967449 39.714001 \n", "L 150.338318 39.273337 \n", "L 150.361608 39.293934 \n", "L 150.477267 39.28176 \n", "L 150.779301 38.962539 \n", "L 150.895616 38.896422 \n", "L 151.428191 38.376629 \n", "L 151.45133 38.426019 \n", "L 151.520547 38.337042 \n", "L 151.867594 38.103854 \n", "L 152.216208 38.071926 \n", "L 152.540442 37.819732 \n", "L 152.912041 37.550216 \n", "L 153.097576 37.428087 \n", "L 153.166889 37.363614 \n", "L 153.421631 37.055815 \n", "L 153.444779 37.110079 \n", "L 153.514565 37.002116 \n", "L 153.907996 36.893504 \n", "L 154.788987 36.401351 \n", "L 155.066591 36.194052 \n", "L 155.252095 36.102718 \n", "L 155.414706 36.15755 \n", "L 155.832357 35.981286 \n", "L 156.017393 35.965449 \n", "L 156.225887 35.714688 \n", "L 156.434168 35.637258 \n", "L 156.572918 35.768403 \n", "L 156.596097 35.75324 \n", "L 156.872949 35.730683 \n", "L 157.104349 35.61733 \n", "L 157.196995 35.688123 \n", "L 157.220077 35.662743 \n", "L 157.28953 35.600292 \n", "L 157.335665 35.650182 \n", "L 157.520289 35.779831 \n", "L 157.774769 35.71013 \n", "L 158.029356 35.591035 \n", "L 158.144909 35.584305 \n", "L 158.330104 35.491112 \n", "L 158.65452 35.347989 \n", "L 158.954659 35.273382 \n", "L 159.046734 35.216868 \n", "L 159.069915 35.262796 \n", "L 159.20818 35.384959 \n", "L 159.231332 35.352449 \n", "L 159.832995 35.076032 \n", "L 160.064755 35.037286 \n", "L 160.366482 35.176754 \n", "L 160.551476 35.146048 \n", "L 160.713285 35.157672 \n", "L 161.338427 35.062662 \n", "L 161.477262 34.997994 \n", "L 161.500474 35.014292 \n", "L 161.778649 35.08784 \n", "L 162.080311 35.128879 \n", "L 162.21935 35.243625 \n", "L 162.798596 34.949695 \n", "L 162.937409 35.116356 \n", "L 162.960515 35.092883 \n", "L 163.354285 34.816874 \n", "L 163.493534 34.779288 \n", "L 163.980253 34.547409 \n", "L 164.37241 34.338136 \n", "L 164.626915 34.037117 \n", "L 165.783706 33.464303 \n", "L 165.876395 33.493646 \n", "L 165.89959 33.461391 \n", "L 166.038181 33.39299 \n", "L 166.177115 33.384452 \n", "L 166.338988 33.438651 \n", "L 166.408057 33.386655 \n", "L 166.454401 33.43561 \n", "L 166.593117 33.520482 \n", "L 166.616396 33.462582 \n", "L 166.801751 33.30702 \n", "L 167.241974 33.156971 \n", "L 167.704574 32.947232 \n", "L 167.797248 32.873539 \n", "L 168.191715 32.641768 \n", "L 168.214917 32.678868 \n", "L 168.376837 32.792001 \n", "L 168.538714 32.764066 \n", "L 168.65395 32.700547 \n", "L 170.179361 32.241095 \n", "L 170.433795 32.350759 \n", "L 170.733413 32.195481 \n", "L 170.871809 32.205087 \n", "L 171.126628 32.525675 \n", "L 171.403505 32.503538 \n", "L 171.588213 32.401473 \n", "L 171.680771 32.340071 \n", "L 171.796298 32.301097 \n", "L 171.819453 32.324252 \n", "L 171.912141 32.325384 \n", "L 171.935304 32.302782 \n", "L 171.981671 32.303634 \n", "L 172.004833 32.254818 \n", "L 172.373929 31.957443 \n", "L 173.042585 32.085019 \n", "L 173.204587 32.191473 \n", "L 173.992205 32.200227 \n", "L 174.223799 32.076299 \n", "L 174.477686 31.962848 \n", "L 174.616573 32.002736 \n", "L 174.639681 31.972231 \n", "L 174.940027 31.837961 \n", "L 175.148282 31.812879 \n", "L 175.310083 31.849692 \n", "L 175.425658 31.915537 \n", "L 175.448682 31.870194 \n", "L 175.771752 31.978201 \n", "L 175.956929 32.086624 \n", "L 176.373311 31.976268 \n", "L 177.273294 31.734895 \n", "L 177.412015 31.608415 \n", "L 177.665932 31.726405 \n", "L 177.850869 31.737011 \n", "L 178.012755 31.835274 \n", "L 178.220999 31.83076 \n", "L 179.354389 31.54556 \n", "L 179.424038 31.585038 \n", "L 179.470387 31.51641 \n", "L 179.701413 31.435587 \n", "L 179.932492 31.43286 \n", "L 180.485517 31.273477 \n", "L 180.578249 31.358644 \n", "L 180.624361 31.329438 \n", "L 181.270763 31.380764 \n", "L 181.615724 31.169712 \n", "L 181.753542 31.134194 \n", "L 182.490052 31.518898 \n", "L 182.628614 31.517215 \n", "L 182.83687 31.598499 \n", "L 183.067698 31.579849 \n", "L 183.22999 31.52181 \n", "L 183.483472 31.300891 \n", "L 184.52251 31.073577 \n", "L 184.707126 31.049394 \n", "L 184.845546 31.0444 \n", "L 185.121873 31.083341 \n", "L 185.167795 31.018836 \n", "L 185.236889 31.07397 \n", "L 185.352487 31.130253 \n", "L 185.375505 31.097912 \n", "L 185.676187 30.973063 \n", "L 185.883606 31.00223 \n", "L 186.529041 30.782786 \n", "L 186.942873 30.515842 \n", "L 186.96598 30.542723 \n", "L 188.120698 29.744002 \n", "L 188.259441 29.640863 \n", "L 188.282611 29.655717 \n", "L 188.420649 29.580712 \n", "L 188.69711 29.38318 \n", "L 189.06596 29.233484 \n", "L 189.181352 29.202524 \n", "L 190.381046 28.886926 \n", "L 191.258413 28.357965 \n", "L 191.650456 28.394764 \n", "L 192.135213 28.648854 \n", "L 192.366456 28.76843 \n", "L 192.551078 28.81548 \n", "L 193.611597 29.406796 \n", "L 195.019744 29.566101 \n", "L 195.319931 29.89307 \n", "L 195.38918 29.936491 \n", "L 195.435351 29.883413 \n", "L 195.573931 29.784652 \n", "L 195.919925 29.966212 \n", "L 196.357294 29.951882 \n", "L 197.119287 30.606436 \n", "L 197.280623 30.60141 \n", "L 197.650685 30.541101 \n", "L 197.950087 30.274082 \n", "L 197.973155 30.29613 \n", "L 198.157661 30.382948 \n", "L 199.381676 30.510739 \n", "L 199.79756 30.634932 \n", "L 200.00567 30.746424 \n", "L 200.306699 30.785091 \n", "L 200.51491 30.858913 \n", "L 200.699523 30.832764 \n", "L 200.861169 30.760056 \n", "L 201.115092 30.540815 \n", "L 201.369273 30.487165 \n", "L 201.71555 30.381176 \n", "L 201.831117 30.462015 \n", "L 202.10821 30.613132 \n", "L 202.13121 30.58095 \n", "L 202.523402 30.450565 \n", "L 202.684752 30.293996 \n", "L 202.73089 30.325224 \n", "L 202.961726 30.404379 \n", "L 203.377685 30.199751 \n", "L 203.515724 30.150354 \n", "L 204.324886 29.823945 \n", "L 204.601224 29.52012 \n", "L 204.947756 29.328808 \n", "L 205.201132 29.499553 \n", "L 205.615766 29.602473 \n", "L 206.607694 29.292766 \n", "L 206.930348 29.45984 \n", "L 207.114735 29.519971 \n", "L 207.276081 29.474851 \n", "L 208.982364 28.488294 \n", "L 209.375773 28.607708 \n", "L 209.583282 28.557094 \n", "L 209.837731 28.542045 \n", "L 209.975551 28.47481 \n", "L 209.998746 28.495565 \n", "L 210.11419 28.586533 \n", "L 210.437439 28.840541 \n", "L 210.622477 28.836961 \n", "L 210.876844 28.831662 \n", "L 211.245898 28.454285 \n", "L 211.453395 28.47032 \n", "L 211.615513 28.533502 \n", "L 211.93937 28.494924 \n", "L 212.193837 28.45815 \n", "L 212.517605 28.459272 \n", "L 212.586621 28.424469 \n", "L 212.632836 28.483404 \n", "L 212.840572 28.556013 \n", "L 213.164123 28.558867 \n", "L 213.418101 28.609884 \n", "L 213.81164 28.774883 \n", "L 214.019449 28.782793 \n", "L 214.898382 28.605329 \n", "L 215.060512 28.566329 \n", "L 215.245695 28.626822 \n", "L 215.50011 28.645395 \n", "L 215.916719 28.501953 \n", "L 216.170659 28.430099 \n", "L 216.448483 28.546272 \n", "L 216.587202 28.531519 \n", "L 217.003308 28.466001 \n", "L 217.257999 28.370423 \n", "L 217.719996 28.350753 \n", "L 218.019917 28.496358 \n", "L 218.250941 28.541378 \n", "L 218.412494 28.512774 \n", "L 218.619943 28.274922 \n", "L 220.443249 28.205589 \n", "L 220.581448 28.247217 \n", "L 220.996671 28.279348 \n", "L 221.203831 28.113054 \n", "L 221.38819 28.236572 \n", "L 221.756808 28.233337 \n", "L 221.917963 28.214684 \n", "L 222.170981 28.140547 \n", "L 222.355606 28.060774 \n", "L 222.909499 28.076647 \n", "L 223.416316 28.375283 \n", "L 224.130624 28.280981 \n", "L 224.84743 29.110618 \n", "L 225.009324 29.085058 \n", "L 225.310037 29.089663 \n", "L 225.448261 29.079398 \n", "L 225.656325 29.097892 \n", "L 225.74884 29.171742 \n", "L 226.049188 29.361401 \n", "L 226.7178 29.727789 \n", "L 226.83358 29.776112 \n", "L 226.972304 29.894413 \n", "L 227.20287 29.774992 \n", "L 227.571819 29.588804 \n", "L 227.733274 29.592008 \n", "L 227.871584 29.563515 \n", "L 228.863862 29.610563 \n", "L 229.095554 29.421445 \n", "L 230.041689 28.973051 \n", "L 230.941413 28.130131 \n", "L 230.964591 28.160008 \n", "L 231.080024 28.205406 \n", "L 231.495489 28.362971 \n", "L 231.679792 28.124498 \n", "L 231.932808 27.859414 \n", "L 232.001594 27.777112 \n", "L 232.186004 27.548946 \n", "L 232.300981 27.493229 \n", "L 232.554442 27.358158 \n", "L 232.946597 27.267406 \n", "L 233.061612 27.298736 \n", "L 233.084646 27.259494 \n", "L 233.315786 27.171274 \n", "L 233.593692 26.986822 \n", "L 233.778484 27.03136 \n", "L 233.893707 27.007926 \n", "L 234.077703 26.81805 \n", "L 234.539665 26.755666 \n", "L 234.701312 26.748574 \n", "L 235.761295 26.522825 \n", "L 236.705347 26.789259 \n", "L 237.027414 26.654193 \n", "L 237.18813 26.601648 \n", "L 237.394616 26.536673 \n", "L 238.707193 26.946178 \n", "L 238.730292 26.924366 \n", "L 238.776453 26.989984 \n", "L 239.030049 27.124684 \n", "L 239.353242 27.108361 \n", "L 239.560141 26.954106 \n", "L 240.18224 26.97897 \n", "L 240.528839 26.787743 \n", "L 241.632598 27.003843 \n", "L 242.138875 26.890704 \n", "L 242.276979 26.929715 \n", "L 242.438474 26.865603 \n", "L 242.622863 26.866277 \n", "L 242.967618 26.790164 \n", "L 243.105293 26.857759 \n", "L 243.403873 26.772831 \n", "L 243.565412 26.719801 \n", "L 243.749582 26.570947 \n", "L 244.232948 26.622582 \n", "L 244.509972 26.521352 \n", "L 244.693829 26.374513 \n", "L 244.80919 26.313195 \n", "L 244.993952 26.26851 \n", "L 245.270235 26.371561 \n", "L 246.053631 26.291695 \n", "L 246.468457 26.26853 \n", "L 246.629661 26.328918 \n", "L 246.744988 26.421558 \n", "L 246.76799 26.393817 \n", "L 247.021708 26.305506 \n", "L 247.182645 26.274049 \n", "L 247.434653 26.231802 \n", "L 247.503543 26.160616 \n", "L 247.57249 26.205477 \n", "L 247.733197 26.263833 \n", "L 248.170112 26.116186 \n", "L 248.998576 26.058285 \n", "L 249.159792 26.088127 \n", "L 249.297934 26.086909 \n", "L 250.543821 26.092201 \n", "L 251.051763 26.45666 \n", "L 251.328783 26.536647 \n", "L 251.536919 26.749478 \n", "L 251.813951 26.730804 \n", "L 252.137502 26.592034 \n", "L 252.482917 26.429977 \n", "L 252.713082 26.45139 \n", "L 253.152266 26.439885 \n", "L 253.291004 26.462643 \n", "L 254.46659 26.494283 \n", "L 254.627224 26.501924 \n", "L 254.857617 26.566641 \n", "L 254.995525 26.483133 \n", "L 255.156672 26.584869 \n", "L 255.341222 26.685512 \n", "L 255.641002 26.737416 \n", "L 256.375909 26.583566 \n", "L 256.445079 26.527551 \n", "L 256.491321 26.551525 \n", "L 256.514158 26.586455 \n", "L 256.582899 26.518982 \n", "L 256.927792 26.299063 \n", "L 257.042874 26.237613 \n", "L 257.342113 26.005588 \n", "L 257.779475 26.311398 \n", "L 258.241043 26.368521 \n", "L 258.540261 26.209462 \n", "L 258.793333 26.110983 \n", "L 259.092486 26.080379 \n", "L 259.989078 25.832547 \n", "L 260.494538 26.067823 \n", "L 260.655465 26.0752 \n", "L 260.816451 26.127228 \n", "L 260.839542 26.090331 \n", "L 261.391418 25.905074 \n", "L 261.483316 25.975759 \n", "L 261.529338 25.923477 \n", "L 261.759437 25.94512 \n", "L 261.94295 26.080709 \n", "L 262.195525 26.15558 \n", "L 262.218471 26.179066 \n", "L 262.287218 26.102559 \n", "L 262.562893 26.139272 \n", "L 262.74716 26.204904 \n", "L 263.598019 25.813949 \n", "L 263.805127 25.782653 \n", "L 264.195852 25.855903 \n", "L 264.747409 25.990356 \n", "L 264.978385 26.160507 \n", "L 265.46148 26.208857 \n", "L 265.692011 26.06556 \n", "L 266.314281 25.846674 \n", "L 266.84248 25.785964 \n", "L 267.25602 25.691488 \n", "L 267.623185 25.559849 \n", "L 267.876005 25.520616 \n", "L 268.036764 25.484145 \n", "L 268.311932 25.615719 \n", "L 268.449607 25.619459 \n", "L 270.402278 25.589245 \n", "L 270.723701 25.409665 \n", "L 270.930503 25.387283 \n", "L 271.091041 25.331664 \n", "L 271.367507 25.37487 \n", "L 271.597631 25.508813 \n", "L 272.538887 25.730289 \n", "L 272.814036 25.615721 \n", "L 272.974617 25.544578 \n", "L 273.318658 25.48776 \n", "L 273.43347 25.50164 \n", "L 273.54839 25.475498 \n", "L 273.709545 25.533295 \n", "L 274.192286 25.301962 \n", "L 274.307301 25.319454 \n", "L 274.330286 25.295326 \n", "L 274.468237 25.284169 \n", "L 274.835737 25.384479 \n", "L 275.065662 25.338177 \n", "L 275.364475 25.535876 \n", "L 275.548537 25.579269 \n", "L 275.686817 25.561617 \n", "L 275.847518 25.525252 \n", "L 276.05483 25.548815 \n", "L 276.2388 25.524703 \n", "L 276.399725 25.530455 \n", "L 276.583973 25.604497 \n", "L 276.676049 25.613589 \n", "L 276.791513 25.704154 \n", "L 276.814429 25.67517 \n", "L 277.43562 25.593943 \n", "L 277.619528 25.633775 \n", "L 278.17077 25.176036 \n", "L 278.400225 25.165596 \n", "L 278.469561 25.160341 \n", "L 278.469561 25.160341 \n", "\" clip-path=\"url(#p13454f8562)\" style=\"fill: none; stroke: #55a868; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 58.**********.792098 \n", "L 58.611234 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 58.**********.792098 \n", "L 278.**********.792098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 340.845234 188.792098 \n", "L 561.051234 188.792098 \n", "L 561.051234 11.588098 \n", "L 340.845234 11.588098 \n", "L 340.845234 188.792098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_22\">\n", "      <path d=\"M 382.401923 188.792098 \n", "L 382.401923 11.588098 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"382.401923\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(373.217869 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_24\">\n", "      <path d=\"M 424.050672 188.792098 \n", "L 424.050672 11.588098 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"424.050672\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 1.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(414.866617 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_26\">\n", "      <path d=\"M 465.69942 188.792098 \n", "L 465.69942 11.588098 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"465.69942\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 1.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(456.515366 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_28\">\n", "      <path d=\"M 507.348169 188.792098 \n", "L 507.348169 11.588098 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"507.348169\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 2.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(498.164114 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_30\">\n", "      <path d=\"M 548.996917 188.792098 \n", "L 548.996917 11.588098 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"548.996917\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 2.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(539.812863 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- Global step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(415.221328 229.044363) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"105.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"166.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"229.931641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"291.210938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"318.994141\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"350.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.613281\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- 1e9 -->\n", "     <g style=\"fill: #262626\" transform=\"translate(539.247 221.246527) scale(0.1155 -0.1155)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"125.146484\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_32\">\n", "      <path d=\"M 340.845234 161.944248 \n", "L 561.051234 161.944248 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"340.845234\" y=\"161.944248\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 92 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(316.647859 166.332346) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-39\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_34\">\n", "      <path d=\"M 340.845234 125.224457 \n", "L 561.051234 125.224457 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"340.845234\" y=\"125.224457\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 94 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(316.647859 129.612555) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-39\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_36\">\n", "      <path d=\"M 340.845234 88.504665 \n", "L 561.051234 88.504665 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"340.845234\" y=\"88.504665\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 96 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(316.647859 92.892763) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-39\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_38\">\n", "      <path d=\"M 340.845234 51.784874 \n", "L 561.051234 51.784874 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"340.845234\" y=\"51.784874\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 98 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(316.647859 56.172972) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-39\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_40\">\n", "      <path d=\"M 340.845234 15.065083 \n", "L 561.051234 15.065083 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"340.845234\" y=\"15.065083\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 100 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(309.299172 19.45318) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- Perc. goal achieved [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(302.678766 174.473988) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-25\" d=\"M 4653 2053 \n", "Q 4381 2053 4226 1822 \n", "Q 4072 1591 4072 1178 \n", "Q 4072 772 4226 539 \n", "Q 4381 306 4653 306 \n", "Q 4919 306 5073 539 \n", "Q 5228 772 5228 1178 \n", "Q 5228 1588 5073 1820 \n", "Q 4919 2053 4653 2053 \n", "z\n", "M 4653 2450 \n", "Q 5147 2450 5437 2106 \n", "Q 5728 1763 5728 1178 \n", "Q 5728 594 5436 251 \n", "Q 5144 -91 4653 -91 \n", "Q 4153 -91 3862 251 \n", "Q 3572 594 3572 1178 \n", "Q 3572 1766 3864 2108 \n", "Q 4156 2450 4653 2450 \n", "z\n", "M 1428 4353 \n", "Q 1159 4353 1004 4120 \n", "Q 850 3888 850 3481 \n", "Q 850 3069 1003 2837 \n", "Q 1156 2606 1428 2606 \n", "Q 1700 2606 1854 2837 \n", "Q 2009 3069 2009 3481 \n", "Q 2009 3884 1853 4118 \n", "Q 1697 4353 1428 4353 \n", "z\n", "M 4250 4750 \n", "L 4750 4750 \n", "L 1831 -91 \n", "L 1331 -91 \n", "L 4250 4750 \n", "z\n", "M 1428 4750 \n", "Q 1922 4750 2215 4408 \n", "Q 2509 4066 2509 3481 \n", "Q 2509 2891 2217 2550 \n", "Q 1925 2209 1428 2209 \n", "Q 931 2209 642 2551 \n", "Q 353 2894 353 3481 \n", "Q 353 4063 643 4406 \n", "Q 934 4750 1428 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"56.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"118.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"157.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"212.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"243.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"275.619141\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"339.095703\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"400.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"461.556641\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"489.339844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"521.126953\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"582.40625\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"637.386719\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"700.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"728.548828\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"790.072266\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"849.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"910.775391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"974.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"1006.039062\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"1045.052734\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"1140.072266\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <path d=\"M 350.854598 150.032889 \n", "L 351.16356 153.911665 \n", "L 351.384692 155.566351 \n", "L 351.582663 157.518675 \n", "L 351.736572 158.59634 \n", "L 352.001239 160.641842 \n", "L 352.045289 160.782113 \n", "L 352.199363 162.382127 \n", "L 352.243425 162.378555 \n", "L 352.463333 163.62967 \n", "L 352.507609 163.69678 \n", "L 352.772273 166.259505 \n", "L 352.838107 166.649032 \n", "L 352.948113 167.249529 \n", "L 353.168533 168.744364 \n", "L 353.830041 173.106868 \n", "L 353.874117 173.067757 \n", "L 353.896023 173.104847 \n", "L 354.445192 177.052266 \n", "L 354.599564 178.424638 \n", "L 355.039446 180.592738 \n", "L 355.083371 180.589459 \n", "L 355.105466 180.433573 \n", "L 355.171799 180.48552 \n", "L 355.282006 180.73737 \n", "L 355.458333 180.039568 \n", "L 355.502296 179.766907 \n", "L 355.590293 179.783002 \n", "L 355.612143 179.828178 \n", "L 355.678068 179.537194 \n", "L 355.722476 179.735503 \n", "L 355.766602 179.662783 \n", "L 355.811053 180.025268 \n", "L 355.876908 179.760756 \n", "L 355.94268 179.938449 \n", "L 355.964555 179.741813 \n", "L 356.03065 179.699333 \n", "L 356.119056 178.964602 \n", "L 356.229243 179.110316 \n", "L 356.339927 179.561829 \n", "L 356.383866 179.537183 \n", "L 356.494178 179.421179 \n", "L 356.516282 179.516131 \n", "L 356.582174 179.326184 \n", "L 356.604263 179.433407 \n", "L 356.648163 179.455265 \n", "L 356.670007 179.272956 \n", "L 356.691914 179.531935 \n", "L 356.735723 179.485083 \n", "L 356.779607 179.672587 \n", "L 356.823764 179.577252 \n", "L 356.845818 179.321379 \n", "L 356.911707 179.471264 \n", "L 356.93397 179.727019 \n", "L 357.022098 179.683494 \n", "L 357.219931 179.227225 \n", "L 357.374126 179.424268 \n", "L 357.462253 179.130204 \n", "L 357.506178 179.196826 \n", "L 357.682748 178.768593 \n", "L 357.704718 178.852526 \n", "L 357.726688 178.606814 \n", "L 357.748835 178.641455 \n", "L 358.012441 177.192838 \n", "L 358.276753 173.53928 \n", "L 358.386834 172.638611 \n", "L 358.673348 168.388753 \n", "L 358.761476 167.656246 \n", "L 359.025167 164.944247 \n", "L 359.047409 164.921552 \n", "L 359.333428 161.572489 \n", "L 359.619258 159.279723 \n", "L 360.103263 153.250515 \n", "L 360.279134 151.69371 \n", "L 360.30119 151.730302 \n", "L 360.499079 150.802835 \n", "L 360.58668 150.034152 \n", "L 361.07055 143.283311 \n", "L 361.092659 143.333234 \n", "L 361.114782 143.201456 \n", "L 361.290755 142.572038 \n", "L 361.312868 142.573105 \n", "L 361.445044 142.063829 \n", "L 361.46714 142.081921 \n", "L 361.621136 142.823104 \n", "L 361.643043 142.816225 \n", "L 361.687088 142.90779 \n", "L 361.731077 142.883263 \n", "L 361.819133 142.944823 \n", "L 361.973178 142.374471 \n", "L 362.017173 142.402818 \n", "L 362.61083 139.450267 \n", "L 362.85279 137.250171 \n", "L 362.918994 136.991375 \n", "L 363.468977 133.07775 \n", "L 363.753585 130.976935 \n", "L 363.973864 129.329498 \n", "L 364.786966 119.786767 \n", "L 365.843265 112.862171 \n", "L 366.085239 111.699943 \n", "L 366.50366 109.004992 \n", "L 367.537684 100.747218 \n", "L 369.120522 90.424212 \n", "L 369.384082 89.715206 \n", "L 369.581868 89.592009 \n", "L 369.603976 89.615403 \n", "L 369.713949 89.739779 \n", "L 369.933886 90.36107 \n", "L 369.999828 90.397821 \n", "L 370.065806 90.341271 \n", "L 370.175688 90.334175 \n", "L 370.682135 89.525664 \n", "L 370.836147 89.160092 \n", "L 371.077996 89.378161 \n", "L 371.121923 89.476508 \n", "L 371.16597 89.580725 \n", "L 371.232038 89.478982 \n", "L 372.177875 87.545818 \n", "L 372.24399 87.601643 \n", "L 372.441858 87.421168 \n", "L 372.662156 86.686036 \n", "L 372.684281 86.708898 \n", "L 372.706392 86.650194 \n", "L 372.859967 86.412025 \n", "L 372.882021 86.484044 \n", "L 372.947885 86.371405 \n", "L 373.607797 85.062006 \n", "L 373.674014 84.978974 \n", "L 373.915564 84.825408 \n", "L 373.959431 84.680003 \n", "L 374.025354 84.745188 \n", "L 374.091287 84.818767 \n", "L 374.135154 84.724699 \n", "L 374.179182 84.67088 \n", "L 374.31118 84.400579 \n", "L 374.355182 84.438034 \n", "L 374.838709 84.339051 \n", "L 374.992777 84.107717 \n", "L 375.103067 83.986383 \n", "L 375.520472 82.749393 \n", "L 375.652273 82.784664 \n", "L 375.762184 82.58718 \n", "L 376.048296 82.001751 \n", "L 376.158264 81.836116 \n", "L 376.201955 81.909632 \n", "L 376.224004 81.936461 \n", "L 376.246096 81.8928 \n", "L 376.575369 80.841165 \n", "L 376.619299 80.666208 \n", "L 376.861242 80.078261 \n", "L 377.103015 79.721741 \n", "L 377.125081 79.759976 \n", "L 377.169066 79.635586 \n", "L 377.278833 79.63705 \n", "L 377.410604 79.870066 \n", "L 377.432561 79.859818 \n", "L 377.696353 79.593248 \n", "L 378.114123 79.151866 \n", "L 378.136281 79.181887 \n", "L 378.180411 79.106415 \n", "L 378.246475 79.132598 \n", "L 378.356647 78.948368 \n", "L 378.48851 78.830233 \n", "L 378.510379 78.880325 \n", "L 378.55423 78.727304 \n", "L 378.664393 78.548181 \n", "L 378.686312 78.586963 \n", "L 378.730288 78.522541 \n", "L 378.884141 78.655685 \n", "L 378.927905 78.586602 \n", "L 378.994133 78.684045 \n", "L 379.059864 78.740634 \n", "L 379.169813 78.836938 \n", "L 379.191825 78.761689 \n", "L 379.213935 78.747279 \n", "L 379.236109 78.797787 \n", "L 379.258061 78.869803 \n", "L 379.323843 78.769794 \n", "L 379.346035 78.811619 \n", "L 379.500204 78.744894 \n", "L 379.522073 78.777071 \n", "L 379.565969 78.678918 \n", "L 379.653749 78.490708 \n", "L 379.697747 78.555404 \n", "L 379.851744 78.754024 \n", "L 380.005453 78.89682 \n", "L 380.225279 78.852256 \n", "L 380.423131 79.153804 \n", "L 380.731596 79.478407 \n", "L 381.061341 79.448755 \n", "L 381.259625 79.030405 \n", "L 381.369503 79.049134 \n", "L 381.391577 79.031359 \n", "L 381.522987 79.012999 \n", "L 381.742497 78.450997 \n", "L 381.764523 78.470151 \n", "L 381.830685 78.417804 \n", "L 381.874899 78.468945 \n", "L 382.006386 78.509813 \n", "L 382.204204 78.614973 \n", "L 382.424693 78.570249 \n", "L 382.578589 78.340486 \n", "L 382.688462 78.196718 \n", "L 383.018713 77.374762 \n", "L 383.062698 77.316725 \n", "L 383.282996 76.874184 \n", "L 383.327041 76.826532 \n", "L 383.459186 76.505229 \n", "L 383.48116 76.528289 \n", "L 383.503298 76.542259 \n", "L 383.525197 76.475436 \n", "L 383.898989 75.789191 \n", "L 384.228709 75.670922 \n", "L 384.382794 75.494689 \n", "L 384.537261 75.414433 \n", "L 384.845094 74.997655 \n", "L 384.910895 75.082857 \n", "L 384.976911 75.022727 \n", "L 385.196937 74.587968 \n", "L 385.658757 73.419613 \n", "L 385.680831 73.461652 \n", "L 385.725063 73.352787 \n", "L 385.923006 72.850791 \n", "L 385.966956 72.802757 \n", "L 386.252806 72.040209 \n", "L 386.648893 71.496608 \n", "L 386.736899 71.519544 \n", "L 386.758989 71.481565 \n", "L 387.023583 71.202608 \n", "L 387.265582 71.320072 \n", "L 387.419284 71.085746 \n", "L 387.837534 70.566982 \n", "L 388.145537 70.732283 \n", "L 388.167651 70.671108 \n", "L 388.233872 70.751314 \n", "L 388.277763 70.700211 \n", "L 388.629781 70.671839 \n", "L 388.739748 70.651463 \n", "L 388.761873 70.670787 \n", "L 389.003478 70.855288 \n", "L 389.223684 71.113813 \n", "L 389.26774 71.094067 \n", "L 389.311899 71.159849 \n", "L 390.016394 71.664663 \n", "L 390.060382 71.617949 \n", "L 390.1266 71.66681 \n", "L 390.390838 71.697702 \n", "L 390.500556 71.693754 \n", "L 391.160382 72.494619 \n", "L 391.446714 72.171314 \n", "L 391.468664 72.111256 \n", "L 391.534955 72.202195 \n", "L 392.436637 73.491216 \n", "L 392.568557 73.560779 \n", "L 392.678704 73.51663 \n", "L 393.185119 74.30385 \n", "L 393.207198 74.243212 \n", "L 393.294933 74.297001 \n", "L 393.515174 74.430676 \n", "L 393.647031 74.474998 \n", "L 393.867525 74.737955 \n", "L 394.109468 74.641609 \n", "L 394.219168 74.627907 \n", "L 394.439287 74.321243 \n", "L 394.549288 74.162405 \n", "L 394.923345 73.328507 \n", "L 395.121669 73.079906 \n", "L 395.16594 73.119974 \n", "L 395.187963 73.080534 \n", "L 395.298014 72.935712 \n", "L 395.320053 72.964252 \n", "L 395.430294 72.884462 \n", "L 395.650212 72.595347 \n", "L 395.958027 72.230919 \n", "L 396.683958 71.119708 \n", "L 396.705885 71.174688 \n", "L 396.904179 71.285514 \n", "L 396.926025 71.21355 \n", "L 397.454636 70.489622 \n", "L 397.476485 70.544695 \n", "L 397.608526 70.598303 \n", "L 397.762567 70.364373 \n", "L 398.004833 70.008051 \n", "L 398.026942 70.018167 \n", "L 398.313057 69.884284 \n", "L 398.510738 69.468389 \n", "L 398.642484 69.497661 \n", "L 398.75255 69.451129 \n", "L 398.906886 69.273645 \n", "L 399.632862 68.903918 \n", "L 400.204764 69.789524 \n", "L 400.490416 70.183472 \n", "L 400.644628 70.12656 \n", "L 400.885797 70.247273 \n", "L 401.14994 70.672366 \n", "L 401.457758 71.118075 \n", "L 401.69955 71.189621 \n", "L 402.117686 71.111243 \n", "L 402.183536 71.059146 \n", "L 402.359889 70.890415 \n", "L 402.470279 70.910781 \n", "L 402.624135 70.92875 \n", "L 402.756295 70.924878 \n", "L 402.778224 70.943069 \n", "L 402.822449 70.865694 \n", "L 402.954375 70.689322 \n", "L 403.174094 70.31879 \n", "L 403.438119 70.430121 \n", "L 403.548516 70.449054 \n", "L 403.57059 70.392256 \n", "L 403.989037 69.956203 \n", "L 404.077044 69.90753 \n", "L 404.31877 69.727697 \n", "L 404.495056 69.686115 \n", "L 404.802053 69.382548 \n", "L 405.351276 68.398943 \n", "L 405.549361 68.09001 \n", "L 406.230705 66.346815 \n", "L 406.539052 65.193705 \n", "L 407.33209 63.005308 \n", "L 407.529998 62.48718 \n", "L 407.750027 61.882653 \n", "L 407.794049 61.865179 \n", "L 408.07938 60.95218 \n", "L 408.233332 60.525208 \n", "L 408.563745 59.618623 \n", "L 409.135608 58.037018 \n", "L 409.157666 58.063523 \n", "L 409.333386 57.821049 \n", "L 409.971282 56.360932 \n", "L 410.323467 55.499168 \n", "L 410.565359 55.2877 \n", "L 410.939576 54.424206 \n", "L 411.137531 54.03811 \n", "L 411.269494 54.026118 \n", "L 411.401518 53.967039 \n", "L 411.511494 54.058449 \n", "L 411.533648 54.042247 \n", "L 411.665424 54.037547 \n", "L 411.797515 54.067398 \n", "L 413.558254 51.50036 \n", "L 413.712505 51.248202 \n", "L 413.756548 51.265954 \n", "L 414.086482 51.017765 \n", "L 414.636157 50.222228 \n", "L 414.899913 49.602483 \n", "L 415.098061 49.442793 \n", "L 415.208035 49.291256 \n", "L 415.339884 49.178831 \n", "L 415.361918 49.198127 \n", "L 415.669919 49.222345 \n", "L 415.801924 49.295747 \n", "L 416.175824 48.774364 \n", "L 416.659911 48.148411 \n", "L 418.310744 44.963099 \n", "L 418.772817 44.344165 \n", "L 419.037173 43.88178 \n", "L 419.146812 43.714243 \n", "L 419.433053 43.10288 \n", "L 420.863305 40.320953 \n", "L 422.578102 35.48718 \n", "L 422.974454 34.785483 \n", "L 423.040616 34.691237 \n", "L 424.405153 32.264072 \n", "L 424.515336 32.128894 \n", "L 426.100573 30.62266 \n", "L 426.233073 30.496292 \n", "L 426.606889 30.19942 \n", "L 426.782924 30.105862 \n", "L 427.310706 29.651042 \n", "L 427.486405 29.620246 \n", "L 427.992288 29.275949 \n", "L 428.476964 28.922537 \n", "L 428.652857 28.776217 \n", "L 428.916517 28.720226 \n", "L 429.136924 28.645025 \n", "L 429.907047 28.507295 \n", "L 430.302802 28.266432 \n", "L 430.632301 28.172121 \n", "L 432.391294 27.153514 \n", "L 432.941003 27.178053 \n", "L 434.745437 26.717989 \n", "L 435.339243 26.345964 \n", "L 435.912448 26.107059 \n", "L 436.265201 25.826985 \n", "L 436.771837 25.568666 \n", "L 440.053992 24.895107 \n", "L 440.515998 24.875819 \n", "L 440.691955 24.869698 \n", "L 441.065955 24.875475 \n", "L 441.968474 24.903448 \n", "L 442.232667 24.913404 \n", "L 442.717227 24.926228 \n", "L 442.915764 24.871318 \n", "L 444.083979 24.837952 \n", "L 449.042332 24.325585 \n", "L 449.263068 24.247133 \n", "L 450.188474 24.186226 \n", "L 450.342643 24.147078 \n", "L 450.541165 24.12761 \n", "L 450.871994 24.203444 \n", "L 451.92823 24.086063 \n", "L 452.346533 24.092542 \n", "L 452.764803 24.048009 \n", "L 454.98952 24.165011 \n", "L 455.210062 24.176753 \n", "L 455.650324 24.163548 \n", "L 456.025592 24.145187 \n", "L 457.346073 23.79572 \n", "L 458.402737 23.702734 \n", "L 460.053 23.392902 \n", "L 460.625084 23.327258 \n", "L 460.779189 23.341853 \n", "L 462.188526 23.256748 \n", "L 463.883898 23.053601 \n", "L 464.192474 23.070976 \n", "L 465.404444 23.093939 \n", "L 466.351352 22.981699 \n", "L 466.726088 22.995657 \n", "L 467.474393 23.049147 \n", "L 468.135833 23.05433 \n", "L 468.665061 22.965237 \n", "L 469.194448 23.043422 \n", "L 469.677704 23.015766 \n", "L 470.580053 22.940411 \n", "L 471.021515 22.931804 \n", "L 471.527162 22.987565 \n", "L 471.858545 23.012566 \n", "L 472.321081 22.967329 \n", "L 473.046853 22.940198 \n", "L 473.28888 22.985301 \n", "L 473.597305 22.955662 \n", "L 474.236299 23.107377 \n", "L 475.161327 23.156658 \n", "L 475.843585 23.296195 \n", "L 476.746729 23.494506 \n", "L 476.922803 23.460306 \n", "L 477.474202 23.456806 \n", "L 478.376859 23.566024 \n", "L 478.729028 23.435042 \n", "L 478.970986 23.475836 \n", "L 479.698386 23.443708 \n", "L 480.139868 23.484427 \n", "L 480.888876 23.471905 \n", "L 482.255001 23.254207 \n", "L 483.003197 23.312883 \n", "L 483.840652 23.231166 \n", "L 485.559417 23.226361 \n", "L 485.889537 23.179791 \n", "L 486.749172 23.271572 \n", "L 487.035673 23.270301 \n", "L 487.388695 23.256339 \n", "L 487.587409 23.243781 \n", "L 487.807761 23.250496 \n", "L 488.622552 23.276089 \n", "L 488.975123 23.298386 \n", "L 489.349477 23.321703 \n", "L 492.612064 23.152852 \n", "L 493.27328 23.146751 \n", "L 494.971181 22.938216 \n", "L 496.316392 23.066197 \n", "L 497.020241 22.928845 \n", "L 497.261843 22.884172 \n", "L 498.671228 22.880626 \n", "L 499.110934 22.893717 \n", "L 501.778078 23.323602 \n", "L 501.976452 23.361764 \n", "L 502.791256 23.489113 \n", "L 503.981278 23.548783 \n", "L 504.223153 23.563831 \n", "L 505.564957 23.398025 \n", "L 505.917556 23.320675 \n", "L 506.094121 23.311334 \n", "L 507.544868 22.694692 \n", "L 508.470733 22.319489 \n", "L 510.013591 21.724677 \n", "L 512.189879 21.214689 \n", "L 513.707593 21.144385 \n", "L 514.037315 21.211236 \n", "L 515.444795 21.091396 \n", "L 515.70844 21.104455 \n", "L 516.808077 21.073006 \n", "L 517.137703 21.026583 \n", "L 518.501819 20.995528 \n", "L 519.206817 20.887734 \n", "L 519.822007 20.809143 \n", "L 520.504035 20.738179 \n", "L 520.877761 20.7565 \n", "L 521.294764 20.765678 \n", "L 522.128995 20.604176 \n", "L 523.756315 20.636221 \n", "L 524.746324 20.789855 \n", "L 525.384787 20.839512 \n", "L 526.684758 20.95165 \n", "L 527.081448 20.969866 \n", "L 527.38922 21.011283 \n", "L 528.202006 21.001738 \n", "L 528.510282 20.976394 \n", "L 528.730046 20.939546 \n", "L 529.126177 20.927764 \n", "L 529.41153 20.939647 \n", "L 529.806611 20.915667 \n", "L 531.191191 20.782768 \n", "L 531.764079 20.821364 \n", "L 532.247945 20.82731 \n", "L 532.48994 20.820801 \n", "L 532.754195 20.781776 \n", "L 536.336993 20.696475 \n", "L 537.040282 20.563377 \n", "L 539.436715 20.420458 \n", "L 539.963782 20.339118 \n", "L 540.42579 20.332541 \n", "L 541.65721 20.310802 \n", "L 542.491543 20.356161 \n", "L 542.93049 20.296147 \n", "L 543.611709 20.239789 \n", "L 544.007028 20.22954 \n", "L 545.062982 20.156045 \n", "L 545.392903 20.12755 \n", "L 546.360836 19.965338 \n", "L 547.196187 19.812858 \n", "L 547.811046 19.743947 \n", "L 548.932268 19.660951 \n", "L 549.305598 19.718127 \n", "L 549.526016 19.734142 \n", "L 551.041871 19.809639 \n", "L 551.041871 19.809639 \n", "\" clip-path=\"url(#p821b794013)\" style=\"fill: none; stroke: #4c72b0; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 340.845234 188.792098 \n", "L 340.845234 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 340.845234 188.792098 \n", "L 561.051234 188.792098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 623.079234 188.792098 \n", "L 843.285234 188.792098 \n", "L 843.285234 11.588098 \n", "L 623.079234 11.588098 \n", "L 623.079234 188.792098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_43\">\n", "      <path d=\"M 664.635923 188.792098 \n", "L 664.635923 11.588098 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"664.635923\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 0.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(655.451869 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_45\">\n", "      <path d=\"M 706.284672 188.792098 \n", "L 706.284672 11.588098 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"706.284672\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 1.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(697.100617 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_47\">\n", "      <path d=\"M 747.93342 188.792098 \n", "L 747.93342 11.588098 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"747.93342\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 1.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(738.749366 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_49\">\n", "      <path d=\"M 789.582169 188.792098 \n", "L 789.582169 11.588098 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"789.582169\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 2.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(780.398114 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_51\">\n", "      <path d=\"M 831.230917 188.792098 \n", "L 831.230917 11.588098 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"831.230917\" y=\"188.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 2.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(822.046863 207.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_31\">\n", "     <!-- Global step -->\n", "     <g style=\"fill: #262626\" transform=\"translate(697.455328 229.044363) scale(0.126 -0.126)\">\n", "      <use xlink:href=\"#DejaVuSans-47\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"77.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"105.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"166.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"229.931641\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"291.210938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"318.994141\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"350.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.880859\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"442.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"503.613281\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_32\">\n", "     <!-- 1e9 -->\n", "     <g style=\"fill: #262626\" transform=\"translate(821.481 221.246527) scale(0.1155 -0.1155)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"125.146484\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_53\">\n", "      <path d=\"M 623.079234 180.247099 \n", "L 843.285234 180.247099 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"623.079234\" y=\"180.247099\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_33\">\n", "      <!-- 0.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(595.211125 184.635196) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_55\">\n", "      <path d=\"M 623.079234 148.235754 \n", "L 843.285234 148.235754 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"623.079234\" y=\"148.235754\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_34\">\n", "      <!-- 1.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(595.211125 152.623852) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_57\">\n", "      <path d=\"M 623.079234 116.22441 \n", "L 843.285234 116.22441 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"623.079234\" y=\"116.22441\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_35\">\n", "      <!-- 1.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(595.211125 120.612508) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_59\">\n", "      <path d=\"M 623.079234 84.213066 \n", "L 843.285234 84.213066 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"623.079234\" y=\"84.213066\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_36\">\n", "      <!-- 2.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(595.211125 88.601164) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_61\">\n", "      <path d=\"M 623.079234 52.201722 \n", "L 843.285234 52.201722 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"623.079234\" y=\"52.201722\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_37\">\n", "      <!-- 2.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(595.211125 56.58982) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_63\">\n", "      <path d=\"M 623.079234 20.190378 \n", "L 843.285234 20.190378 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#mb74aca4dfb\" x=\"623.079234\" y=\"20.190378\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_38\">\n", "      <!-- 3.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(595.211125 24.578476) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_39\">\n", "     <!-- <PERSON><PERSON> [%] -->\n", "     <g style=\"fill: #262626\" transform=\"translate(588.590719 132.680379) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4d\" d=\"M 628 4666 \n", "L 1569 4666 \n", "L 2759 1491 \n", "L 3956 4666 \n", "L 4897 4666 \n", "L 4897 0 \n", "L 4281 0 \n", "L 4281 4097 \n", "L 3078 897 \n", "L 2444 897 \n", "L 1241 4097 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4d\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"86.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"147.802734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"187.011719\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"228.125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"255.908203\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"310.888672\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"342.675781\"/>\n", "      <use xlink:href=\"#DejaVuSans-25\" x=\"381.689453\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"476.708984\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_65\">\n", "    <path d=\"M 633.088598 170.502034 \n", "L 633.154724 170.163854 \n", "L 633.508065 168.699826 \n", "L 633.640923 167.550513 \n", "L 633.816663 166.307879 \n", "L 634.411321 160.496075 \n", "L 634.609228 158.436908 \n", "L 634.807864 156.380958 \n", "L 635.006273 154.76374 \n", "L 635.248151 152.015213 \n", "L 635.446623 149.611566 \n", "L 635.622807 147.691599 \n", "L 637.361759 125.546257 \n", "L 637.758439 123.47847 \n", "L 638.045053 119.980226 \n", "L 638.110908 119.651968 \n", "L 638.26465 117.802967 \n", "L 638.485504 113.445208 \n", "L 638.639936 110.945156 \n", "L 638.925914 107.18928 \n", "L 639.079818 105.476408 \n", "L 639.256098 103.027659 \n", "L 639.321796 102.311286 \n", "L 639.608126 97.273061 \n", "L 639.938718 93.206321 \n", "L 639.960688 93.44349 \n", "L 639.982835 93.109103 \n", "L 642.183265 52.987183 \n", "L 642.930438 43.331446 \n", "L 643.723315 30.041444 \n", "L 644.229038 22.026618 \n", "L 644.27314 22.15512 \n", "L 644.29499 22.011461 \n", "L 644.31712 21.753211 \n", "L 644.405142 21.847133 \n", "L 644.559048 21.004714 \n", "L 644.669231 20.794764 \n", "L 644.691237 20.94486 \n", "L 644.757002 20.733798 \n", "L 644.84483 20.526324 \n", "L 644.866825 20.686741 \n", "L 644.91087 20.865269 \n", "L 644.932895 20.727976 \n", "L 644.954794 20.410632 \n", "L 645.042877 20.41831 \n", "L 645.064893 20.723739 \n", "L 645.152994 20.641473 \n", "L 645.372954 19.642825 \n", "L 645.460904 19.851209 \n", "L 645.548897 20.059543 \n", "L 645.57096 19.709831 \n", "L 645.658964 19.994483 \n", "L 645.724848 20.091488 \n", "L 645.702977 19.896316 \n", "L 645.746694 19.992938 \n", "L 645.812384 19.750861 \n", "L 645.856152 19.81499 \n", "L 646.053594 21.068277 \n", "L 646.07562 20.855663 \n", "L 646.141485 21.261702 \n", "L 646.317559 22.62715 \n", "L 646.515324 23.530994 \n", "L 646.669065 24.900661 \n", "L 647.285222 31.1209 \n", "L 647.329024 30.933183 \n", "L 647.35112 31.216628 \n", "L 647.614562 34.060796 \n", "L 647.967159 37.22863 \n", "L 647.98902 37.111376 \n", "L 648.01087 37.365576 \n", "L 648.033026 37.338937 \n", "L 648.077265 37.477753 \n", "L 648.29737 39.320248 \n", "L 648.495556 41.75247 \n", "L 648.715583 43.859014 \n", "L 650.387402 66.152484 \n", "L 650.409503 66.159191 \n", "L 650.761261 69.882106 \n", "L 650.95913 72.61622 \n", "L 651.793814 82.683043 \n", "L 652.101955 85.00291 \n", "L 652.343778 86.100453 \n", "L 652.365802 86.044537 \n", "L 652.409688 86.207649 \n", "L 652.519795 86.416968 \n", "L 652.563801 86.378773 \n", "L 652.585759 86.362462 \n", "L 652.60772 86.189242 \n", "L 652.673545 86.447832 \n", "L 653.311996 89.325412 \n", "L 653.355923 89.436173 \n", "L 653.576216 90.632951 \n", "L 653.598262 90.629645 \n", "L 653.686196 91.012585 \n", "L 653.972075 92.563411 \n", "L 653.993963 92.540433 \n", "L 654.016152 92.574121 \n", "L 654.147867 92.94932 \n", "L 654.169798 92.929 \n", "L 654.213856 93.032558 \n", "L 654.499874 94.003204 \n", "L 654.785894 95.009003 \n", "L 654.852062 94.916519 \n", "L 654.874116 95.056458 \n", "L 654.962283 95.10223 \n", "L 654.984162 95.011327 \n", "L 655.07207 95.111021 \n", "L 655.247688 95.696361 \n", "L 655.269645 95.671451 \n", "L 655.291705 95.696154 \n", "L 655.511783 96.384317 \n", "L 655.533684 96.308558 \n", "L 655.599761 96.363958 \n", "L 655.621863 96.480274 \n", "L 655.687972 96.354732 \n", "L 655.775899 96.028987 \n", "L 655.819896 96.092441 \n", "L 656.061613 96.996853 \n", "L 656.303298 97.728041 \n", "L 656.369154 97.682026 \n", "L 656.435074 97.977945 \n", "L 656.479226 97.841088 \n", "L 656.611093 97.028464 \n", "L 656.655196 97.233057 \n", "L 656.677043 97.175421 \n", "L 656.698995 97.283628 \n", "L 656.742789 97.61481 \n", "L 656.808644 97.39451 \n", "L 656.830506 97.319698 \n", "L 656.852376 97.410184 \n", "L 656.918528 97.382967 \n", "L 657.183074 96.96168 \n", "L 657.226777 97.203567 \n", "L 657.314864 97.171696 \n", "L 657.358922 97.135531 \n", "L 657.380816 97.258396 \n", "L 657.402678 97.208976 \n", "L 657.600481 97.325422 \n", "L 657.710327 96.769258 \n", "L 657.732357 97.00685 \n", "L 658.150156 98.13128 \n", "L 658.282296 98.000207 \n", "L 658.304225 98.042964 \n", "L 658.326289 97.865299 \n", "L 658.348291 97.898829 \n", "L 658.5021 97.74178 \n", "L 658.633687 98.300535 \n", "L 658.677561 98.269208 \n", "L 658.765307 98.384672 \n", "L 658.919039 97.919483 \n", "L 658.94107 97.990144 \n", "L 658.963116 97.777113 \n", "L 659.073215 97.596992 \n", "L 659.029173 97.834147 \n", "L 659.095242 97.612922 \n", "L 659.644604 99.855172 \n", "L 659.886181 101.910258 \n", "L 659.908351 101.810727 \n", "L 659.952363 101.883954 \n", "L 660.018529 101.853486 \n", "L 660.062326 102.076694 \n", "L 660.194636 101.950791 \n", "L 660.414411 102.694028 \n", "L 660.524488 102.734978 \n", "L 660.656537 102.550821 \n", "L 660.78823 102.798718 \n", "L 660.810182 102.710082 \n", "L 660.832345 102.911231 \n", "L 660.854419 102.904566 \n", "L 661.557843 106.015256 \n", "L 661.601893 105.925453 \n", "L 661.623814 106.16264 \n", "L 661.865789 107.248874 \n", "L 661.95374 106.927198 \n", "L 661.9758 106.990477 \n", "L 662.173519 108.270162 \n", "L 662.195401 108.247778 \n", "L 662.239453 108.046845 \n", "L 662.305373 108.235797 \n", "L 662.327279 108.349777 \n", "L 662.415355 108.300291 \n", "L 662.437397 108.252298 \n", "L 662.459279 108.37787 \n", "L 662.503379 108.342542 \n", "L 662.525439 108.496429 \n", "L 662.591368 108.382014 \n", "L 662.723193 108.250523 \n", "L 662.78917 108.417714 \n", "L 662.81133 108.300167 \n", "L 662.833462 108.227433 \n", "L 662.855533 108.370961 \n", "L 662.877556 108.355434 \n", "L 663.031735 109.186478 \n", "L 663.053663 109.153672 \n", "L 663.07572 109.056666 \n", "L 663.119729 109.266843 \n", "L 663.207604 109.693906 \n", "L 663.251593 109.631264 \n", "L 663.471648 108.757631 \n", "L 663.493625 108.765187 \n", "L 663.581514 109.078283 \n", "L 663.84475 109.735172 \n", "L 663.998523 110.178979 \n", "L 664.130853 110.01922 \n", "L 664.570553 111.281389 \n", "L 664.68064 111.757622 \n", "L 664.702675 111.666657 \n", "L 664.790526 111.642288 \n", "L 664.878641 111.759842 \n", "L 664.900614 111.682261 \n", "L 665.032507 111.343293 \n", "L 665.142425 111.566931 \n", "L 665.252713 111.304613 \n", "L 665.274779 111.362413 \n", "L 665.296698 111.406311 \n", "L 665.318639 111.333222 \n", "L 665.362696 111.011313 \n", "L 665.428662 111.259606 \n", "L 665.450833 111.273563 \n", "L 665.494977 111.287319 \n", "L 665.583119 111.052969 \n", "L 665.627244 111.215496 \n", "L 666.022855 112.556557 \n", "L 666.220839 113.367145 \n", "L 666.286773 113.553946 \n", "L 666.330713 113.395534 \n", "L 666.352613 113.331969 \n", "L 666.418758 113.419432 \n", "L 666.484711 113.613798 \n", "L 666.52872 113.484796 \n", "L 666.550804 113.421226 \n", "L 666.594631 113.517475 \n", "L 666.638918 113.48961 \n", "L 666.749153 113.42765 \n", "L 666.925314 114.045289 \n", "L 666.969147 114.165837 \n", "L 667.100947 114.60362 \n", "L 667.122918 114.599882 \n", "L 667.342623 114.272401 \n", "L 667.430937 114.447441 \n", "L 667.60707 115.056331 \n", "L 667.629098 115.033866 \n", "L 667.651107 115.142617 \n", "L 668.223014 117.683015 \n", "L 668.267026 117.517392 \n", "L 668.289123 117.473059 \n", "L 668.310959 117.603037 \n", "L 668.376751 117.962906 \n", "L 668.420742 117.748474 \n", "L 668.464771 117.542344 \n", "L 668.55276 117.680887 \n", "L 668.574704 117.734134 \n", "L 668.64083 117.630658 \n", "L 668.68503 117.369008 \n", "L 668.750939 117.448419 \n", "L 668.794833 117.655879 \n", "L 668.882893 117.634011 \n", "L 668.904977 117.610514 \n", "L 669.036954 118.115988 \n", "L 669.059154 118.104958 \n", "L 669.477675 119.429677 \n", "L 669.499582 119.287736 \n", "L 669.565319 119.410143 \n", "L 670.049671 121.042706 \n", "L 670.071534 121.081467 \n", "L 670.093387 120.915939 \n", "L 670.291258 119.782761 \n", "L 670.313251 119.88626 \n", "L 670.379537 120.072849 \n", "L 670.467872 120.420059 \n", "L 670.511763 120.276578 \n", "L 670.57768 120.050236 \n", "L 670.643691 120.214756 \n", "L 670.73179 120.492221 \n", "L 670.775626 120.44007 \n", "L 670.797633 120.398649 \n", "L 670.819703 120.488583 \n", "L 670.841833 120.682834 \n", "L 670.929841 120.526651 \n", "L 671.083601 120.691495 \n", "L 671.105516 120.631547 \n", "L 671.149764 120.680009 \n", "L 671.193587 120.608537 \n", "L 671.215558 120.670117 \n", "L 671.237478 120.536931 \n", "L 671.523849 119.285026 \n", "L 671.590018 119.234241 \n", "L 671.61196 119.337595 \n", "L 671.699971 119.283547 \n", "L 671.722019 119.389634 \n", "L 671.766004 119.305152 \n", "L 671.810007 119.44823 \n", "L 672.008343 120.157752 \n", "L 672.228538 120.707616 \n", "L 672.272466 120.654103 \n", "L 672.294382 120.769161 \n", "L 672.404496 120.865041 \n", "L 672.492781 120.776029 \n", "L 672.448708 120.889061 \n", "L 672.514822 120.871587 \n", "L 672.778725 121.437343 \n", "L 672.910633 121.783656 \n", "L 673.064686 121.383021 \n", "L 673.108663 121.538877 \n", "L 673.240817 121.956588 \n", "L 673.262904 121.883125 \n", "L 673.438319 121.552981 \n", "L 673.460441 121.610933 \n", "L 673.482394 121.702201 \n", "L 673.548364 121.573211 \n", "L 673.570202 121.680478 \n", "L 673.592375 121.656949 \n", "L 673.61451 121.701299 \n", "L 673.790922 122.455458 \n", "L 673.856681 122.336035 \n", "L 673.8787 122.282101 \n", "L 673.922469 122.451988 \n", "L 674.560536 124.441405 \n", "L 674.582466 124.408512 \n", "L 674.604531 124.350167 \n", "L 674.648737 124.509336 \n", "L 674.758424 124.840424 \n", "L 674.802557 124.661627 \n", "L 674.890611 124.15529 \n", "L 674.956784 124.225273 \n", "L 675.110996 123.982132 \n", "L 675.133066 124.016425 \n", "L 675.22095 124.190932 \n", "L 675.264651 124.119184 \n", "L 675.550803 123.619042 \n", "L 675.572905 123.645353 \n", "L 675.793246 124.808066 \n", "L 675.881031 124.499805 \n", "L 675.969145 124.142392 \n", "L 675.99113 124.297024 \n", "L 676.079355 124.765041 \n", "L 676.123376 124.516912 \n", "L 676.211251 124.221068 \n", "L 676.277258 124.297281 \n", "L 676.299378 124.411819 \n", "L 676.343468 124.136185 \n", "L 676.453168 124.16712 \n", "L 676.563273 124.464972 \n", "L 676.607476 124.448854 \n", "L 676.651249 124.399714 \n", "L 676.695299 124.451255 \n", "L 676.717431 124.397897 \n", "L 676.739328 124.531597 \n", "L 676.981562 125.24351 \n", "L 677.091659 125.408838 \n", "L 677.113569 125.373103 \n", "L 677.488124 124.685445 \n", "L 677.532014 124.486493 \n", "L 677.730147 123.532825 \n", "L 677.752179 123.553483 \n", "L 677.774163 123.488513 \n", "L 678.126254 122.739188 \n", "L 678.280075 122.876906 \n", "L 678.456552 122.52939 \n", "L 678.50055 122.745169 \n", "L 678.544297 122.524215 \n", "L 678.676095 122.234171 \n", "L 678.720104 122.275486 \n", "L 678.741986 122.310777 \n", "L 678.763941 122.183003 \n", "L 678.89597 121.827552 \n", "L 678.917958 121.709377 \n", "L 679.005968 121.738654 \n", "L 679.028132 121.767291 \n", "L 679.072204 121.663896 \n", "L 679.160025 121.433791 \n", "L 679.622354 119.82924 \n", "L 679.754503 119.980144 \n", "L 679.864442 119.555371 \n", "L 679.886491 119.591439 \n", "L 680.040756 119.870531 \n", "L 680.062641 119.811264 \n", "L 680.238833 119.326164 \n", "L 680.260942 119.371566 \n", "L 680.590879 120.392167 \n", "L 680.612986 120.30567 \n", "L 680.832497 119.7582 \n", "L 681.008469 119.076648 \n", "L 681.052606 119.193689 \n", "L 681.074546 119.05954 \n", "L 681.250748 118.765503 \n", "L 681.492745 118.377989 \n", "L 681.602971 118.024778 \n", "L 681.625035 118.16065 \n", "L 681.669095 118.001215 \n", "L 681.713367 118.086526 \n", "L 681.888765 117.812028 \n", "L 681.932879 117.829229 \n", "L 681.954849 117.799461 \n", "L 682.240814 116.947015 \n", "L 682.262876 116.976079 \n", "L 682.329003 117.042412 \n", "L 682.350912 117.008295 \n", "L 682.724416 116.103186 \n", "L 682.768517 116.142589 \n", "L 682.834733 116.083106 \n", "L 683.032218 115.463163 \n", "L 683.075946 115.656501 \n", "L 683.428059 116.813514 \n", "L 683.471849 116.786847 \n", "L 683.493794 116.833982 \n", "L 683.515889 116.894585 \n", "L 683.537753 116.76672 \n", "L 683.559871 116.783613 \n", "L 683.647923 116.553175 \n", "L 683.713652 116.635976 \n", "L 683.911484 117.134184 \n", "L 684.131754 117.747503 \n", "L 684.153637 117.714166 \n", "L 684.175539 117.82693 \n", "L 684.373525 118.25693 \n", "L 684.395639 118.223472 \n", "L 684.417536 118.379603 \n", "L 684.571925 118.506133 \n", "L 684.704279 118.997455 \n", "L 684.748392 118.923809 \n", "L 684.792408 118.84753 \n", "L 684.836156 118.942945 \n", "L 684.990295 119.233104 \n", "L 685.078339 119.058322 \n", "L 685.100314 119.227012 \n", "L 685.188375 119.03701 \n", "L 685.232281 119.139915 \n", "L 685.408094 119.971081 \n", "L 685.474208 119.789581 \n", "L 685.628261 119.554505 \n", "L 685.650146 119.70204 \n", "L 685.694317 119.810509 \n", "L 685.738466 119.704288 \n", "L 686.002666 118.909197 \n", "L 686.024688 118.991602 \n", "L 686.06881 118.747653 \n", "L 686.28897 118.562649 \n", "L 686.311044 118.516064 \n", "L 686.332939 118.563709 \n", "L 686.640827 119.662789 \n", "L 686.838779 120.100173 \n", "L 686.860732 120.103744 \n", "L 687.058 120.950479 \n", "L 687.145775 120.96497 \n", "L 687.277738 121.174372 \n", "L 687.299594 121.090548 \n", "L 687.343512 121.070419 \n", "L 687.36551 121.174025 \n", "L 687.387428 121.113999 \n", "L 687.607383 122.092194 \n", "L 687.629298 122.131155 \n", "L 687.651232 121.98594 \n", "L 687.67311 121.961113 \n", "L 687.695178 122.026999 \n", "L 687.87122 122.658474 \n", "L 688.04686 122.930681 \n", "L 688.068994 122.892975 \n", "L 688.09102 122.994482 \n", "L 688.11289 123.10598 \n", "L 688.156793 122.946604 \n", "L 688.178917 122.959703 \n", "L 688.442687 122.355323 \n", "L 688.464705 122.38635 \n", "L 688.486771 122.458859 \n", "L 688.530985 122.29934 \n", "L 688.861235 121.032131 \n", "L 688.949343 121.172572 \n", "L 689.213511 121.600549 \n", "L 689.235668 121.567238 \n", "L 689.279857 121.426331 \n", "L 689.345949 121.496496 \n", "L 689.763998 121.778497 \n", "L 689.895798 122.215415 \n", "L 690.028049 121.89414 \n", "L 690.072215 122.006538 \n", "L 690.137955 121.907118 \n", "L 690.269517 122.238835 \n", "L 690.401413 122.553212 \n", "L 690.423403 122.488707 \n", "L 690.467332 122.626248 \n", "L 690.489475 122.619072 \n", "L 690.687752 123.58689 \n", "L 690.709592 123.563006 \n", "L 690.90766 123.185951 \n", "L 690.775606 123.607622 \n", "L 690.951747 123.286016 \n", "L 691.435712 124.827014 \n", "L 691.45773 124.785854 \n", "L 691.479596 124.814916 \n", "L 691.523371 124.732939 \n", "L 691.633203 124.587893 \n", "L 691.831503 125.2051 \n", "L 692.00744 125.461256 \n", "L 692.051428 125.32943 \n", "L 692.1172 125.455344 \n", "L 692.183337 125.336223 \n", "L 692.33757 125.768892 \n", "L 692.381615 125.552598 \n", "L 692.447668 125.254572 \n", "L 692.491693 125.281246 \n", "L 692.667373 125.676272 \n", "L 692.689399 125.593954 \n", "L 692.711504 125.722065 \n", "L 692.755239 125.621854 \n", "L 692.887535 125.956348 \n", "L 692.931539 125.856366 \n", "L 693.041397 125.84221 \n", "L 693.107535 125.528307 \n", "L 693.151528 125.61454 \n", "L 693.195488 125.773809 \n", "L 693.239295 125.651158 \n", "L 693.32731 125.517795 \n", "L 693.349424 125.570328 \n", "L 693.547459 125.954366 \n", "L 694.51565 132.338132 \n", "L 695.242081 133.975729 \n", "L 695.286046 133.791247 \n", "L 695.396181 133.707547 \n", "L 695.55035 134.367754 \n", "L 695.57228 134.326079 \n", "L 695.726326 134.779665 \n", "L 695.792254 134.652455 \n", "L 695.814309 134.648772 \n", "L 695.990548 135.253512 \n", "L 696.0126 135.219804 \n", "L 696.100793 135.376972 \n", "L 696.23267 135.576873 \n", "L 696.320482 135.235713 \n", "L 696.342586 135.305835 \n", "L 696.430471 135.83356 \n", "L 696.496401 135.77908 \n", "L 696.562673 135.920755 \n", "L 696.628437 135.858417 \n", "L 696.759918 135.67526 \n", "L 696.936014 136.173068 \n", "L 697.485939 138.390226 \n", "L 697.529912 138.257752 \n", "L 697.749847 138.462219 \n", "L 697.859872 138.757078 \n", "L 697.926034 139.294443 \n", "L 697.992183 139.228229 \n", "L 698.035924 139.145646 \n", "L 698.101964 139.234341 \n", "L 698.189963 139.569537 \n", "L 698.255949 139.500121 \n", "L 698.321788 139.42119 \n", "L 698.365794 139.458437 \n", "L 698.431817 139.67844 \n", "L 698.762045 140.671055 \n", "L 698.806162 140.619316 \n", "L 698.981716 141.048052 \n", "L 699.245882 142.081975 \n", "L 699.267784 141.994948 \n", "L 699.333872 142.186403 \n", "L 699.443706 142.311558 \n", "L 699.50977 142.22707 \n", "L 699.796015 142.01708 \n", "L 699.994147 142.841673 \n", "L 700.01604 142.836767 \n", "L 700.038151 142.825213 \n", "L 700.192445 143.286273 \n", "L 700.236413 143.248227 \n", "L 700.302535 143.29143 \n", "L 700.39089 143.171275 \n", "L 700.412972 143.288876 \n", "L 700.633035 144.213181 \n", "L 700.677048 144.3015 \n", "L 700.875059 144.973066 \n", "L 700.940965 144.822862 \n", "L 701.050831 144.677313 \n", "L 701.072995 144.702617 \n", "L 701.227076 144.681537 \n", "L 701.446723 145.430072 \n", "L 701.60087 145.217024 \n", "L 701.667053 145.096487 \n", "L 701.711129 145.233683 \n", "L 702.041211 146.590888 \n", "L 702.0632 146.560515 \n", "L 702.085285 146.663325 \n", "L 702.129523 146.63303 \n", "L 702.283652 147.005816 \n", "L 702.327631 146.954694 \n", "L 702.393506 146.991306 \n", "L 702.877314 146.621781 \n", "L 703.229337 148.039866 \n", "L 703.338976 148.209153 \n", "L 703.426932 147.766556 \n", "L 703.470665 147.937554 \n", "L 703.580625 148.322057 \n", "L 703.646632 148.077087 \n", "L 703.690685 147.997792 \n", "L 703.734542 148.154996 \n", "L 703.866241 148.281099 \n", "L 703.888097 148.36022 \n", "L 703.954135 148.299501 \n", "L 704.020002 148.378596 \n", "L 704.085996 148.172948 \n", "L 704.108158 148.282686 \n", "L 704.152274 148.065502 \n", "L 704.283997 148.029951 \n", "L 704.459793 148.298453 \n", "L 704.503767 148.218764 \n", "L 704.547955 148.242001 \n", "L 704.724069 148.701978 \n", "L 704.746003 148.680581 \n", "L 704.79022 148.674116 \n", "L 704.878164 148.449503 \n", "L 704.92196 148.532876 \n", "L 705.208454 148.930074 \n", "L 705.538301 149.300538 \n", "L 705.560308 149.240814 \n", "L 705.604443 149.225278 \n", "L 705.714447 149.011083 \n", "L 705.75843 149.050134 \n", "L 705.890309 149.347504 \n", "L 705.912264 149.281263 \n", "L 706.022483 149.151293 \n", "L 706.198435 148.579415 \n", "L 706.242485 148.589342 \n", "L 706.286606 148.70597 \n", "L 706.330542 148.548604 \n", "L 706.396952 148.340687 \n", "L 706.462931 148.511653 \n", "L 706.727179 148.774263 \n", "L 706.749336 148.722365 \n", "L 706.880977 148.55771 \n", "L 707.277484 149.305196 \n", "L 707.299649 149.277245 \n", "L 707.454023 149.43793 \n", "L 707.585986 149.958123 \n", "L 707.630037 150.175382 \n", "L 707.695884 150.108056 \n", "L 707.717976 150.040354 \n", "L 707.761886 150.17596 \n", "L 707.783923 150.17031 \n", "L 708.268416 151.298311 \n", "L 708.467073 151.797277 \n", "L 708.489027 151.774148 \n", "L 708.533154 151.705059 \n", "L 708.555111 151.816799 \n", "L 708.577272 151.811385 \n", "L 708.687128 151.942421 \n", "L 708.730945 151.907732 \n", "L 708.77505 151.890861 \n", "L 708.796998 151.936652 \n", "L 708.951032 152.792433 \n", "L 709.083056 152.679512 \n", "L 709.105028 152.664763 \n", "L 709.127096 152.743926 \n", "L 709.325278 153.444832 \n", "L 709.347123 153.392596 \n", "L 709.391084 153.33771 \n", "L 709.566651 153.969256 \n", "L 709.808105 154.863644 \n", "L 710.116053 155.159125 \n", "L 710.226288 155.064411 \n", "L 710.248144 155.09219 \n", "L 710.402368 155.443889 \n", "L 710.600807 155.084915 \n", "L 710.908721 155.576761 \n", "L 711.040673 155.400072 \n", "L 711.128552 155.673472 \n", "L 711.370924 156.418871 \n", "L 711.392789 156.386232 \n", "L 711.41495 156.463064 \n", "L 711.458984 156.482719 \n", "L 711.657139 156.891188 \n", "L 711.854992 157.347576 \n", "L 712.030768 157.957218 \n", "L 712.141047 158.029149 \n", "L 712.558764 158.56063 \n", "L 712.580717 158.51916 \n", "L 712.844375 158.494118 \n", "L 712.88814 158.590649 \n", "L 712.932129 158.45364 \n", "L 713.130183 158.205632 \n", "L 713.174072 158.278349 \n", "L 713.328267 158.472737 \n", "L 713.438581 158.124584 \n", "L 713.46068 158.183993 \n", "L 713.680461 158.538352 \n", "L 713.724616 158.445201 \n", "L 713.746459 158.42551 \n", "L 713.768486 158.246482 \n", "L 713.85628 158.436567 \n", "L 713.944046 158.485736 \n", "L 713.966133 158.439448 \n", "L 714.119915 158.457377 \n", "L 714.295697 158.141967 \n", "L 714.537413 158.592524 \n", "L 714.669256 158.560683 \n", "L 714.779108 158.560317 \n", "L 714.932815 158.879609 \n", "L 715.131045 158.748863 \n", "L 715.153078 158.771828 \n", "L 715.373141 159.262222 \n", "L 715.526606 159.515992 \n", "L 715.658583 159.688195 \n", "L 715.702734 159.619699 \n", "L 716.120935 160.021394 \n", "L 716.252933 160.078965 \n", "L 716.27491 160.055399 \n", "L 716.318843 160.116306 \n", "L 716.759125 160.635719 \n", "L 716.957408 160.145322 \n", "L 717.023237 160.221371 \n", "L 717.089146 160.231873 \n", "L 717.154741 159.913158 \n", "L 717.220991 160.010673 \n", "L 717.243073 160.020095 \n", "L 717.264923 159.886884 \n", "L 717.352875 159.984152 \n", "L 717.485004 159.936714 \n", "L 717.6175 160.177203 \n", "L 717.529193 159.881345 \n", "L 717.661673 160.017731 \n", "L 717.815585 159.559843 \n", "L 717.859784 159.68309 \n", "L 717.903984 159.588303 \n", "L 717.925963 159.532498 \n", "L 717.991959 159.561215 \n", "L 718.080077 159.631169 \n", "L 718.102186 159.606527 \n", "L 718.124357 159.518318 \n", "L 718.212436 159.555836 \n", "L 718.256634 159.67134 \n", "L 718.345091 159.579108 \n", "L 718.389029 159.459892 \n", "L 718.45493 159.53033 \n", "L 718.521119 159.580986 \n", "L 718.543031 159.545343 \n", "L 718.565185 159.499539 \n", "L 718.631022 159.578819 \n", "L 718.675045 159.687046 \n", "L 718.873679 160.168005 \n", "L 718.89585 160.148045 \n", "L 718.917873 160.252574 \n", "L 719.094091 160.500704 \n", "L 719.138226 160.446826 \n", "L 719.182297 160.542303 \n", "L 719.270103 160.838879 \n", "L 719.292226 160.717798 \n", "L 719.446398 160.365221 \n", "L 719.534746 160.176982 \n", "L 719.57885 160.020915 \n", "L 719.645015 160.103778 \n", "L 719.798789 160.534745 \n", "L 719.820703 160.410334 \n", "L 720.041399 160.197073 \n", "L 720.085434 160.251694 \n", "L 720.261694 160.492476 \n", "L 720.305715 160.319765 \n", "L 720.371834 160.374256 \n", "L 720.50401 160.515262 \n", "L 720.548036 160.373972 \n", "L 720.613965 160.527677 \n", "L 720.679864 160.68734 \n", "L 720.723888 160.562437 \n", "L 720.855724 160.459633 \n", "L 720.877642 160.475685 \n", "L 720.987894 160.355856 \n", "L 721.120228 159.894428 \n", "L 721.164005 159.925289 \n", "L 721.31833 160.05497 \n", "L 721.494467 160.445129 \n", "L 721.582518 160.233592 \n", "L 721.604597 160.239288 \n", "L 721.626603 160.148487 \n", "L 721.781243 159.897571 \n", "L 721.891675 159.697472 \n", "L 721.913769 159.769017 \n", "L 722.266078 159.536131 \n", "L 722.309971 159.63406 \n", "L 722.420084 159.688497 \n", "L 722.442091 159.615076 \n", "L 722.50802 159.776257 \n", "L 722.771915 160.423688 \n", "L 722.81611 160.318908 \n", "L 722.94793 159.849547 \n", "L 723.036082 159.954386 \n", "L 723.409759 160.774438 \n", "L 723.453796 160.727171 \n", "L 723.519766 160.724456 \n", "L 723.739947 161.240965 \n", "L 723.761995 161.230785 \n", "L 723.783936 161.211428 \n", "L 723.806063 161.23935 \n", "L 724.07041 161.984948 \n", "L 724.092453 161.948415 \n", "L 724.643239 162.85172 \n", "L 724.731214 162.749326 \n", "L 724.753189 162.764754 \n", "L 725.127834 163.77989 \n", "L 725.325952 164.280743 \n", "L 725.4799 164.392028 \n", "L 725.656229 164.666768 \n", "L 725.678312 164.644755 \n", "L 725.700257 164.7119 \n", "L 725.722325 164.708345 \n", "L 726.031802 165.319289 \n", "L 726.0978 165.294544 \n", "L 726.119671 165.336834 \n", "L 726.185987 165.262363 \n", "L 726.251966 165.353712 \n", "L 726.450053 164.885228 \n", "L 726.494101 165.032733 \n", "L 726.692851 165.889361 \n", "L 726.715005 165.85834 \n", "L 726.825253 165.81157 \n", "L 727.023884 166.331856 \n", "L 727.24451 167.171804 \n", "L 727.26656 167.164175 \n", "L 727.574841 167.377194 \n", "L 727.728997 167.772961 \n", "L 727.817169 167.702799 \n", "L 727.993656 168.385156 \n", "L 728.015746 168.351104 \n", "L 728.235748 168.334473 \n", "L 728.522191 169.109615 \n", "L 728.544353 169.139228 \n", "L 728.588059 169.040586 \n", "L 728.609922 169.052726 \n", "L 728.719905 168.984261 \n", "L 728.741931 168.997933 \n", "L 728.962315 169.177772 \n", "L 729.028218 169.088783 \n", "L 729.446753 169.017766 \n", "L 729.578728 168.858955 \n", "L 729.689258 168.876079 \n", "L 729.711156 168.835073 \n", "L 729.755359 168.789239 \n", "L 729.799704 168.847781 \n", "L 729.97592 168.9273 \n", "L 730.08615 168.758565 \n", "L 730.152228 168.831867 \n", "L 730.284141 169.045255 \n", "L 730.306318 168.998831 \n", "L 730.372483 168.91215 \n", "L 730.460782 169.126625 \n", "L 730.592706 168.882056 \n", "L 730.636771 168.911853 \n", "L 730.658855 168.836361 \n", "L 730.725194 168.575366 \n", "L 730.901704 168.249301 \n", "L 731.122038 168.558956 \n", "L 731.276332 168.231883 \n", "L 731.452832 168.205242 \n", "L 731.47492 168.145772 \n", "L 731.519157 168.239313 \n", "L 731.541312 168.209851 \n", "L 731.915654 168.995197 \n", "L 731.937706 168.987996 \n", "L 732.003715 169.152713 \n", "L 732.047934 169.143843 \n", "L 732.201942 168.733884 \n", "L 732.22399 168.771778 \n", "L 732.268049 168.830611 \n", "L 732.312261 168.701609 \n", "L 732.422474 168.828811 \n", "L 732.466647 168.759675 \n", "L 732.730991 168.18774 \n", "L 732.97353 168.569764 \n", "L 733.105994 168.632734 \n", "L 733.127922 168.585135 \n", "L 733.194029 168.550323 \n", "L 733.237934 168.60149 \n", "L 733.370033 168.773686 \n", "L 733.611756 168.492767 \n", "L 733.721686 168.52316 \n", "L 733.787879 168.484455 \n", "L 733.853767 168.392495 \n", "L 733.875797 168.449387 \n", "L 733.9199 168.573912 \n", "L 733.96405 168.432174 \n", "L 734.052093 168.117524 \n", "L 734.118194 168.168839 \n", "L 734.184275 168.271525 \n", "L 734.228405 168.258867 \n", "L 734.338288 168.297483 \n", "L 734.404397 168.407598 \n", "L 734.558493 168.618878 \n", "L 734.646595 168.783614 \n", "L 734.690656 168.767855 \n", "L 734.778697 168.831164 \n", "L 735.086865 169.089808 \n", "L 735.329131 168.389014 \n", "L 735.46135 168.142036 \n", "L 735.483324 168.166892 \n", "L 735.505179 168.248367 \n", "L 735.549215 168.140693 \n", "L 735.59317 168.22386 \n", "L 735.791639 167.83964 \n", "L 735.813672 167.883983 \n", "L 735.901887 168.061848 \n", "L 735.946105 167.941114 \n", "L 735.967968 167.867958 \n", "L 736.01225 168.064507 \n", "L 736.320984 168.245212 \n", "L 736.342826 168.20216 \n", "L 736.519194 167.86924 \n", "L 736.563146 167.735223 \n", "L 736.629049 167.83246 \n", "L 736.805475 168.418449 \n", "L 736.871459 168.35564 \n", "L 737.025651 168.564788 \n", "L 737.113436 168.820069 \n", "L 737.179607 168.747213 \n", "L 737.400132 168.654366 \n", "L 737.642156 169.03442 \n", "L 737.686407 168.940089 \n", "L 737.752521 169.023197 \n", "L 737.840387 169.140683 \n", "L 737.862323 169.063727 \n", "L 737.928386 168.875136 \n", "L 737.994622 168.978633 \n", "L 738.038679 169.067303 \n", "L 738.060785 168.978789 \n", "L 738.082767 168.895324 \n", "L 738.149178 168.998002 \n", "L 738.303709 169.062205 \n", "L 738.678497 168.372106 \n", "L 738.722643 168.407151 \n", "L 738.92087 168.507055 \n", "L 739.075081 168.510267 \n", "L 739.163436 168.688101 \n", "L 739.229024 168.554879 \n", "L 739.426732 168.170229 \n", "L 739.602039 168.568158 \n", "L 739.624153 168.558591 \n", "L 740.328366 166.975012 \n", "L 740.416618 167.049923 \n", "L 740.438769 166.995006 \n", "L 740.460622 166.947583 \n", "L 740.526488 167.053961 \n", "L 740.614578 167.141064 \n", "L 741.076449 168.01543 \n", "L 741.098505 167.906965 \n", "L 741.208336 167.740014 \n", "L 741.252346 167.517455 \n", "L 741.340537 167.602412 \n", "L 741.780628 167.352606 \n", "L 742.132963 167.964585 \n", "L 742.176742 167.885703 \n", "L 742.308875 167.61914 \n", "L 742.331067 167.668378 \n", "L 742.397135 167.831677 \n", "L 742.441078 167.739206 \n", "L 742.617205 167.604501 \n", "L 742.639233 167.651425 \n", "L 742.77115 167.712188 \n", "L 742.793193 167.698802 \n", "L 742.925146 167.597941 \n", "L 742.947133 167.632503 \n", "L 742.99119 167.64547 \n", "L 743.013189 167.569477 \n", "L 743.23395 167.153958 \n", "L 743.541977 166.184836 \n", "L 743.608118 165.992979 \n", "L 743.784234 165.724986 \n", "L 743.938235 165.606816 \n", "L 743.960118 165.648311 \n", "L 744.025973 165.743366 \n", "L 744.06996 165.629273 \n", "L 744.09214 165.573923 \n", "L 744.135981 165.702589 \n", "L 744.180042 165.847057 \n", "L 744.246141 165.678263 \n", "L 744.488685 165.503636 \n", "L 744.554829 165.652505 \n", "L 744.599028 165.616517 \n", "L 744.664933 165.503989 \n", "L 744.730984 165.581266 \n", "L 744.973197 166.114006 \n", "L 745.017181 166.199526 \n", "L 745.06124 166.056331 \n", "L 745.083399 166.104035 \n", "L 745.105447 166.087932 \n", "L 745.149303 166.159401 \n", "L 745.369407 166.720235 \n", "L 745.567633 167.328784 \n", "L 745.809631 167.844434 \n", "L 745.897922 167.837407 \n", "L 745.919907 167.899322 \n", "L 746.117898 168.056093 \n", "L 746.250145 168.039621 \n", "L 746.272303 168.068005 \n", "L 746.338156 167.998782 \n", "L 746.360258 167.98784 \n", "L 746.382419 168.084055 \n", "L 746.53701 168.30154 \n", "L 746.559094 168.285857 \n", "L 746.713211 168.649642 \n", "L 746.867185 168.95018 \n", "L 746.977381 169.020341 \n", "L 747.198 169.655804 \n", "L 747.26397 169.764597 \n", "L 747.373564 169.860657 \n", "L 747.4175 169.784516 \n", "L 747.461727 169.931992 \n", "L 747.704624 170.422664 \n", "L 748.343078 170.906231 \n", "L 748.431223 171.170435 \n", "L 748.519351 171.144846 \n", "L 748.651559 171.232357 \n", "L 748.695587 171.329668 \n", "L 749.246104 173.646539 \n", "L 749.862452 175.010497 \n", "L 749.950857 174.923078 \n", "L 750.061183 174.674717 \n", "L 750.127403 174.73719 \n", "L 750.259631 174.833325 \n", "L 750.52435 174.572626 \n", "L 750.546415 174.634473 \n", "L 750.678867 174.654378 \n", "L 750.766935 174.630667 \n", "L 750.943321 174.209874 \n", "L 750.987467 174.293593 \n", "L 751.031631 174.192425 \n", "L 751.09784 174.123268 \n", "L 751.11997 174.200493 \n", "L 751.186111 174.248596 \n", "L 751.2083 174.219638 \n", "L 751.4503 173.804743 \n", "L 751.53822 173.688909 \n", "L 751.911704 173.445469 \n", "L 751.955468 173.537863 \n", "L 752.021683 173.456448 \n", "L 752.241646 173.395814 \n", "L 752.374118 173.156275 \n", "L 752.462366 173.23945 \n", "L 752.77031 173.393316 \n", "L 752.880435 173.133033 \n", "L 752.902402 173.208672 \n", "L 752.990473 172.934826 \n", "L 753.122852 172.618933 \n", "L 753.144969 172.645026 \n", "L 753.167074 172.637811 \n", "L 753.233327 172.48239 \n", "L 753.27761 172.51354 \n", "L 753.497742 172.398295 \n", "L 753.673239 171.936565 \n", "L 753.717208 171.914719 \n", "L 753.871742 171.575086 \n", "L 754.202764 171.781653 \n", "L 754.269067 171.731151 \n", "L 754.313265 171.771436 \n", "L 754.335307 171.798764 \n", "L 754.357344 171.722859 \n", "L 754.511266 171.360636 \n", "L 754.555081 171.388862 \n", "L 754.621116 171.408478 \n", "L 754.643258 171.376353 \n", "L 754.906463 170.954531 \n", "L 754.928474 171.006033 \n", "L 754.972321 170.972875 \n", "L 755.126541 170.677165 \n", "L 755.236631 170.556812 \n", "L 755.302972 170.316551 \n", "L 755.369222 170.381932 \n", "L 755.589183 170.593309 \n", "L 755.721103 170.525571 \n", "L 756.073795 170.012032 \n", "L 756.140065 170.089748 \n", "L 756.206352 170.126598 \n", "L 756.338144 170.373004 \n", "L 756.360185 170.355401 \n", "L 756.448448 170.262734 \n", "L 756.470299 170.171756 \n", "L 756.536177 170.315578 \n", "L 756.734562 170.071207 \n", "L 756.756604 170.111973 \n", "L 756.88916 170.27374 \n", "L 757.065039 169.966645 \n", "L 757.196974 169.679579 \n", "L 757.219064 169.68698 \n", "L 757.241066 169.67011 \n", "L 757.285231 169.773554 \n", "L 757.351439 169.814409 \n", "L 757.373485 169.697269 \n", "L 757.505432 169.400237 \n", "L 757.549536 169.420636 \n", "L 757.637502 169.526197 \n", "L 757.659498 169.619312 \n", "L 757.747452 169.519613 \n", "L 758.25392 168.650373 \n", "L 758.341711 168.804983 \n", "L 758.452087 168.873389 \n", "L 758.474186 168.847412 \n", "L 758.562096 168.88084 \n", "L 758.716154 169.048937 \n", "L 758.782254 169.135389 \n", "L 758.8043 169.108234 \n", "L 758.958689 168.857591 \n", "L 758.980729 168.84707 \n", "L 759.002566 168.89645 \n", "L 759.355645 169.676781 \n", "L 759.399488 169.641086 \n", "L 759.553835 169.417705 \n", "L 759.708202 169.721623 \n", "L 760.0165 170.123348 \n", "L 760.148452 169.987891 \n", "L 760.280503 170.214931 \n", "L 760.412613 170.439495 \n", "L 760.4786 170.284408 \n", "L 760.544659 170.360354 \n", "L 760.677097 170.375768 \n", "L 760.765169 170.414653 \n", "L 760.787009 170.36196 \n", "L 760.984864 169.995251 \n", "L 761.0069 170.088245 \n", "L 761.160921 170.192836 \n", "L 761.226959 170.344657 \n", "L 761.447379 170.940333 \n", "L 761.469467 170.910614 \n", "L 761.513706 171.033525 \n", "L 761.712053 171.579403 \n", "L 761.73394 171.549519 \n", "L 761.976567 171.93372 \n", "L 761.998698 171.904168 \n", "L 762.131089 171.796741 \n", "L 762.153192 171.817664 \n", "L 762.219316 171.900854 \n", "L 762.241379 171.837282 \n", "L 762.307566 171.591254 \n", "L 762.395929 171.691814 \n", "L 762.483956 172.031675 \n", "L 762.770239 173.118495 \n", "L 762.814486 173.013638 \n", "L 762.968622 172.913482 \n", "L 763.056775 172.960772 \n", "L 763.078806 172.927347 \n", "L 763.343658 172.636491 \n", "L 763.365613 172.662069 \n", "L 763.520112 172.765058 \n", "L 763.542016 172.661321 \n", "L 763.608269 172.598669 \n", "L 763.652131 172.670896 \n", "L 763.850277 173.156397 \n", "L 764.268536 173.465734 \n", "L 764.312712 173.512665 \n", "L 764.422852 173.684234 \n", "L 764.444999 173.676187 \n", "L 764.620929 174.006349 \n", "L 764.818842 173.692854 \n", "L 764.951026 173.562461 \n", "L 764.972913 173.586321 \n", "L 765.060622 173.613929 \n", "L 765.193369 173.520991 \n", "L 765.281104 173.495277 \n", "L 765.303277 173.547678 \n", "L 765.611446 174.28849 \n", "L 765.919983 174.857874 \n", "L 766.14071 175.385659 \n", "L 766.162788 175.363857 \n", "L 766.206818 175.465802 \n", "L 766.471559 176.194091 \n", "L 766.669765 176.217072 \n", "L 766.977899 175.723107 \n", "L 767.021845 175.602573 \n", "L 767.220464 175.1759 \n", "L 767.330624 175.223305 \n", "L 767.352756 175.283407 \n", "L 767.39664 175.167125 \n", "L 767.484758 175.025259 \n", "L 767.529061 175.056061 \n", "L 767.749482 175.164788 \n", "L 767.925547 175.273337 \n", "L 768.322202 174.213159 \n", "L 768.542208 174.558726 \n", "L 768.564143 174.542051 \n", "L 768.630141 174.567332 \n", "L 768.674337 174.525654 \n", "L 768.740644 174.604899 \n", "L 768.894758 174.9253 \n", "L 768.938991 174.819457 \n", "L 768.983172 174.731478 \n", "L 769.027202 174.870627 \n", "L 769.137262 175.113555 \n", "L 769.181553 175.000817 \n", "L 769.490138 174.664719 \n", "L 769.53422 174.70074 \n", "L 769.666787 174.793967 \n", "L 769.688949 174.768453 \n", "L 769.865516 174.796795 \n", "L 769.953623 174.921704 \n", "L 769.997642 174.834073 \n", "L 770.284005 174.887375 \n", "L 770.481967 175.141952 \n", "L 770.614295 175.065235 \n", "L 770.724468 175.149328 \n", "L 770.76846 175.074117 \n", "L 770.834633 174.973965 \n", "L 771.253038 173.876397 \n", "L 771.561571 173.3667 \n", "L 771.605505 173.404103 \n", "L 771.803605 173.372978 \n", "L 771.825687 173.402676 \n", "L 771.847806 173.366644 \n", "L 771.980051 172.993576 \n", "L 772.024252 173.044566 \n", "L 772.068335 173.034972 \n", "L 772.090396 172.966421 \n", "L 772.222226 173.001772 \n", "L 772.398637 173.294171 \n", "L 772.420674 173.249149 \n", "L 772.70739 173.789096 \n", "L 772.773567 173.716086 \n", "L 773.03854 173.506578 \n", "L 773.060665 173.545198 \n", "L 773.082616 173.569202 \n", "L 773.12644 173.460389 \n", "L 773.170521 173.424294 \n", "L 773.214594 173.478646 \n", "L 773.63334 173.977819 \n", "L 773.831235 174.376882 \n", "L 774.029429 174.190312 \n", "L 774.117752 174.24659 \n", "L 774.161871 174.164221 \n", "L 774.471112 174.37401 \n", "L 774.51535 174.458278 \n", "L 774.581389 174.369364 \n", "L 774.647869 174.305944 \n", "L 774.669791 174.423845 \n", "L 774.735844 174.598227 \n", "L 774.823963 174.492495 \n", "L 774.846064 174.477867 \n", "L 774.868059 174.529373 \n", "L 775.000506 174.582613 \n", "L 775.176626 174.689929 \n", "L 775.264772 174.754409 \n", "L 775.286709 174.68007 \n", "L 775.308784 174.661376 \n", "L 775.352972 174.720366 \n", "L 775.440926 174.873203 \n", "L 775.485141 174.792504 \n", "L 775.529318 174.807613 \n", "L 775.57338 174.756831 \n", "L 775.794283 174.533455 \n", "L 775.904082 174.333127 \n", "L 775.926185 174.356178 \n", "L 776.102297 174.573331 \n", "L 776.212384 174.481861 \n", "L 776.278507 174.409709 \n", "L 776.322362 174.47851 \n", "L 776.388634 174.571656 \n", "L 776.45464 174.68067 \n", "L 776.498909 174.587241 \n", "L 776.609219 174.470341 \n", "L 776.631395 174.567569 \n", "L 776.719399 174.469739 \n", "L 776.74155 174.398317 \n", "L 776.807615 174.535931 \n", "L 776.829719 174.625402 \n", "L 776.895703 174.464644 \n", "L 776.917599 174.512805 \n", "L 776.961857 174.38828 \n", "L 777.072502 174.188557 \n", "L 777.183039 174.032349 \n", "L 777.205181 174.04421 \n", "L 777.602295 174.787325 \n", "L 777.668392 174.844765 \n", "L 777.80064 174.910295 \n", "L 778.219745 174.554675 \n", "L 778.263905 174.734799 \n", "L 778.418102 174.837158 \n", "L 778.660649 175.384085 \n", "L 778.726794 175.286396 \n", "L 778.771087 175.385506 \n", "L 778.792956 175.47982 \n", "L 778.836936 175.374954 \n", "L 778.968537 175.117446 \n", "L 779.056609 175.227271 \n", "L 779.188559 174.906365 \n", "L 779.232346 174.847447 \n", "L 779.473891 174.303348 \n", "L 779.495843 174.326771 \n", "L 779.539837 174.373873 \n", "L 779.606025 174.317255 \n", "L 779.627923 174.302178 \n", "L 779.650078 174.394186 \n", "L 779.935998 174.762868 \n", "L 779.980175 174.668722 \n", "L 780.068219 174.594108 \n", "L 780.090084 174.692185 \n", "L 780.266582 174.504573 \n", "L 780.288765 174.544332 \n", "L 780.332892 174.597557 \n", "L 780.376903 174.531591 \n", "L 780.619243 173.900954 \n", "L 780.707239 173.749804 \n", "L 781.124561 172.68421 \n", "L 781.212734 172.772922 \n", "L 781.366905 173.122962 \n", "L 781.411024 173.069917 \n", "L 781.698364 172.679822 \n", "L 781.72043 172.777582 \n", "L 781.808539 172.666421 \n", "L 781.918661 172.511169 \n", "L 781.962791 172.485201 \n", "L 782.007104 172.525431 \n", "L 782.11735 172.588953 \n", "L 782.139202 172.569833 \n", "L 782.161085 172.52992 \n", "L 782.205307 172.65342 \n", "L 782.227369 172.719199 \n", "L 782.293559 172.573569 \n", "L 782.381854 172.472335 \n", "L 782.403806 172.599149 \n", "L 782.689876 172.853252 \n", "L 782.711932 172.820691 \n", "L 782.954169 172.420681 \n", "L 783.108048 172.454278 \n", "L 783.174152 172.484117 \n", "L 783.196156 172.457654 \n", "L 783.482984 171.957479 \n", "L 783.725345 171.78578 \n", "L 783.791533 171.478125 \n", "L 783.857728 171.592336 \n", "L 784.078282 171.292411 \n", "L 784.100392 171.330994 \n", "L 784.232493 171.368158 \n", "L 784.254334 171.342343 \n", "L 784.29834 171.280611 \n", "L 784.320446 171.37611 \n", "L 784.474409 171.836594 \n", "L 784.540621 171.745813 \n", "L 784.562769 171.66677 \n", "L 784.584886 171.871185 \n", "L 785.003158 172.684404 \n", "L 785.025256 172.671417 \n", "L 785.179168 172.61234 \n", "L 785.289314 172.272949 \n", "L 785.333582 172.299209 \n", "L 785.509619 172.412401 \n", "L 785.796258 172.776064 \n", "L 785.884553 172.674515 \n", "L 785.906628 172.723056 \n", "L 786.237375 173.31597 \n", "L 786.303444 173.436223 \n", "L 786.566999 174.120929 \n", "L 786.809019 173.509715 \n", "L 786.85323 173.523345 \n", "L 786.919113 173.619565 \n", "L 787.117299 173.780982 \n", "L 787.139256 173.723081 \n", "L 787.183216 173.839454 \n", "L 787.403744 174.29499 \n", "L 787.55759 174.624938 \n", "L 787.975677 175.074283 \n", "L 787.997578 175.047004 \n", "L 788.129413 174.957191 \n", "L 788.151556 174.977766 \n", "L 788.217847 174.890049 \n", "L 788.350202 174.658929 \n", "L 788.394197 174.556443 \n", "L 788.43847 174.66476 \n", "L 788.614014 174.802839 \n", "L 788.701771 174.865008 \n", "L 788.94318 175.437507 \n", "L 789.119207 175.768228 \n", "L 789.405002 176.415703 \n", "L 789.536666 176.385338 \n", "L 789.647107 176.255962 \n", "L 789.668965 176.308951 \n", "L 789.778868 176.433532 \n", "L 789.822932 176.379443 \n", "L 789.866775 176.362452 \n", "L 789.888659 176.420548 \n", "L 790.131142 176.951738 \n", "L 790.396228 177.227461 \n", "L 790.594978 176.932195 \n", "L 790.770916 177.081821 \n", "L 790.880881 177.157377 \n", "L 790.902968 177.202997 \n", "L 790.969087 177.123486 \n", "L 791.013176 177.171961 \n", "L 791.365878 176.87651 \n", "L 791.476389 177.017372 \n", "L 791.498568 176.943926 \n", "L 791.652757 176.801457 \n", "L 791.674876 176.817906 \n", "L 791.939053 177.02638 \n", "L 792.357421 177.953289 \n", "L 792.467382 177.81289 \n", "L 792.598958 177.588018 \n", "L 792.642942 177.668083 \n", "L 792.730943 177.669165 \n", "L 792.951187 177.111698 \n", "L 792.995287 177.172993 \n", "L 793.083538 177.115732 \n", "L 793.259436 177.041672 \n", "L 793.34727 177.164491 \n", "L 793.391325 177.079817 \n", "L 793.413236 177.017045 \n", "L 793.479382 177.074185 \n", "L 793.698431 177.301576 \n", "L 793.742539 177.35635 \n", "L 793.874277 177.650962 \n", "L 793.940547 177.55232 \n", "L 794.247921 176.982472 \n", "L 794.401879 176.941243 \n", "L 794.842183 176.082181 \n", "L 794.908016 176.150271 \n", "L 794.929922 176.201583 \n", "L 794.995494 176.073737 \n", "L 795.017342 176.136714 \n", "L 795.10559 176.13365 \n", "L 795.127697 176.194723 \n", "L 795.193784 176.205382 \n", "L 795.282075 176.031481 \n", "L 795.326117 176.04253 \n", "L 795.45842 175.715353 \n", "L 795.809783 174.694652 \n", "L 795.897755 174.715908 \n", "L 796.095015 175.219712 \n", "L 796.116997 175.162095 \n", "L 796.161102 175.279425 \n", "L 796.29346 175.355398 \n", "L 796.315595 175.309887 \n", "L 796.535122 175.184355 \n", "L 796.557266 175.228126 \n", "L 797.01903 176.318885 \n", "L 797.084884 176.208905 \n", "L 797.371111 175.819564 \n", "L 797.392991 175.873178 \n", "L 797.52453 175.941094 \n", "L 797.63482 175.755553 \n", "L 797.678795 175.8455 \n", "L 798.184448 175.909138 \n", "L 798.448128 176.156402 \n", "L 798.79999 175.695169 \n", "L 798.844193 175.779379 \n", "L 798.910152 175.993476 \n", "L 798.976146 175.895801 \n", "L 799.108096 175.822937 \n", "L 799.151983 175.799005 \n", "L 799.218238 175.864843 \n", "L 799.371703 175.795605 \n", "L 799.415564 175.899021 \n", "L 799.701408 176.310879 \n", "L 799.832831 176.489971 \n", "L 799.854942 176.414711 \n", "L 799.921128 176.476888 \n", "L 800.141172 177.163965 \n", "L 800.229347 177.095719 \n", "L 800.427445 176.684659 \n", "L 800.471396 176.706626 \n", "L 800.603731 176.726558 \n", "L 800.691917 176.72529 \n", "L 800.735819 176.869712 \n", "L 800.801595 176.787908 \n", "L 800.933686 176.8816 \n", "L 801.28666 177.642918 \n", "L 801.308645 177.635155 \n", "L 801.506364 177.130531 \n", "L 801.550507 177.145872 \n", "L 801.616695 177.218214 \n", "L 801.638765 177.295559 \n", "L 801.704638 177.168557 \n", "L 801.748623 177.028838 \n", "L 801.81455 177.099351 \n", "L 802.056007 177.448435 \n", "L 802.143699 177.354371 \n", "L 802.518099 176.648185 \n", "L 802.738035 176.82252 \n", "L 802.759947 176.792451 \n", "L 802.781901 176.813601 \n", "L 802.825753 176.744414 \n", "L 803.023861 176.489831 \n", "L 803.067745 176.600749 \n", "L 803.133699 176.494077 \n", "L 803.309457 176.170121 \n", "L 803.397243 176.241161 \n", "L 803.572602 176.526147 \n", "L 803.879569 176.643028 \n", "L 803.945628 176.596248 \n", "L 804.099888 176.905328 \n", "L 804.275389 177.224742 \n", "L 804.319244 177.117456 \n", "L 804.582519 176.85194 \n", "L 804.868616 177.223683 \n", "L 804.978583 177.334523 \n", "L 805.044408 177.274634 \n", "L 805.308099 176.785036 \n", "L 805.329989 176.815532 \n", "L 805.418376 176.940343 \n", "L 805.462086 176.901749 \n", "L 805.484223 176.881085 \n", "L 805.528421 176.974935 \n", "L 805.57272 176.980764 \n", "L 805.682886 176.86357 \n", "L 805.704745 176.870594 \n", "L 806.078167 177.362916 \n", "L 806.100254 177.334174 \n", "L 806.166271 177.338342 \n", "L 806.188339 177.372358 \n", "L 806.496789 177.613994 \n", "L 806.606622 177.442388 \n", "L 806.738545 177.226264 \n", "L 806.848178 177.261165 \n", "L 806.804416 177.178204 \n", "L 806.870249 177.230837 \n", "L 806.958206 177.253863 \n", "L 807.090714 177.510955 \n", "L 807.11256 177.490675 \n", "L 807.178681 177.388355 \n", "L 807.22296 177.457375 \n", "L 807.310725 177.495207 \n", "L 807.332592 177.469115 \n", "L 807.465163 177.185021 \n", "L 807.509168 177.209843 \n", "L 807.531023 177.138694 \n", "L 807.596907 177.221864 \n", "L 807.773159 177.319898 \n", "L 807.839488 177.371074 \n", "L 808.103939 177.773009 \n", "L 808.434519 177.7764 \n", "L 808.478463 177.846316 \n", "L 808.522464 177.731486 \n", "L 808.654331 177.601655 \n", "L 808.676433 177.628185 \n", "L 808.830551 177.748455 \n", "L 809.006875 177.59684 \n", "L 809.249403 177.334108 \n", "L 809.271285 177.371219 \n", "L 809.337571 177.296322 \n", "L 809.403608 177.129134 \n", "L 809.469611 177.166427 \n", "L 809.667257 176.980189 \n", "L 809.689366 177.004942 \n", "L 809.886784 177.091047 \n", "L 809.952816 176.947776 \n", "L 810.01854 176.97558 \n", "L 810.062425 177.142639 \n", "L 810.150487 177.091601 \n", "L 810.238365 177.013521 \n", "L 810.414155 176.81935 \n", "L 810.810174 177.218255 \n", "L 810.85423 177.140622 \n", "L 811.250256 176.776771 \n", "L 811.338253 176.782521 \n", "L 811.404285 176.625555 \n", "L 811.491906 176.663484 \n", "L 811.557749 176.612594 \n", "L 811.601679 176.477151 \n", "L 811.689271 176.504276 \n", "L 811.996529 176.439913 \n", "L 812.194315 176.753633 \n", "L 812.238318 176.716045 \n", "L 812.369761 176.54306 \n", "L 812.391658 176.57165 \n", "L 812.457741 176.658133 \n", "L 812.523523 176.549125 \n", "L 812.545473 176.502787 \n", "L 812.589332 176.581584 \n", "L 812.764911 177.015398 \n", "L 812.786858 176.99412 \n", "L 813.072542 177.123509 \n", "L 813.09444 176.994218 \n", "L 813.18263 176.736508 \n", "L 813.248654 176.790083 \n", "L 813.645589 176.271138 \n", "L 813.689615 176.31198 \n", "L 813.77791 176.447199 \n", "L 813.932114 176.634931 \n", "L 813.953964 176.608491 \n", "L 814.173987 176.813331 \n", "L 814.394181 176.919267 \n", "L 814.460102 176.773873 \n", "L 814.525888 176.970445 \n", "L 814.547815 177.051738 \n", "L 814.635716 176.955548 \n", "L 814.657659 176.924389 \n", "L 814.72394 177.003264 \n", "L 814.988195 177.364427 \n", "L 814.767975 176.958682 \n", "L 815.032224 177.243162 \n", "L 815.141939 177.275565 \n", "L 815.252017 177.232614 \n", "L 815.317657 177.286552 \n", "L 815.493756 177.611865 \n", "L 815.515838 177.580098 \n", "L 815.625669 177.386024 \n", "L 815.669656 177.406282 \n", "L 815.757544 177.485376 \n", "L 815.801394 177.403687 \n", "L 815.977279 177.186828 \n", "L 815.999213 177.218606 \n", "L 816.108992 177.194661 \n", "L 816.460998 176.947358 \n", "L 816.505054 176.935215 \n", "L 816.593057 176.730637 \n", "L 816.636993 176.79108 \n", "L 816.680959 176.777503 \n", "L 816.702874 176.861873 \n", "L 816.878951 177.151747 \n", "L 816.944782 177.081961 \n", "L 817.032607 176.97524 \n", "L 817.054457 177.038512 \n", "L 817.076643 177.143923 \n", "L 817.164517 177.101071 \n", "L 817.252526 177.183278 \n", "L 817.296507 177.258366 \n", "L 817.340408 177.121631 \n", "L 817.472489 177.1531 \n", "L 817.670132 177.260693 \n", "L 817.779994 177.336995 \n", "L 817.911627 177.503101 \n", "L 817.977563 177.402769 \n", "L 818.043842 177.432875 \n", "L 818.373435 177.044067 \n", "L 818.395404 177.068783 \n", "L 818.680914 177.756467 \n", "L 818.702776 177.744869 \n", "L 818.834606 177.700976 \n", "L 818.878533 177.739679 \n", "L 818.944625 177.678384 \n", "L 819.12052 177.9214 \n", "L 819.142627 177.909066 \n", "L 819.164523 177.899548 \n", "L 819.186505 177.950897 \n", "L 819.208342 178.04658 \n", "L 819.296345 177.963765 \n", "L 819.494516 178.061167 \n", "L 819.670538 178.355536 \n", "L 819.692473 178.278412 \n", "L 819.75813 178.381379 \n", "L 819.780084 178.376171 \n", "L 820.153223 178.77045 \n", "L 820.175373 178.738672 \n", "L 820.197468 178.66123 \n", "L 820.285795 178.73844 \n", "L 820.484131 178.554762 \n", "L 820.637632 178.753727 \n", "L 820.703416 178.690226 \n", "L 820.791316 178.758329 \n", "L 820.901334 178.762671 \n", "L 820.945118 178.726511 \n", "L 820.989129 178.820438 \n", "L 821.164835 179.231863 \n", "L 821.319063 179.35876 \n", "L 821.494885 179.730307 \n", "L 821.517045 179.698367 \n", "L 822.285767 180.189221 \n", "L 822.439441 180.260135 \n", "L 822.63786 180.417994 \n", "L 822.769721 180.466025 \n", "L 822.791772 180.443405 \n", "L 822.81372 180.509092 \n", "L 822.901718 180.650107 \n", "L 822.967535 180.614944 \n", "L 823.231728 180.410661 \n", "L 823.363713 180.25437 \n", "L 823.649329 179.737771 \n", "L 823.693244 179.786469 \n", "L 823.935105 179.790581 \n", "L 824.154647 179.313085 \n", "L 824.176544 179.341572 \n", "L 824.220702 179.26452 \n", "L 824.264513 179.170371 \n", "L 824.330224 179.264145 \n", "L 824.418079 179.459429 \n", "L 824.505869 179.414199 \n", "L 824.57149 179.232055 \n", "L 824.637483 179.329153 \n", "L 824.659487 179.347135 \n", "L 824.681413 179.242016 \n", "L 824.74743 179.221609 \n", "L 824.923283 179.586533 \n", "L 824.967085 179.665433 \n", "L 825.032876 179.601181 \n", "L 825.318327 179.298103 \n", "L 825.362413 179.327089 \n", "L 825.406313 179.256865 \n", "L 825.494007 179.161583 \n", "L 825.53801 179.06655 \n", "L 825.625864 179.130946 \n", "L 825.669829 179.132257 \n", "L 825.757727 179.233912 \n", "L 825.779781 179.168311 \n", "L 825.845709 179.077156 \n", "L 825.955774 178.965235 \n", "L 825.977725 178.999444 \n", "L 826.15304 179.15755 \n", "L 826.219065 179.140983 \n", "L 826.306767 178.967487 \n", "L 826.350833 178.983111 \n", "L 826.372999 179.013391 \n", "L 826.43893 178.927449 \n", "L 826.637324 178.506439 \n", "L 826.747012 178.176276 \n", "L 826.790974 178.234838 \n", "L 826.813059 178.290236 \n", "L 826.879038 178.162985 \n", "L 826.989076 178.029352 \n", "L 827.011075 178.058771 \n", "L 827.187212 177.977535 \n", "L 827.231209 178.037836 \n", "L 827.27496 177.987259 \n", "L 827.406847 177.655278 \n", "L 827.428819 177.661102 \n", "L 827.582891 177.78947 \n", "L 827.604865 177.756963 \n", "L 827.758939 177.643648 \n", "L 827.780935 177.666462 \n", "L 828.287069 178.10699 \n", "L 828.352873 178.018387 \n", "L 828.462977 177.964427 \n", "L 828.57271 177.946522 \n", "L 828.748523 178.286286 \n", "L 828.638715 177.907129 \n", "L 828.770512 178.241802 \n", "L 828.814497 178.129184 \n", "L 828.858539 178.303035 \n", "L 829.122148 178.710372 \n", "L 829.144049 178.679576 \n", "L 829.364197 178.789276 \n", "L 829.452066 178.711044 \n", "L 829.495963 178.734963 \n", "L 829.649704 178.970149 \n", "L 829.671616 178.86545 \n", "L 829.737441 179.005477 \n", "L 829.979134 178.765727 \n", "L 830.220793 178.190583 \n", "L 830.24287 178.219952 \n", "L 830.286762 178.107596 \n", "L 830.528911 177.621331 \n", "L 830.68268 177.807643 \n", "L 830.968442 177.748618 \n", "L 831.034355 177.691302 \n", "L 831.05636 177.741309 \n", "L 831.144254 177.800782 \n", "L 831.188175 177.78351 \n", "L 831.210096 177.732937 \n", "L 831.298039 177.801339 \n", "L 831.473763 178.005377 \n", "L 831.517629 177.934466 \n", "L 831.671895 177.720277 \n", "L 831.693849 177.781848 \n", "L 832.156238 178.352988 \n", "L 832.200071 178.315373 \n", "L 832.244109 178.395667 \n", "L 832.331686 178.255504 \n", "L 832.397576 178.31173 \n", "L 832.507388 178.169624 \n", "L 832.639207 178.2658 \n", "L 832.903122 178.509102 \n", "L 833.122771 178.8777 \n", "L 833.166572 178.930817 \n", "L 833.275871 179.156164 \n", "L 833.275871 179.156164 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #c44e52; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"line2d_66\">\n", "    <path d=\"M 633.088598 177.810932 \n", "L 633.19875 177.759756 \n", "L 633.264922 178.122587 \n", "L 633.309143 178.048961 \n", "L 633.684975 176.812878 \n", "L 633.816663 176.168371 \n", "L 634.411321 170.69662 \n", "L 634.565132 168.971274 \n", "L 635.248151 164.420375 \n", "L 635.402533 163.134122 \n", "L 635.777251 161.182069 \n", "L 636.064041 158.710072 \n", "L 637.824293 144.788142 \n", "L 638.110908 140.645099 \n", "L 638.26465 138.41822 \n", "L 638.573927 132.62124 \n", "L 638.816174 128.719842 \n", "L 639.013607 126.245184 \n", "L 639.079818 125.498638 \n", "L 639.256098 123.640314 \n", "L 639.497833 121.501487 \n", "L 639.608126 120.739041 \n", "L 639.630279 120.799015 \n", "L 639.916748 120.176303 \n", "L 639.938718 120.22389 \n", "L 639.960688 120.427624 \n", "L 640.004936 120.12108 \n", "L 640.290448 116.22835 \n", "L 640.510753 111.930501 \n", "L 640.708921 108.883815 \n", "L 641.127465 100.967828 \n", "L 641.39145 96.705179 \n", "L 643.877043 55.752311 \n", "L 644.207178 53.052231 \n", "L 644.31712 52.496118 \n", "L 644.361038 52.634353 \n", "L 644.757002 50.821681 \n", "L 645.064893 48.318071 \n", "L 645.372954 46.613134 \n", "L 645.460904 46.723549 \n", "L 645.548897 46.693408 \n", "L 645.614918 46.514155 \n", "L 645.636976 46.710268 \n", "L 645.658964 46.7245 \n", "L 645.943701 49.363143 \n", "L 646.141485 51.173394 \n", "L 646.163551 51.149075 \n", "L 646.383479 52.628433 \n", "L 646.801206 56.630034 \n", "L 646.823177 56.533056 \n", "L 646.84515 56.731319 \n", "L 647.175242 60.433146 \n", "L 647.835083 65.390895 \n", "L 648.143397 68.052368 \n", "L 648.539631 71.195414 \n", "L 648.892104 73.76625 \n", "L 648.913983 73.743757 \n", "L 649.727805 82.641521 \n", "L 649.793577 83.247537 \n", "L 650.079481 85.790053 \n", "L 650.277697 87.578074 \n", "L 650.453682 89.11514 \n", "L 650.497548 89.300262 \n", "L 650.761261 91.580191 \n", "L 651.113111 94.192765 \n", "L 651.134991 94.159537 \n", "L 651.179144 94.27974 \n", "L 651.925855 98.163784 \n", "L 651.947949 98.115316 \n", "L 651.970007 98.204838 \n", "L 652.123857 98.725868 \n", "L 652.255761 99.000575 \n", "L 652.2777 98.882138 \n", "L 652.47594 98.439393 \n", "L 652.49789 98.482553 \n", "L 652.695574 98.885591 \n", "L 652.739518 98.798901 \n", "L 652.76154 98.788805 \n", "L 653.026146 100.115322 \n", "L 653.070147 100.092447 \n", "L 653.09232 100.20267 \n", "L 653.180204 100.487349 \n", "L 653.39997 101.345472 \n", "L 653.509945 101.499145 \n", "L 653.686196 101.122944 \n", "L 653.840086 101.440555 \n", "L 653.862087 101.452313 \n", "L 653.972075 101.25418 \n", "L 653.993963 101.338862 \n", "L 654.06015 101.263954 \n", "L 654.191969 101.065821 \n", "L 654.235945 101.147477 \n", "L 654.280087 101.123119 \n", "L 654.301941 101.200468 \n", "L 654.543984 102.14626 \n", "L 654.609766 102.297379 \n", "L 654.675858 102.150176 \n", "L 654.763984 102.139118 \n", "L 654.852062 102.080874 \n", "L 654.874116 102.222252 \n", "L 654.918281 102.149179 \n", "L 654.984162 102.238245 \n", "L 655.247688 103.037178 \n", "L 655.269645 102.985335 \n", "L 655.313803 102.873755 \n", "L 655.357724 103.045182 \n", "L 655.379588 103.032193 \n", "L 655.511783 103.192784 \n", "L 655.841797 102.331377 \n", "L 655.863988 102.368351 \n", "L 655.908014 102.256147 \n", "L 655.929971 102.239787 \n", "L 655.951969 102.324726 \n", "L 655.973872 102.428727 \n", "L 656.039719 102.238151 \n", "L 656.105413 102.33746 \n", "L 656.149564 102.163002 \n", "L 656.215563 102.322627 \n", "L 656.259354 102.19155 \n", "L 656.347166 102.278175 \n", "L 656.764646 103.795827 \n", "L 656.808644 103.912268 \n", "L 656.918528 104.100252 \n", "L 656.94042 104.092465 \n", "L 657.183074 104.52978 \n", "L 657.226777 104.667032 \n", "L 657.270825 104.467929 \n", "L 657.424734 104.503444 \n", "L 657.68843 105.16249 \n", "L 657.754472 105.033725 \n", "L 657.732357 105.185609 \n", "L 657.798463 105.12177 \n", "L 657.820382 105.180201 \n", "L 657.864317 105.04891 \n", "L 657.886273 105.082558 \n", "L 657.908325 104.957146 \n", "L 657.952288 105.180456 \n", "L 658.084 105.281851 \n", "L 658.194213 105.126971 \n", "L 658.348291 105.6021 \n", "L 658.435955 105.207262 \n", "L 658.54593 105.018999 \n", "L 658.567961 105.060626 \n", "L 658.58987 105.110145 \n", "L 658.633687 104.948627 \n", "L 658.721469 104.544007 \n", "L 658.787187 104.62214 \n", "L 658.897049 104.412153 \n", "L 658.919039 104.439185 \n", "L 659.293022 105.805253 \n", "L 659.403066 106.37377 \n", "L 659.57894 106.980023 \n", "L 659.622677 107.083062 \n", "L 659.798221 107.513424 \n", "L 659.820377 107.492169 \n", "L 659.886181 107.651093 \n", "L 659.908351 107.45144 \n", "L 660.282519 105.344339 \n", "L 660.304422 105.358571 \n", "L 660.326274 105.406798 \n", "L 660.348123 105.368067 \n", "L 660.458636 105.16172 \n", "L 660.480475 105.17382 \n", "L 660.502593 105.184784 \n", "L 660.524488 105.112474 \n", "L 660.656537 104.558589 \n", "L 660.678493 104.589271 \n", "L 660.832345 104.159146 \n", "L 660.986278 103.425721 \n", "L 661.074059 103.461267 \n", "L 661.096185 103.362319 \n", "L 661.140054 103.457703 \n", "L 661.293864 103.912955 \n", "L 661.315928 103.859897 \n", "L 661.425825 103.968817 \n", "L 661.470109 103.876707 \n", "L 661.557843 103.933743 \n", "L 661.623814 103.766395 \n", "L 661.690181 103.738 \n", "L 661.756073 103.92562 \n", "L 662.129487 103.590094 \n", "L 662.151525 103.615741 \n", "L 662.21745 103.661358 \n", "L 662.261575 103.606282 \n", "L 662.833462 101.736498 \n", "L 662.855533 101.883048 \n", "L 663.471648 100.42839 \n", "L 663.537746 100.416135 \n", "L 663.647433 100.174917 \n", "L 663.66936 100.188832 \n", "L 663.69133 100.255943 \n", "L 663.713193 100.018381 \n", "L 663.822813 99.915032 \n", "L 664.240386 100.95048 \n", "L 664.372413 100.800166 \n", "L 664.306667 101.001914 \n", "L 664.394286 100.87729 \n", "L 664.504485 100.793063 \n", "L 664.526549 100.748847 \n", "L 664.570553 100.881343 \n", "L 664.592632 100.936111 \n", "L 664.636507 100.753091 \n", "L 665.208715 98.394193 \n", "L 665.252713 98.464087 \n", "L 665.3407 98.513069 \n", "L 665.362696 98.478958 \n", "L 665.384812 98.401767 \n", "L 665.428662 98.581144 \n", "L 665.649184 99.202698 \n", "L 665.803032 99.608029 \n", "L 665.825085 99.571546 \n", "L 665.847074 99.668824 \n", "L 665.869087 99.875592 \n", "L 665.934849 99.444728 \n", "L 665.956864 99.512818 \n", "L 666.000947 99.288645 \n", "L 666.022855 99.200864 \n", "L 666.044936 99.436772 \n", "L 666.066985 99.516278 \n", "L 666.111057 99.336159 \n", "L 666.177038 99.167032 \n", "L 666.220839 99.353613 \n", "L 666.352613 99.547795 \n", "L 666.484711 100.17484 \n", "L 666.52872 100.155181 \n", "L 666.572705 100.269041 \n", "L 666.705218 100.526796 \n", "L 666.815107 100.885517 \n", "L 667.409016 102.773337 \n", "L 667.430937 102.697918 \n", "L 667.474892 102.692864 \n", "L 667.496804 102.758643 \n", "L 668.332882 104.645657 \n", "L 668.35473 104.57288 \n", "L 668.398747 104.761496 \n", "L 668.508789 104.870226 \n", "L 668.530753 104.772181 \n", "L 668.61885 104.880475 \n", "L 668.948838 106.349218 \n", "L 669.103196 106.892212 \n", "L 669.125126 106.879774 \n", "L 669.389453 107.266498 \n", "L 669.455558 107.322178 \n", "L 669.54346 106.979081 \n", "L 670.027662 107.589294 \n", "L 670.203541 107.058556 \n", "L 670.225417 107.123791 \n", "L 670.247314 107.139636 \n", "L 670.269205 107.025226 \n", "L 670.291258 106.930089 \n", "L 670.335345 107.068758 \n", "L 670.379537 106.952028 \n", "L 670.53369 107.542179 \n", "L 670.555627 107.479045 \n", "L 670.863781 107.871105 \n", "L 670.885787 107.837359 \n", "L 670.907784 107.815854 \n", "L 670.929841 107.852688 \n", "L 671.127571 108.287216 \n", "L 671.1717 108.236781 \n", "L 671.237478 108.281543 \n", "L 671.281363 108.455129 \n", "L 671.325658 108.301281 \n", "L 671.369593 107.988164 \n", "L 671.457684 108.196385 \n", "L 671.590018 108.607523 \n", "L 671.787999 109.677783 \n", "L 671.810007 109.599261 \n", "L 671.854047 109.783471 \n", "L 671.875958 109.759 \n", "L 672.052449 109.938085 \n", "L 672.096199 109.755622 \n", "L 672.184439 109.817129 \n", "L 672.250394 109.50181 \n", "L 672.316414 109.664016 \n", "L 672.382509 109.773417 \n", "L 672.426613 109.657958 \n", "L 672.536805 109.499778 \n", "L 672.580797 109.568987 \n", "L 672.822572 109.899426 \n", "L 672.954787 110.267838 \n", "L 672.976835 110.250478 \n", "L 673.020876 110.20393 \n", "L 673.064686 110.30358 \n", "L 673.196597 110.598323 \n", "L 673.262904 110.55503 \n", "L 673.284746 110.66055 \n", "L 673.306757 110.611717 \n", "L 673.438319 110.615035 \n", "L 673.482394 110.816939 \n", "L 673.548364 110.793844 \n", "L 673.658578 110.392581 \n", "L 673.724642 110.428563 \n", "L 673.83476 110.492545 \n", "L 673.966495 110.590167 \n", "L 674.01043 110.665528 \n", "L 674.186388 111.21437 \n", "L 674.406421 111.097332 \n", "L 674.428506 110.945045 \n", "L 674.516709 110.994995 \n", "L 674.538657 110.961773 \n", "L 674.582466 111.059823 \n", "L 674.604531 111.056536 \n", "L 674.758424 111.684146 \n", "L 674.890611 111.844314 \n", "L 675.044688 112.239966 \n", "L 675.066876 112.20843 \n", "L 675.133066 112.109498 \n", "L 675.155014 112.179439 \n", "L 675.177023 112.24264 \n", "L 675.22095 112.104697 \n", "L 675.396905 111.664798 \n", "L 675.572905 111.359094 \n", "L 675.881031 112.613381 \n", "L 675.902942 112.515945 \n", "L 675.947018 112.714908 \n", "L 676.145433 113.195622 \n", "L 676.299378 113.210371 \n", "L 676.409359 112.779285 \n", "L 676.475026 112.814637 \n", "L 676.497061 112.853309 \n", "L 676.541134 112.791327 \n", "L 676.849304 112.092945 \n", "L 676.871184 112.163176 \n", "L 676.915492 112.04246 \n", "L 677.157345 111.067507 \n", "L 677.179311 111.083371 \n", "L 677.201438 111.005394 \n", "L 677.333645 110.658073 \n", "L 677.355669 110.727611 \n", "L 677.488124 110.702639 \n", "L 678.214117 108.952287 \n", "L 678.236004 108.980592 \n", "L 678.280075 108.858693 \n", "L 678.346277 108.442346 \n", "L 678.478471 107.905467 \n", "L 678.544297 107.931027 \n", "L 678.873882 107.294877 \n", "L 678.939885 107.290938 \n", "L 678.983977 107.175838 \n", "L 679.028132 107.399831 \n", "L 679.160025 107.858635 \n", "L 679.181896 107.720958 \n", "L 679.424223 106.955881 \n", "L 679.930677 108.473742 \n", "L 680.018722 108.277443 \n", "L 680.062641 108.308351 \n", "L 680.084561 108.285268 \n", "L 680.128623 108.379787 \n", "L 680.216956 108.322667 \n", "L 680.283128 108.012175 \n", "L 680.349249 108.182245 \n", "L 680.459169 108.005136 \n", "L 680.481192 108.063734 \n", "L 680.678925 108.260914 \n", "L 681.008469 109.368335 \n", "L 681.030554 109.334131 \n", "L 681.052606 109.43153 \n", "L 681.228625 109.65193 \n", "L 681.294821 109.644891 \n", "L 681.382889 109.469863 \n", "L 681.427012 109.507231 \n", "L 681.647109 109.216119 \n", "L 681.801261 109.014756 \n", "L 681.845024 109.121311 \n", "L 681.888765 109.331284 \n", "L 681.976833 109.305905 \n", "L 682.020937 109.285459 \n", "L 682.065012 109.34804 \n", "L 682.240814 109.841322 \n", "L 682.504684 111.066296 \n", "L 682.724416 112.210426 \n", "L 682.834733 112.459126 \n", "L 682.878628 112.43403 \n", "L 682.900567 112.414765 \n", "L 682.922447 112.485314 \n", "L 683.075946 113.166958 \n", "L 683.273717 114.187514 \n", "L 683.691758 116.450981 \n", "L 683.757735 116.5065 \n", "L 683.955537 117.428151 \n", "L 683.977478 117.388426 \n", "L 684.021794 117.164981 \n", "L 684.087855 117.256682 \n", "L 684.219651 117.547977 \n", "L 684.241732 117.532498 \n", "L 684.285694 117.649809 \n", "L 684.417536 118.059995 \n", "L 684.46152 118.030181 \n", "L 684.483636 118.069387 \n", "L 684.505735 118.041933 \n", "L 684.571925 117.763185 \n", "L 684.616021 117.929086 \n", "L 684.968174 118.954853 \n", "L 684.990295 118.934026 \n", "L 685.144277 118.541645 \n", "L 685.188375 118.569091 \n", "L 685.21031 118.611525 \n", "L 685.232281 118.556303 \n", "L 685.341966 118.285882 \n", "L 685.363895 118.293218 \n", "L 685.539884 118.858312 \n", "L 685.561998 118.827897 \n", "L 685.650146 118.782358 \n", "L 685.694317 118.85862 \n", "L 685.760453 119.08612 \n", "L 685.848713 118.940755 \n", "L 685.870821 118.961189 \n", "L 685.892786 118.867388 \n", "L 685.914742 118.842812 \n", "L 685.936876 118.915454 \n", "L 685.958729 118.961851 \n", "L 685.980763 118.780623 \n", "L 686.223037 118.525199 \n", "L 686.266826 118.404722 \n", "L 686.311044 118.457326 \n", "L 686.354827 118.629323 \n", "L 686.420855 118.567619 \n", "L 686.442822 118.486332 \n", "L 686.508833 118.570222 \n", "L 686.684919 118.962521 \n", "L 686.816858 119.064965 \n", "L 686.926553 119.630732 \n", "L 687.014135 119.515343 \n", "L 687.058 119.456431 \n", "L 687.299594 119.847831 \n", "L 687.321651 119.790064 \n", "L 687.387428 119.919433 \n", "L 687.607383 120.540707 \n", "L 687.629298 120.496918 \n", "L 687.67311 120.633253 \n", "L 687.695178 120.595048 \n", "L 687.761307 120.669794 \n", "L 687.805417 120.618478 \n", "L 687.82726 120.549646 \n", "L 687.87122 120.686346 \n", "L 687.95921 120.995696 \n", "L 688.04686 120.917465 \n", "L 688.178917 120.743581 \n", "L 688.200903 120.770048 \n", "L 688.245131 120.810309 \n", "L 688.288841 120.723043 \n", "L 688.310932 120.760763 \n", "L 688.530985 119.836178 \n", "L 688.618901 119.408781 \n", "L 688.684838 119.472959 \n", "L 688.927483 118.896332 \n", "L 689.015421 118.77861 \n", "L 689.169725 118.486045 \n", "L 689.213511 118.487096 \n", "L 689.235668 118.534745 \n", "L 689.257765 118.574652 \n", "L 689.302008 118.44562 \n", "L 689.433948 118.583844 \n", "L 689.653936 119.237509 \n", "L 689.675967 119.160442 \n", "L 689.720011 119.160915 \n", "L 689.742001 119.207897 \n", "L 689.895798 119.369874 \n", "L 689.939888 119.4203 \n", "L 690.072215 119.759177 \n", "L 690.094258 119.731208 \n", "L 690.159793 119.578151 \n", "L 690.203541 119.619325 \n", "L 690.423403 120.008379 \n", "L 690.599701 120.154193 \n", "L 690.665611 119.933652 \n", "L 690.73159 120.002436 \n", "L 690.753482 120.097429 \n", "L 690.797745 119.914336 \n", "L 690.819737 119.878656 \n", "L 690.863753 119.974068 \n", "L 691.633203 120.813136 \n", "L 691.875508 121.344089 \n", "L 691.897527 121.317971 \n", "L 691.919629 121.378762 \n", "L 692.00744 121.4953 \n", "L 692.029351 121.440135 \n", "L 692.183337 121.274834 \n", "L 692.205282 121.342774 \n", "L 692.33757 121.444809 \n", "L 692.381615 121.284447 \n", "L 692.447668 121.303744 \n", "L 692.645338 122.069307 \n", "L 692.667373 122.047112 \n", "L 693.195488 123.380662 \n", "L 693.217327 123.349359 \n", "L 693.261229 123.254172 \n", "L 693.283142 123.346856 \n", "L 693.503494 124.129202 \n", "L 693.657452 124.953187 \n", "L 693.987527 127.269659 \n", "L 694.273368 128.689704 \n", "L 694.317477 128.574125 \n", "L 694.383712 128.354352 \n", "L 694.449726 128.368062 \n", "L 694.493578 128.481706 \n", "L 694.559761 128.426404 \n", "L 694.603804 128.285949 \n", "L 694.669985 128.405756 \n", "L 694.911866 128.779447 \n", "L 694.955875 128.703998 \n", "L 694.999625 128.581504 \n", "L 695.043383 128.647989 \n", "L 695.087469 128.761634 \n", "L 695.131763 128.711209 \n", "L 695.26395 128.55679 \n", "L 695.35199 128.46435 \n", "L 695.37407 128.519032 \n", "L 695.440404 128.710558 \n", "L 695.484436 128.592569 \n", "L 695.506488 128.564292 \n", "L 695.528417 128.664194 \n", "L 695.836243 129.698568 \n", "L 696.408478 131.971531 \n", "L 696.430471 131.884705 \n", "L 696.474341 132.049732 \n", "L 696.496401 132.03245 \n", "L 696.540591 132.062802 \n", "L 696.584594 132.000932 \n", "L 696.65033 132.028175 \n", "L 697.398092 134.636803 \n", "L 697.463993 134.843992 \n", "L 697.881891 136.441786 \n", "L 697.926034 136.538381 \n", "L 698.255949 137.568969 \n", "L 698.431817 137.168951 \n", "L 698.453797 137.136478 \n", "L 698.541761 137.201206 \n", "L 698.78419 137.905098 \n", "L 698.806162 137.769607 \n", "L 698.871976 137.585922 \n", "L 698.915855 137.671955 \n", "L 699.047777 138.107847 \n", "L 699.091877 138.092662 \n", "L 699.223847 137.960041 \n", "L 699.333872 138.017546 \n", "L 699.355788 137.983212 \n", "L 699.443706 137.873344 \n", "L 699.399973 138.024086 \n", "L 699.50977 137.928954 \n", "L 699.774171 137.732781 \n", "L 699.796015 137.894997 \n", "L 700.126471 138.920653 \n", "L 700.148617 138.905347 \n", "L 700.170457 138.948306 \n", "L 700.500844 140.031313 \n", "L 700.522815 140.015597 \n", "L 700.544744 140.095584 \n", "L 700.566886 140.110843 \n", "L 700.633035 140.351893 \n", "L 700.875059 141.404341 \n", "L 701.006817 141.299342 \n", "L 701.050831 141.240125 \n", "L 701.09484 141.288518 \n", "L 701.315048 141.670668 \n", "L 701.336977 141.618358 \n", "L 701.380812 141.72809 \n", "L 701.55674 141.897524 \n", "L 701.755184 142.442434 \n", "L 701.953242 143.280785 \n", "L 701.975412 143.257204 \n", "L 702.129523 143.505773 \n", "L 702.195882 143.589267 \n", "L 702.261802 143.549377 \n", "L 702.525522 143.02664 \n", "L 702.569426 142.92126 \n", "L 702.613299 143.107737 \n", "L 702.723403 143.190349 \n", "L 702.8335 142.981036 \n", "L 702.85534 143.014744 \n", "L 702.943348 143.105996 \n", "L 702.98712 143.058525 \n", "L 703.185553 142.527596 \n", "L 703.207498 142.534564 \n", "L 703.273197 142.402311 \n", "L 703.492748 142.146229 \n", "L 703.514809 142.210461 \n", "L 703.536768 141.980765 \n", "L 703.71266 141.438104 \n", "L 703.734542 141.410658 \n", "L 703.778465 141.506548 \n", "L 704.020002 142.325146 \n", "L 704.108158 142.108699 \n", "L 704.372007 141.289679 \n", "L 704.547955 140.891431 \n", "L 704.680106 141.007394 \n", "L 704.724069 141.117862 \n", "L 704.79022 141.02832 \n", "L 704.834145 140.987353 \n", "L 704.856168 140.903292 \n", "L 704.92196 141.003771 \n", "L 705.032229 141.049079 \n", "L 705.054386 141.028248 \n", "L 705.120496 141.021151 \n", "L 705.142529 140.883616 \n", "L 705.208454 141.085596 \n", "L 705.428322 141.749021 \n", "L 705.450374 141.727854 \n", "L 705.714447 142.122121 \n", "L 705.736499 142.074122 \n", "L 705.846555 141.688064 \n", "L 705.890309 141.712006 \n", "L 705.978364 141.823004 \n", "L 706.110471 142.198081 \n", "L 706.132353 142.052712 \n", "L 706.242485 141.865614 \n", "L 706.286606 141.893119 \n", "L 706.308614 141.833536 \n", "L 706.352698 141.958237 \n", "L 706.374847 141.959347 \n", "L 706.462931 142.225465 \n", "L 706.594998 142.776867 \n", "L 706.661081 142.758205 \n", "L 706.749336 142.835147 \n", "L 706.793329 142.798341 \n", "L 706.991375 142.766235 \n", "L 707.211355 142.209289 \n", "L 707.321683 141.935029 \n", "L 707.365752 141.956172 \n", "L 707.498072 141.88692 \n", "L 707.519992 141.922951 \n", "L 707.827971 142.46174 \n", "L 708.004163 142.811272 \n", "L 708.092348 142.729506 \n", "L 708.136348 142.527691 \n", "L 708.224529 142.592647 \n", "L 708.312417 143.008943 \n", "L 708.400714 142.898327 \n", "L 708.511188 142.889308 \n", "L 708.708976 143.504254 \n", "L 708.77505 143.5824 \n", "L 709.17133 144.528487 \n", "L 709.215378 144.472712 \n", "L 709.281238 144.452432 \n", "L 709.303255 144.479714 \n", "L 709.566651 145.088898 \n", "L 709.764272 146.416118 \n", "L 709.983905 147.18932 \n", "L 710.072321 147.007448 \n", "L 710.116053 147.054567 \n", "L 710.226288 147.189601 \n", "L 710.490332 147.739573 \n", "L 710.55679 147.923259 \n", "L 710.754978 148.567305 \n", "L 710.777127 148.560468 \n", "L 711.106705 149.127633 \n", "L 711.128552 149.081865 \n", "L 711.194565 149.096122 \n", "L 711.216489 149.118267 \n", "L 711.326735 149.474204 \n", "L 711.50297 149.985644 \n", "L 711.657139 150.50187 \n", "L 711.723432 150.482238 \n", "L 711.8331 150.591418 \n", "L 712.030768 150.78753 \n", "L 712.074861 150.708147 \n", "L 712.118893 150.771458 \n", "L 712.338674 151.201958 \n", "L 712.382466 151.156257 \n", "L 712.42658 151.210415 \n", "L 712.558764 151.607852 \n", "L 712.602787 151.552759 \n", "L 712.646614 151.65428 \n", "L 712.756386 151.680127 \n", "L 712.778467 151.641677 \n", "L 712.82246 151.760743 \n", "L 712.866301 151.733928 \n", "L 712.88814 151.675962 \n", "L 712.910234 151.630467 \n", "L 712.976308 151.726581 \n", "L 713.240161 152.313391 \n", "L 713.394437 152.213831 \n", "L 713.416607 152.315861 \n", "L 713.702527 152.922783 \n", "L 713.746459 153.073107 \n", "L 713.812352 152.944477 \n", "L 714.075879 153.583781 \n", "L 714.097939 153.517285 \n", "L 714.207927 153.42706 \n", "L 714.229876 153.474465 \n", "L 714.295697 153.524941 \n", "L 714.317662 153.487312 \n", "L 714.559264 152.858053 \n", "L 714.581214 152.871452 \n", "L 714.6474 153.026592 \n", "L 715.042798 154.036032 \n", "L 715.197173 154.156005 \n", "L 715.395095 154.233394 \n", "L 715.548733 154.698994 \n", "L 715.680698 154.900174 \n", "L 715.746869 154.784079 \n", "L 715.790851 154.901595 \n", "L 715.922609 155.145707 \n", "L 715.944639 155.118399 \n", "L 716.076898 155.045824 \n", "L 716.120935 155.000958 \n", "L 716.165037 155.114507 \n", "L 716.209071 155.127771 \n", "L 716.231085 155.196377 \n", "L 716.29687 155.242761 \n", "L 716.318843 155.162079 \n", "L 716.406861 155.137883 \n", "L 716.428882 155.164202 \n", "L 716.604918 155.847022 \n", "L 716.737149 156.480117 \n", "L 716.781268 156.371559 \n", "L 716.803131 156.390768 \n", "L 716.825316 156.361767 \n", "L 717.023237 155.945604 \n", "L 717.067279 155.92374 \n", "L 717.154741 155.605243 \n", "L 717.199028 155.716204 \n", "L 717.440858 156.362924 \n", "L 717.705775 155.787591 \n", "L 717.771581 155.722852 \n", "L 718.080077 154.384112 \n", "L 718.300907 154.763978 \n", "L 718.45493 154.643427 \n", "L 718.631022 154.726838 \n", "L 718.69705 154.830291 \n", "L 718.741172 154.802968 \n", "L 718.785187 154.77366 \n", "L 718.807355 154.799318 \n", "L 718.89585 154.912451 \n", "L 718.917873 154.899337 \n", "L 719.247982 154.436036 \n", "L 719.270103 154.463569 \n", "L 719.292226 154.392533 \n", "L 719.424302 153.988649 \n", "L 719.512627 154.12682 \n", "L 719.688823 154.551729 \n", "L 719.754869 154.411918 \n", "L 719.798789 154.506291 \n", "L 719.84285 154.669551 \n", "L 720.063524 155.596288 \n", "L 720.129544 155.641254 \n", "L 720.173693 155.592152 \n", "L 720.305715 155.489293 \n", "L 720.415942 155.442699 \n", "L 720.613965 154.95886 \n", "L 720.657859 155.071481 \n", "L 720.723888 155.044855 \n", "L 720.767763 155.001607 \n", "L 720.811889 155.096705 \n", "L 720.965874 155.305195 \n", "L 721.185974 155.054192 \n", "L 721.230134 155.145601 \n", "L 721.274305 155.037633 \n", "L 721.450406 154.987027 \n", "L 721.626603 155.429725 \n", "L 721.759218 155.424991 \n", "L 721.935671 155.727357 \n", "L 721.957708 155.782164 \n", "L 722.001845 155.6889 \n", "L 722.375961 154.94 \n", "L 722.727933 155.420076 \n", "L 722.793943 155.333786 \n", "L 723.013921 154.926468 \n", "L 723.080076 155.059055 \n", "L 723.233934 155.472161 \n", "L 723.299955 155.574609 \n", "L 723.739947 156.749184 \n", "L 723.806063 156.685972 \n", "L 724.136651 157.006774 \n", "L 724.158516 156.982803 \n", "L 724.268418 156.839997 \n", "L 724.290396 156.898994 \n", "L 724.444549 157.223132 \n", "L 724.554943 157.691944 \n", "L 724.731214 158.059188 \n", "L 725.061686 158.679609 \n", "L 725.215826 159.198311 \n", "L 725.435927 159.793257 \n", "L 725.457779 159.747483 \n", "L 725.5018 159.812533 \n", "L 725.567828 159.773948 \n", "L 725.700257 159.790704 \n", "L 725.899112 159.428428 \n", "L 725.921237 159.468557 \n", "L 726.053795 159.797806 \n", "L 726.0978 159.643975 \n", "L 726.34007 159.519195 \n", "L 726.383869 159.573754 \n", "L 726.450053 159.500643 \n", "L 726.472069 159.504763 \n", "L 726.516136 159.661028 \n", "L 726.604369 159.641374 \n", "L 726.73701 159.454891 \n", "L 726.758919 159.522253 \n", "L 726.979791 159.9068 \n", "L 727.001736 159.895654 \n", "L 727.156445 159.803283 \n", "L 727.178484 159.835927 \n", "L 727.398544 159.836623 \n", "L 727.464796 159.723591 \n", "L 727.508858 159.790447 \n", "L 727.706967 160.120038 \n", "L 727.83915 160.686874 \n", "L 727.993656 161.255462 \n", "L 728.037673 161.203046 \n", "L 728.059598 161.243587 \n", "L 728.103738 161.164935 \n", "L 728.125734 161.205785 \n", "L 728.191915 161.15648 \n", "L 728.235748 161.193778 \n", "L 728.324024 161.142531 \n", "L 728.367891 160.995463 \n", "L 728.411988 160.87325 \n", "L 728.456221 160.924713 \n", "L 728.544353 161.117289 \n", "L 728.588059 161.062579 \n", "L 728.653765 160.956789 \n", "L 728.697736 160.918808 \n", "L 728.741931 161.002874 \n", "L 728.984228 161.496873 \n", "L 729.160481 161.77926 \n", "L 729.358757 162.176641 \n", "L 729.468788 162.267181 \n", "L 729.490724 162.231123 \n", "L 729.600893 162.026664 \n", "L 729.667166 162.099535 \n", "L 729.84379 162.586087 \n", "L 730.020004 162.635929 \n", "L 730.042065 162.572093 \n", "L 730.196317 162.412008 \n", "L 730.372483 162.259034 \n", "L 730.394527 162.306492 \n", "L 730.504768 162.453894 \n", "L 730.54869 162.42158 \n", "L 730.791357 161.242254 \n", "L 730.835475 161.312662 \n", "L 731.011935 161.058207 \n", "L 731.122038 160.896975 \n", "L 731.144211 160.90742 \n", "L 731.276332 161.075031 \n", "L 731.717482 162.34904 \n", "L 731.73965 162.32054 \n", "L 731.761736 162.245569 \n", "L 731.827909 162.41185 \n", "L 731.915654 162.487926 \n", "L 732.245874 162.831571 \n", "L 732.268049 162.782621 \n", "L 732.334344 162.814295 \n", "L 732.444487 163.055115 \n", "L 732.532673 163.008422 \n", "L 732.797311 162.6668 \n", "L 732.907739 162.718735 \n", "L 732.863528 162.628905 \n", "L 732.929618 162.704527 \n", "L 733.149987 162.813206 \n", "L 733.41403 162.493306 \n", "L 733.435986 162.507076 \n", "L 734.184275 163.91717 \n", "L 734.712686 164.193124 \n", "L 734.866878 163.737154 \n", "L 734.888925 163.764679 \n", "L 734.976884 163.908955 \n", "L 734.998803 163.855958 \n", "L 735.197028 163.781055 \n", "L 735.307196 163.674013 \n", "L 735.329131 163.686327 \n", "L 735.46135 163.601907 \n", "L 735.527218 163.552112 \n", "L 735.571182 163.581059 \n", "L 735.857888 164.038582 \n", "L 735.967968 163.86797 \n", "L 736.056366 163.792388 \n", "L 736.078369 163.849894 \n", "L 736.364948 164.746948 \n", "L 736.386803 164.728708 \n", "L 736.431062 164.784171 \n", "L 736.717191 165.539982 \n", "L 736.761257 165.525214 \n", "L 736.783371 165.530324 \n", "L 736.805475 165.42681 \n", "L 737.487969 163.446584 \n", "L 737.818477 163.525989 \n", "L 737.972465 163.327585 \n", "L 738.082767 163.179734 \n", "L 738.127006 163.270908 \n", "L 738.149178 163.317749 \n", "L 738.193423 163.200626 \n", "L 738.413838 163.082677 \n", "L 738.590369 163.338856 \n", "L 738.722643 163.275109 \n", "L 738.832759 163.442107 \n", "L 738.854783 163.431281 \n", "L 739.097148 162.982414 \n", "L 739.558189 163.39669 \n", "L 739.712175 163.617392 \n", "L 739.82229 163.781125 \n", "L 739.976527 163.998382 \n", "L 739.99848 163.967002 \n", "L 740.086221 163.810601 \n", "L 740.173966 163.676948 \n", "L 740.21802 163.706035 \n", "L 740.350246 163.889066 \n", "L 740.394453 163.868655 \n", "L 740.460622 163.748827 \n", "L 740.504314 163.837471 \n", "L 740.548527 163.821476 \n", "L 740.570565 163.770773 \n", "L 740.680573 163.721183 \n", "L 740.812315 163.81036 \n", "L 740.922365 163.677358 \n", "L 740.94444 163.729933 \n", "L 741.098505 164.062216 \n", "L 741.120418 164.011229 \n", "L 741.406513 163.562769 \n", "L 741.450632 163.665697 \n", "L 741.758532 163.953307 \n", "L 741.912926 163.767649 \n", "L 742.1111 163.967785 \n", "L 742.176742 163.955453 \n", "L 742.198709 164.014799 \n", "L 742.397135 164.500993 \n", "L 742.573235 165.020258 \n", "L 742.749159 165.625563 \n", "L 742.947133 166.224152 \n", "L 743.035358 166.405329 \n", "L 743.145677 166.679784 \n", "L 743.189674 166.598673 \n", "L 743.498068 167.133475 \n", "L 743.916105 167.802378 \n", "L 743.938235 167.740399 \n", "L 744.135981 167.617698 \n", "L 744.158131 167.689129 \n", "L 744.290351 167.700848 \n", "L 744.33448 167.654204 \n", "L 744.378729 167.746276 \n", "L 744.422526 167.820866 \n", "L 744.466544 167.697645 \n", "L 744.995044 167.600777 \n", "L 745.215385 167.843668 \n", "L 745.237254 167.81208 \n", "L 745.479393 167.753058 \n", "L 745.633572 167.992226 \n", "L 745.677697 167.910943 \n", "L 745.765792 167.741828 \n", "L 745.809631 167.799145 \n", "L 745.963812 168.01168 \n", "L 746.007962 167.960301 \n", "L 746.030137 168.058628 \n", "L 746.338156 168.573532 \n", "L 746.40448 168.574842 \n", "L 746.426474 168.632679 \n", "L 746.53701 168.65032 \n", "L 746.603106 168.498029 \n", "L 746.66908 168.581384 \n", "L 746.713211 168.705926 \n", "L 746.933165 169.347197 \n", "L 746.977381 169.402371 \n", "L 747.065976 169.652057 \n", "L 747.154291 169.534388 \n", "L 747.395645 169.613065 \n", "L 747.4175 169.686298 \n", "L 747.483906 169.590005 \n", "L 747.616561 169.508395 \n", "L 747.858812 169.767819 \n", "L 747.880886 169.749059 \n", "L 747.968978 169.662734 \n", "L 747.991004 169.775257 \n", "L 748.233284 170.002535 \n", "L 748.255131 169.958138 \n", "L 748.387072 169.860335 \n", "L 748.475206 169.937283 \n", "L 748.497174 169.913974 \n", "L 748.651559 169.770325 \n", "L 748.717668 169.860167 \n", "L 748.761905 169.790215 \n", "L 749.136128 169.385038 \n", "L 749.179817 169.422493 \n", "L 749.378407 169.809714 \n", "L 749.576511 170.12012 \n", "L 749.862452 170.416089 \n", "L 749.928711 170.441309 \n", "L 750.083362 170.689384 \n", "L 750.105537 170.676115 \n", "L 750.436071 170.265612 \n", "L 750.458198 170.31725 \n", "L 750.546415 170.260082 \n", "L 750.833135 169.628619 \n", "L 750.877132 169.554211 \n", "L 750.965492 169.596095 \n", "L 751.164212 169.531803 \n", "L 751.186111 169.570696 \n", "L 751.4503 169.457464 \n", "L 751.648189 168.944002 \n", "L 751.845751 168.462349 \n", "L 751.999561 168.561081 \n", "L 752.043608 168.628055 \n", "L 752.10957 168.722715 \n", "L 752.153479 168.64482 \n", "L 752.374118 168.175734 \n", "L 752.396267 168.189754 \n", "L 752.572508 167.875036 \n", "L 752.594553 167.911483 \n", "L 752.660495 168.044316 \n", "L 752.726274 167.989306 \n", "L 752.836197 168.065772 \n", "L 752.858295 168.042286 \n", "L 753.034493 167.943992 \n", "L 753.211233 167.743293 \n", "L 753.585626 167.121281 \n", "L 753.849734 166.177622 \n", "L 754.004122 166.01327 \n", "L 754.136504 166.041547 \n", "L 754.335307 165.744906 \n", "L 754.489113 166.131918 \n", "L 754.511266 166.09976 \n", "L 754.665439 165.923775 \n", "L 754.950457 165.731062 \n", "L 755.082382 165.671766 \n", "L 755.501018 164.891008 \n", "L 755.52288 164.905339 \n", "L 755.544945 164.80879 \n", "L 755.699164 164.696244 \n", "L 755.721103 164.645534 \n", "L 755.787151 164.727575 \n", "L 756.338144 165.927409 \n", "L 756.382072 165.810875 \n", "L 756.580111 165.087012 \n", "L 756.624119 165.169326 \n", "L 756.778754 165.475318 \n", "L 756.823008 165.366176 \n", "L 756.88916 165.18128 \n", "L 756.955295 165.205903 \n", "L 757.108988 165.497671 \n", "L 757.351439 166.429058 \n", "L 757.593409 166.197296 \n", "L 757.70349 166.421861 \n", "L 757.813726 166.797152 \n", "L 757.857559 166.750538 \n", "L 757.967749 166.734985 \n", "L 758.077585 166.864992 \n", "L 758.099663 166.830934 \n", "L 758.25392 166.666589 \n", "L 758.407834 166.352306 \n", "L 758.474186 166.077589 \n", "L 758.540165 166.11326 \n", "L 758.650291 166.388137 \n", "L 758.694264 166.282204 \n", "L 758.760316 166.283291 \n", "L 758.782254 166.302492 \n", "L 759.134897 167.128147 \n", "L 759.311366 167.492995 \n", "L 759.333497 167.49337 \n", "L 759.487925 167.850641 \n", "L 759.553835 167.77412 \n", "L 759.598048 167.722925 \n", "L 759.642082 167.757693 \n", "L 759.774269 167.878117 \n", "L 759.862486 168.039775 \n", "L 759.928427 168.091086 \n", "L 759.972345 168.023119 \n", "L 759.99439 167.998857 \n", "L 760.060298 168.080233 \n", "L 760.192424 168.185358 \n", "L 760.412613 167.910694 \n", "L 760.4786 167.810872 \n", "L 760.522554 167.573047 \n", "L 760.588916 167.649488 \n", "L 760.831087 168.057685 \n", "L 760.853176 167.979346 \n", "L 760.897092 168.113328 \n", "L 760.941133 168.232807 \n", "L 761.0069 168.190163 \n", "L 761.051125 168.158619 \n", "L 761.073131 168.214359 \n", "L 761.447379 168.815804 \n", "L 761.557879 168.72672 \n", "L 761.601884 168.761562 \n", "L 761.712053 168.809536 \n", "L 761.910291 169.12128 \n", "L 762.131089 169.878983 \n", "L 762.329648 169.939161 \n", "L 762.748097 170.910215 \n", "L 763.056775 171.022112 \n", "L 763.078806 170.946861 \n", "L 763.586216 169.996578 \n", "L 763.608269 170.024789 \n", "L 764.047751 171.142598 \n", "L 764.312712 170.873302 \n", "L 764.334699 170.88193 \n", "L 764.35675 170.803552 \n", "L 764.378879 170.743754 \n", "L 764.466927 170.781571 \n", "L 764.642796 170.925771 \n", "L 764.664749 170.879142 \n", "L 764.907177 170.792629 \n", "L 765.016659 170.725136 \n", "L 765.03851 170.712564 \n", "L 765.060622 170.806948 \n", "L 765.237197 171.009814 \n", "L 765.875869 171.866991 \n", "L 766.052643 172.392247 \n", "L 766.096679 172.301936 \n", "L 766.251139 172.20786 \n", "L 766.27322 172.26662 \n", "L 766.361305 172.338887 \n", "L 766.405434 172.305817 \n", "L 766.493699 172.183483 \n", "L 766.515566 172.231582 \n", "L 766.647651 172.464594 \n", "L 766.713639 172.427177 \n", "L 767.264492 172.058627 \n", "L 767.330624 171.96193 \n", "L 767.374593 172.030371 \n", "L 767.59518 172.342125 \n", "L 767.991525 172.129835 \n", "L 768.167627 171.795793 \n", "L 768.388205 171.748023 \n", "L 768.432224 171.657006 \n", "L 768.49818 171.68104 \n", "L 768.674337 172.105475 \n", "L 769.115225 173.093857 \n", "L 769.137262 173.083781 \n", "L 769.159414 173.058289 \n", "L 769.225531 173.145445 \n", "L 769.269673 173.228154 \n", "L 769.33581 173.190056 \n", "L 769.423904 173.088044 \n", "L 769.446079 173.149197 \n", "L 770.019664 174.183923 \n", "L 770.195907 174.036822 \n", "L 770.217957 174.022148 \n", "L 770.239914 174.092582 \n", "L 770.34991 174.310978 \n", "L 770.371789 174.267578 \n", "L 770.614295 173.984898 \n", "L 770.680462 173.926645 \n", "L 770.702445 173.871246 \n", "L 770.790598 173.915282 \n", "L 770.988919 173.806225 \n", "L 771.407236 173.401267 \n", "L 771.45136 173.388464 \n", "L 771.473419 173.419085 \n", "L 771.583477 173.593475 \n", "L 771.627459 173.576514 \n", "L 771.693502 173.520889 \n", "L 771.737416 173.562325 \n", "L 772.090396 174.184113 \n", "L 772.112243 174.168889 \n", "L 772.24427 174.277942 \n", "L 772.420674 174.524975 \n", "L 772.86187 174.421831 \n", "L 773.192474 173.838788 \n", "L 773.3468 173.91919 \n", "L 773.501006 173.609735 \n", "L 773.56715 173.710346 \n", "L 773.677427 173.74209 \n", "L 773.699482 173.675002 \n", "L 773.875198 173.508708 \n", "L 773.96324 173.610522 \n", "L 774.051582 173.683969 \n", "L 774.073572 173.664962 \n", "L 774.183783 173.575072 \n", "L 774.205774 173.600373 \n", "L 774.382889 173.55202 \n", "L 774.55933 173.37504 \n", "L 774.647869 173.175115 \n", "L 774.735844 173.264181 \n", "L 774.912254 173.396519 \n", "L 774.97863 173.461683 \n", "L 775.132422 173.665336 \n", "L 775.220569 173.559297 \n", "L 775.794283 172.575312 \n", "L 775.860366 172.6191 \n", "L 776.256415 173.531098 \n", "L 776.300469 173.470895 \n", "L 776.36653 173.56766 \n", "L 776.542872 173.627806 \n", "L 776.587088 173.542293 \n", "L 776.631395 173.623013 \n", "L 776.697561 173.638443 \n", "L 776.719399 173.616216 \n", "L 776.74155 173.564741 \n", "L 776.807615 173.638366 \n", "L 776.939674 173.840202 \n", "L 776.984014 173.746068 \n", "L 777.050328 173.717132 \n", "L 777.094677 173.765002 \n", "L 777.249081 173.905998 \n", "L 777.271025 173.850272 \n", "L 777.315094 173.818732 \n", "L 777.337221 173.760752 \n", "L 777.491676 173.736294 \n", "L 777.602295 173.762567 \n", "L 777.80064 173.548851 \n", "L 778.065049 173.24086 \n", "L 778.241792 173.15385 \n", "L 778.285988 173.264981 \n", "L 778.638478 173.938652 \n", "L 778.771087 173.843382 \n", "L 778.990697 174.211705 \n", "L 779.210401 174.418942 \n", "L 779.254241 174.321019 \n", "L 779.320293 174.164489 \n", "L 779.386134 174.225537 \n", "L 779.539837 174.30706 \n", "L 780.244657 175.060999 \n", "L 780.399016 174.886187 \n", "L 780.421024 174.909221 \n", "L 780.44306 174.881451 \n", "L 780.619243 174.585617 \n", "L 780.641112 174.565976 \n", "L 780.685172 174.670421 \n", "L 780.729126 174.646218 \n", "L 780.75127 174.713432 \n", "L 780.861187 174.800598 \n", "L 780.883355 174.791801 \n", "L 780.971109 174.795371 \n", "L 781.102712 175.024491 \n", "L 781.146722 175.005964 \n", "L 781.300822 175.317723 \n", "L 781.366905 175.266713 \n", "L 781.521554 175.066886 \n", "L 781.676321 174.81612 \n", "L 781.698364 174.85301 \n", "L 781.72043 174.884725 \n", "L 781.764393 174.790911 \n", "L 781.984967 174.337282 \n", "L 782.007104 174.35833 \n", "L 782.051344 174.291828 \n", "L 782.095236 174.4006 \n", "L 782.27157 174.454885 \n", "L 782.359799 174.55604 \n", "L 782.403806 174.540941 \n", "L 782.469955 174.482993 \n", "L 782.491912 174.541542 \n", "L 782.557791 174.555834 \n", "L 782.579748 174.52515 \n", "L 782.623909 174.493648 \n", "L 782.667811 174.532962 \n", "L 782.711932 174.674492 \n", "L 782.777738 174.563367 \n", "L 783.240404 174.375493 \n", "L 783.505124 173.821097 \n", "L 783.549309 173.886575 \n", "L 783.659261 173.94011 \n", "L 783.681333 173.90799 \n", "L 783.725345 173.990258 \n", "L 783.74752 173.99891 \n", "L 783.923827 173.97142 \n", "L 783.990051 173.895897 \n", "L 784.034148 173.942325 \n", "L 784.584886 174.62 \n", "L 784.629122 174.541854 \n", "L 784.717215 174.466392 \n", "L 784.739352 174.513597 \n", "L 784.827366 174.761543 \n", "L 784.871429 174.674248 \n", "L 785.135185 174.547904 \n", "L 785.399452 173.911437 \n", "L 785.465608 173.863941 \n", "L 785.509619 173.752376 \n", "L 785.575708 173.877145 \n", "L 785.686053 173.795854 \n", "L 785.752004 173.80975 \n", "L 785.994619 174.193323 \n", "L 786.413244 174.441029 \n", "L 786.54508 174.41505 \n", "L 786.611021 174.378423 \n", "L 786.63315 174.462791 \n", "L 787.843242 175.993537 \n", "L 787.865366 175.96449 \n", "L 788.019509 175.892656 \n", "L 788.041622 175.916688 \n", "L 788.151556 175.938286 \n", "L 788.372299 175.568871 \n", "L 788.4163 175.628478 \n", "L 788.570117 175.777535 \n", "L 788.745787 175.932484 \n", "L 789.031127 176.489327 \n", "L 789.163269 176.500657 \n", "L 789.273089 176.547427 \n", "L 789.536666 176.342357 \n", "L 789.756841 175.988859 \n", "L 789.888659 175.706921 \n", "L 789.910527 175.713962 \n", "L 789.954421 175.685098 \n", "L 789.976448 175.629658 \n", "L 790.086801 175.524206 \n", "L 790.108987 175.5803 \n", "L 790.285731 175.824506 \n", "L 790.374098 175.965324 \n", "L 790.41818 175.880587 \n", "L 790.638807 175.604839 \n", "L 790.660926 175.690526 \n", "L 790.902968 176.460204 \n", "L 791.056919 176.501922 \n", "L 791.255605 176.184722 \n", "L 791.564408 175.869959 \n", "L 791.806996 175.962375 \n", "L 792.027162 176.171381 \n", "L 792.049277 176.148518 \n", "L 792.115412 176.081993 \n", "L 792.379493 175.670294 \n", "L 792.929033 174.76806 \n", "L 793.017445 174.698101 \n", "L 793.34727 174.299283 \n", "L 793.391325 174.385522 \n", "L 793.523228 174.459303 \n", "L 793.545125 174.397988 \n", "L 793.654694 174.353089 \n", "L 793.676562 174.431839 \n", "L 793.764475 174.361365 \n", "L 794.291969 174.307097 \n", "L 794.468052 174.029946 \n", "L 794.688137 173.755387 \n", "L 794.820107 173.50383 \n", "L 795.061587 173.632544 \n", "L 795.149555 173.720327 \n", "L 795.21593 173.66212 \n", "L 795.304175 173.794465 \n", "L 795.5462 173.54126 \n", "L 795.568081 173.577563 \n", "L 795.61201 173.522795 \n", "L 795.743806 173.350102 \n", "L 795.765648 173.366699 \n", "L 795.875797 173.407506 \n", "L 796.29346 174.421491 \n", "L 796.425509 174.266684 \n", "L 796.447503 174.181636 \n", "L 796.535122 174.20808 \n", "L 796.623197 174.394559 \n", "L 796.820917 174.802822 \n", "L 796.843004 174.837859 \n", "L 796.886986 174.733954 \n", "L 796.908887 174.74498 \n", "L 797.040876 174.795359 \n", "L 797.06279 174.716371 \n", "L 797.327239 174.132178 \n", "L 797.34922 174.148431 \n", "L 797.392991 174.191179 \n", "L 797.436823 174.144553 \n", "L 797.678795 173.823056 \n", "L 797.744493 173.955556 \n", "L 797.788511 173.881548 \n", "L 797.964438 173.809359 \n", "L 797.9863 173.86323 \n", "L 798.140264 174.359331 \n", "L 798.162307 174.327467 \n", "L 798.228356 174.49136 \n", "L 798.40434 174.617861 \n", "L 798.42625 174.584309 \n", "L 798.579585 174.529048 \n", "L 798.79999 174.880533 \n", "L 798.844193 174.844421 \n", "L 799.020166 174.757504 \n", "L 799.284063 174.919256 \n", "L 799.437558 174.762677 \n", "L 799.459421 174.840451 \n", "L 799.657372 175.041576 \n", "L 799.723255 175.180109 \n", "L 800.031227 175.862517 \n", "L 800.427445 176.517668 \n", "L 800.670076 176.422235 \n", "L 800.691917 176.461105 \n", "L 801.109946 177.263962 \n", "L 801.220575 177.375341 \n", "L 801.242722 177.34426 \n", "L 801.28666 177.384024 \n", "L 801.352526 177.312809 \n", "L 801.440817 177.228559 \n", "L 801.572605 177.033852 \n", "L 801.594568 177.070039 \n", "L 801.880497 176.787571 \n", "L 801.924222 176.908623 \n", "L 802.056007 177.236971 \n", "L 802.078112 177.202738 \n", "L 802.232273 177.008255 \n", "L 802.430185 177.07615 \n", "L 802.540293 176.997621 \n", "L 802.738035 177.463674 \n", "L 802.759947 177.451985 \n", "L 802.935834 177.377775 \n", "L 802.957733 177.408733 \n", "L 803.265365 178.026002 \n", "L 803.353546 178.08988 \n", "L 803.419121 178.149514 \n", "L 803.463059 178.078473 \n", "L 803.484989 178.088785 \n", "L 803.506889 178.019004 \n", "L 803.638544 178.002906 \n", "L 803.748247 178.116762 \n", "L 803.770145 178.100861 \n", "L 804.055634 178.201634 \n", "L 804.077748 178.265032 \n", "L 804.143758 178.129805 \n", "L 804.187716 178.044352 \n", "L 804.231666 177.888144 \n", "L 804.297348 177.92941 \n", "L 804.538603 178.184087 \n", "L 804.692163 178.148134 \n", "L 804.736273 178.145798 \n", "L 804.758346 178.181965 \n", "L 804.912451 178.345753 \n", "L 804.934605 178.326817 \n", "L 805.000434 178.193426 \n", "L 805.044408 178.247619 \n", "L 805.132517 178.335413 \n", "L 805.176264 178.29608 \n", "L 805.329989 178.113843 \n", "L 805.374234 178.166215 \n", "L 805.396362 178.090798 \n", "L 805.660754 177.602059 \n", "L 806.210413 177.976786 \n", "L 806.540599 177.728671 \n", "L 806.5847 177.793135 \n", "L 806.82634 177.832655 \n", "L 806.914169 177.780655 \n", "L 806.958206 177.804488 \n", "L 807.11256 177.797241 \n", "L 807.22296 177.752305 \n", "L 807.574993 177.207231 \n", "L 807.728951 177.328881 \n", "L 807.817362 177.345717 \n", "L 807.839488 177.315561 \n", "L 807.905756 177.176106 \n", "L 807.949826 177.264755 \n", "L 808.258505 177.534557 \n", "L 808.434519 177.86281 \n", "L 808.456587 177.836256 \n", "L 808.500308 177.733956 \n", "L 808.588243 177.792089 \n", "L 808.764691 177.798928 \n", "L 809.183434 178.333898 \n", "L 809.667257 178.122563 \n", "L 809.777051 178.230031 \n", "L 809.798924 178.219606 \n", "L 810.01854 178.191523 \n", "L 810.348441 178.678386 \n", "L 810.414155 178.74657 \n", "L 810.479818 178.846844 \n", "L 810.545675 178.773756 \n", "L 810.700274 178.850531 \n", "L 810.942065 179.094227 \n", "L 810.964046 179.150525 \n", "L 811.02995 179.097641 \n", "L 811.338253 178.302726 \n", "L 811.513978 178.095741 \n", "L 812.08446 178.387273 \n", "L 812.216178 178.320035 \n", "L 812.347878 178.46065 \n", "L 812.523523 178.516399 \n", "L 812.589332 178.44477 \n", "L 812.633159 178.286728 \n", "L 812.69874 178.36586 \n", "L 812.786858 178.437966 \n", "L 812.896917 178.630721 \n", "L 812.940828 178.554435 \n", "L 813.469168 178.144541 \n", "L 813.579403 177.868605 \n", "L 813.623645 177.884231 \n", "L 813.866165 177.833574 \n", "L 813.932114 177.803127 \n", "L 813.953964 177.826597 \n", "L 814.064027 177.958148 \n", "L 814.085964 177.93919 \n", "L 814.262301 178.025317 \n", "L 814.394181 177.825256 \n", "L 814.416129 177.866033 \n", "L 814.525888 177.946187 \n", "L 814.547815 177.913713 \n", "L 814.811903 177.906707 \n", "L 815.097968 178.255052 \n", "L 815.493756 178.13281 \n", "L 815.647541 177.869756 \n", "L 815.735482 177.777335 \n", "L 815.977279 177.474771 \n", "L 816.130957 177.591223 \n", "L 816.417025 177.490422 \n", "L 816.571109 177.648959 \n", "L 816.74678 178.256278 \n", "L 816.768816 178.246794 \n", "L 816.856962 178.416021 \n", "L 816.988752 178.474661 \n", "L 817.010636 178.451419 \n", "L 817.252526 178.309326 \n", "L 817.494463 177.822869 \n", "L 817.560461 177.82397 \n", "L 817.823867 177.246048 \n", "L 818.065747 177.01002 \n", "L 818.109625 177.065004 \n", "L 818.219668 177.155215 \n", "L 818.351456 177.15051 \n", "L 818.570993 177.349507 \n", "L 818.768848 177.62317 \n", "L 818.812747 177.597114 \n", "L 818.856615 177.693486 \n", "L 818.944625 177.821121 \n", "L 819.142627 178.032199 \n", "L 819.186505 177.954318 \n", "L 819.582599 177.825389 \n", "L 819.75813 177.698102 \n", "L 819.911491 177.895588 \n", "L 819.955354 177.835956 \n", "L 820.637632 177.22648 \n", "L 820.901334 177.111962 \n", "L 821.582885 178.178487 \n", "L 821.670715 178.097825 \n", "L 821.80243 178.143854 \n", "L 821.934273 177.947528 \n", "L 821.956225 178.020817 \n", "L 822.109929 178.01121 \n", "L 822.241902 177.790052 \n", "L 822.263809 177.809253 \n", "L 822.351389 177.81803 \n", "L 822.373413 177.787494 \n", "L 822.747776 177.606496 \n", "L 822.769721 177.644806 \n", "L 822.835788 177.810674 \n", "L 822.901718 177.754362 \n", "L 822.945605 177.765999 \n", "L 822.967535 177.807846 \n", "L 823.165703 178.215578 \n", "L 823.231728 178.427061 \n", "L 823.297704 178.413472 \n", "L 823.40763 178.463288 \n", "L 823.51763 178.51118 \n", "L 823.539528 178.493482 \n", "L 823.649329 178.608845 \n", "L 823.671239 178.548946 \n", "L 823.825069 178.33043 \n", "L 823.869259 178.403499 \n", "L 824.440025 178.429527 \n", "L 824.461958 178.380525 \n", "L 824.54964 178.43476 \n", "L 824.74743 178.538661 \n", "L 825.230337 178.785749 \n", "L 825.340312 179.01029 \n", "L 825.38436 178.961155 \n", "L 825.450136 178.940287 \n", "L 825.47205 178.969217 \n", "L 825.801724 179.474022 \n", "L 825.999656 179.686713 \n", "L 826.021552 179.669537 \n", "L 826.372999 179.839937 \n", "L 826.394942 179.780931 \n", "L 826.593321 179.42381 \n", "L 826.659187 179.318991 \n", "L 827.055133 178.839207 \n", "L 827.20912 178.761827 \n", "L 827.296982 178.578188 \n", "L 827.340984 178.622321 \n", "L 827.648918 178.876296 \n", "L 827.802922 179.119902 \n", "L 828.177005 179.36622 \n", "L 828.309045 179.499504 \n", "L 828.462977 179.468529 \n", "L 828.484976 179.495974 \n", "L 828.57271 179.495615 \n", "L 828.594836 179.464861 \n", "L 828.770512 179.297529 \n", "L 829.144049 179.320562 \n", "L 829.38625 179.654922 \n", "L 829.495963 179.722668 \n", "L 829.517964 179.70782 \n", "L 829.60563 179.512154 \n", "L 829.781464 179.236575 \n", "L 830.02311 179.61547 \n", "L 830.330644 179.551912 \n", "L 830.396492 179.582617 \n", "L 830.440495 179.552128 \n", "L 830.462666 179.531733 \n", "L 830.50685 179.637366 \n", "L 830.616751 179.635704 \n", "L 830.638816 179.691753 \n", "L 830.704694 179.776932 \n", "L 830.748573 179.666117 \n", "L 830.880497 179.629997 \n", "L 830.968442 179.733791 \n", "L 831.012361 179.692282 \n", "L 831.517629 179.58764 \n", "L 831.760016 179.799295 \n", "L 832.112015 179.970584 \n", "L 832.244109 180.236956 \n", "L 832.309746 180.136215 \n", "L 832.463473 180.034774 \n", "L 832.969045 180.73737 \n", "L 833.188436 180.680119 \n", "L 833.275871 180.716719 \n", "L 833.275871 180.716719 \n", "\" clip-path=\"url(#p6756c34627)\" style=\"fill: none; stroke: #ffa500; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 623.079234 188.792098 \n", "L 623.079234 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 623.079234 188.792098 \n", "L 843.285234 188.792098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_11\">\n", "     <path d=\"M 763.833672 48.944348 \n", "L 836.285234 48.944348 \n", "Q 838.285234 48.944348 838.285234 46.944348 \n", "L 838.285234 18.588098 \n", "Q 838.285234 16.588098 836.285234 16.588098 \n", "L 763.833672 16.588098 \n", "Q 761.833672 16.588098 761.833672 18.588098 \n", "L 761.833672 46.944348 \n", "Q 761.833672 48.944348 763.833672 48.944348 \n", "z\n", "\" style=\"fill: #ffffff; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_67\">\n", "     <path d=\"M 765.833672 24.686535 \n", "L 775.833672 24.686535 \n", "L 785.833672 24.686535 \n", "\" style=\"fill: none; stroke: #c44e52; stroke-width: 2; stroke-linecap: round\"/>\n", "    </g>\n", "    <g id=\"text_40\">\n", "     <!-- Collided -->\n", "     <g style=\"fill: #262626\" transform=\"translate(793.833672 28.186535) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"186.572266\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"214.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"277.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"339.355469\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_68\">\n", "     <path d=\"M 765.833672 39.36466 \n", "L 775.833672 39.36466 \n", "L 785.833672 39.36466 \n", "\" style=\"fill: none; stroke: #ffa500; stroke-width: 2; stroke-linecap: round\"/>\n", "    </g>\n", "    <g id=\"text_41\">\n", "     <!-- Off-road -->\n", "     <g style=\"fill: #262626\" transform=\"translate(793.833672 42.86466) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4f\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"113.916016\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"143.621094\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"179.705078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"218.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"279.75\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"341.029297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 206.980434 131.200798 \n", "L 273.042234 131.200798 \n", "L 273.042234 69.179398 \n", "L 206.980434 69.179398 \n", "L 206.980434 131.200798 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_69\">\n", "      <path d=\"M 220.842652 131.200798 \n", "L 220.842652 69.179398 \n", "\" clip-path=\"url(#p764bb8d49b)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"220.842652\" y=\"131.200798\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_42\">\n", "      <!-- 20.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(207.984254 149.476993) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_71\">\n", "      <path d=\"M 253.779405 131.200798 \n", "L 253.779405 69.179398 \n", "\" clip-path=\"url(#p764bb8d49b)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"253.779405\" y=\"131.200798\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_43\">\n", "      <!-- 22.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(240.921007 149.476993) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_73\">\n", "    <path d=\"M 206.980434 131.098675 \n", "L 207.046803 131.200798 \n", "L 207.112917 130.784413 \n", "L 207.211807 129.21703 \n", "L 207.310909 127.60157 \n", "L 207.344202 127.803459 \n", "L 207.377191 127.770754 \n", "L 207.410411 127.57598 \n", "L 207.443643 128.008067 \n", "L 207.476797 127.925734 \n", "L 207.509769 127.338855 \n", "L 207.576845 127.943448 \n", "L 207.609977 127.411619 \n", "L 207.775093 129.293057 \n", "L 207.841159 129.120762 \n", "L 207.874233 128.022146 \n", "L 207.973459 128.209232 \n", "L 208.039958 127.250909 \n", "L 208.106157 127.353215 \n", "L 208.139324 127.496678 \n", "L 208.306111 123.040713 \n", "L 208.405831 123.406259 \n", "L 208.439035 123.648515 \n", "L 208.572 120.336341 \n", "L 208.605171 120.389895 \n", "L 208.737935 122.012562 \n", "L 208.771137 121.74382 \n", "L 208.804369 122.204819 \n", "L 208.870513 121.636374 \n", "L 208.969755 122.431676 \n", "L 209.366628 113.89596 \n", "L 209.399719 114.068877 \n", "L 209.432924 114.317116 \n", "L 209.466168 113.91865 \n", "L 209.499518 113.900039 \n", "L 209.664444 111.93272 \n", "L 209.763857 112.8994 \n", "L 209.797236 112.065063 \n", "L 209.86377 112.771477 \n", "L 209.896952 112.880852 \n", "L 209.930135 112.851647 \n", "L 210.030031 114.802343 \n", "L 210.063171 114.345238 \n", "L 210.129354 113.383067 \n", "L 210.195598 113.723265 \n", "L 210.2287 113.524065 \n", "L 210.294577 113.708773 \n", "L 210.327742 113.610684 \n", "L 210.560246 111.17828 \n", "L 210.593471 111.399425 \n", "L 210.62661 110.989709 \n", "L 210.65941 111.291111 \n", "L 210.890439 109.063416 \n", "L 210.923775 109.112347 \n", "L 211.056008 107.794612 \n", "L 211.089762 107.889236 \n", "L 211.122741 107.667675 \n", "L 211.2554 105.274983 \n", "L 211.321613 105.376346 \n", "L 211.355032 104.936192 \n", "L 211.420819 105.034894 \n", "L 211.684755 107.850896 \n", "L 211.717623 107.408136 \n", "L 211.783303 107.459056 \n", "L 211.915337 108.851868 \n", "L 211.948288 108.8038 \n", "L 212.047555 107.381441 \n", "L 212.08063 107.416575 \n", "L 212.345139 112.96695 \n", "L 212.411475 114.091393 \n", "L 212.47767 114.016759 \n", "L 212.576809 113.209748 \n", "L 212.841543 115.569639 \n", "L 212.874682 115.347074 \n", "L 212.907592 115.436654 \n", "L 212.974195 116.336103 \n", "L 213.007154 116.060828 \n", "L 213.436882 110.961824 \n", "L 213.535637 111.082835 \n", "L 213.568497 111.312554 \n", "L 213.700087 109.59937 \n", "L 213.733224 109.744956 \n", "L 213.766408 109.612955 \n", "L 213.931417 106.933594 \n", "L 213.964223 107.102375 \n", "L 213.997059 107.157668 \n", "L 214.260647 108.937731 \n", "L 214.293505 108.657657 \n", "L 214.326657 108.968745 \n", "L 214.392525 109.620681 \n", "L 214.42553 109.460075 \n", "L 214.524413 108.775882 \n", "L 214.557339 109.021881 \n", "L 214.657257 111.239986 \n", "L 214.822499 113.217941 \n", "L 215.054097 114.708875 \n", "L 215.1864 117.637062 \n", "L 215.219493 117.494951 \n", "L 215.384487 118.073838 \n", "L 215.417458 117.769967 \n", "L 215.483413 117.807988 \n", "L 215.516387 118.061523 \n", "L 215.615687 116.543169 \n", "L 215.681918 116.798296 \n", "L 215.848056 119.307861 \n", "L 215.914418 119.495847 \n", "L 215.947624 119.252457 \n", "L 215.980793 118.642069 \n", "L 216.013913 119.416243 \n", "L 216.246164 123.560128 \n", "L 216.444615 124.370359 \n", "L 216.577166 125.86528 \n", "L 216.742514 124.080175 \n", "L 216.808816 124.455739 \n", "L 216.842078 124.366431 \n", "L 217.238898 119.392992 \n", "L 217.271845 119.536998 \n", "L 217.304992 119.153385 \n", "L 217.371474 119.199613 \n", "L 217.404334 119.307407 \n", "L 217.43759 119.145924 \n", "L 217.470671 118.776866 \n", "L 217.503908 119.176704 \n", "L 217.702222 122.773966 \n", "L 217.768279 121.985466 \n", "L 217.867324 123.06443 \n", "L 218.298523 116.003818 \n", "L 218.364761 116.362924 \n", "L 218.397686 116.40792 \n", "L 218.563318 114.819168 \n", "L 218.59627 115.00745 \n", "L 219.093109 119.632052 \n", "L 219.158951 119.654938 \n", "L 219.224753 118.670568 \n", "L 219.389637 120.968133 \n", "L 219.554256 121.486251 \n", "L 219.619926 122.171757 \n", "L 219.685855 123.217848 \n", "L 219.752046 122.984131 \n", "L 219.7853 122.99271 \n", "L 219.851006 122.173317 \n", "L 219.883888 122.377852 \n", "L 219.982956 122.949022 \n", "L 220.015886 122.849717 \n", "L 220.181021 120.615013 \n", "L 220.214087 121.303437 \n", "L 220.247189 121.847788 \n", "L 220.313463 121.54682 \n", "L 220.611022 114.720611 \n", "L 220.676987 115.528377 \n", "L 220.842067 117.893325 \n", "L 220.875162 117.700115 \n", "L 221.007356 118.640369 \n", "L 221.040446 118.860552 \n", "L 221.073464 118.791761 \n", "L 221.106311 118.010584 \n", "L 221.205747 118.043286 \n", "L 221.404815 116.673184 \n", "L 221.536875 117.229174 \n", "L 221.603316 116.645655 \n", "L 221.735817 115.102266 \n", "L 221.801656 115.53667 \n", "L 221.9009 114.713649 \n", "L 221.933715 114.879981 \n", "L 221.966475 115.203342 \n", "L 221.999347 114.607787 \n", "L 222.032293 114.35744 \n", "L 222.06515 114.886887 \n", "L 222.229656 117.084665 \n", "L 222.526909 114.932901 \n", "L 222.560161 115.850423 \n", "L 222.625678 114.692128 \n", "L 222.691566 114.401929 \n", "L 222.724557 114.592222 \n", "L 222.75756 114.566532 \n", "L 222.989604 110.891223 \n", "L 223.25383 108.514637 \n", "L 223.320285 107.73651 \n", "L 223.452204 106.583925 \n", "L 223.584042 107.638714 \n", "L 223.683444 108.639864 \n", "L 223.716425 108.018617 \n", "L 223.74975 108.494323 \n", "L 223.948749 110.478913 \n", "L 224.114117 107.152488 \n", "L 224.180501 106.769586 \n", "L 224.246561 107.547636 \n", "L 224.27981 107.36695 \n", "L 224.47746 103.902869 \n", "L 224.676112 101.766397 \n", "L 224.908491 99.875773 \n", "L 224.974755 100.295966 \n", "L 225.23946 103.092314 \n", "L 225.305305 102.640717 \n", "L 225.338353 103.008299 \n", "L 225.371468 103.177622 \n", "L 225.404571 103.02336 \n", "L 225.503766 101.739033 \n", "L 225.536735 102.281941 \n", "L 225.669098 104.060786 \n", "L 225.702458 103.877033 \n", "L 225.768302 104.041992 \n", "L 225.801355 104.151788 \n", "L 225.834692 104.123797 \n", "L 226.099123 98.806955 \n", "L 226.198271 100.084994 \n", "L 226.529321 101.077214 \n", "L 226.595581 100.405287 \n", "L 226.728204 101.825361 \n", "L 226.794274 102.579845 \n", "L 226.82746 103.17727 \n", "L 226.893744 102.423898 \n", "L 226.99266 101.390084 \n", "L 227.124932 100.259271 \n", "L 227.489058 104.575852 \n", "L 227.687594 102.252653 \n", "L 227.720766 101.824446 \n", "L 227.787313 102.274714 \n", "L 227.820257 102.217534 \n", "L 228.018426 100.245566 \n", "L 228.183287 101.082447 \n", "L 228.24917 100.525705 \n", "L 228.281999 100.192581 \n", "L 228.314951 100.916839 \n", "L 228.380612 100.632533 \n", "L 228.413508 100.742094 \n", "L 228.578265 97.279279 \n", "L 228.644246 98.23899 \n", "L 228.875041 100.309651 \n", "L 228.908024 100.165207 \n", "L 229.040865 98.923835 \n", "L 229.073922 99.039058 \n", "L 229.106849 99.45964 \n", "L 229.139618 98.843467 \n", "L 229.304859 96.02249 \n", "L 229.403685 97.145692 \n", "L 229.436483 96.566671 \n", "L 229.535389 96.03712 \n", "L 229.568236 96.073831 \n", "L 229.7988 97.561355 \n", "L 229.831784 97.245229 \n", "L 229.897537 98.649438 \n", "L 229.963415 98.345156 \n", "L 229.996245 98.328899 \n", "L 230.227677 96.553216 \n", "L 230.260648 96.415498 \n", "L 230.293982 97.169177 \n", "L 230.36046 96.356578 \n", "L 230.492958 94.65924 \n", "L 230.625509 94.77695 \n", "L 230.824101 93.398435 \n", "L 230.857042 93.808821 \n", "L 230.989185 95.956288 \n", "L 231.121643 94.935547 \n", "L 231.187727 95.171324 \n", "L 231.32015 96.079186 \n", "L 231.419944 95.256159 \n", "L 231.5526 96.792819 \n", "L 231.751242 95.759487 \n", "L 231.883165 94.656426 \n", "L 231.91648 95.236916 \n", "L 231.982619 96.053894 \n", "L 232.015726 95.198909 \n", "L 232.214805 93.256786 \n", "L 232.248298 93.253996 \n", "L 232.380856 92.236617 \n", "L 232.4471 93.460767 \n", "L 232.513107 93.166084 \n", "L 232.546315 93.175901 \n", "L 232.64566 93.701217 \n", "L 232.678877 93.657504 \n", "L 232.711909 93.324262 \n", "L 232.811544 93.38097 \n", "L 232.844405 93.168128 \n", "L 233.009932 96.78086 \n", "L 233.175623 99.386613 \n", "L 233.209015 99.503526 \n", "L 233.242042 99.405486 \n", "L 233.374553 101.786086 \n", "L 233.772889 105.925892 \n", "L 233.806149 105.779272 \n", "L 233.905298 105.117225 \n", "L 234.369772 113.748555 \n", "L 234.568147 112.49333 \n", "L 234.634784 113.37859 \n", "L 234.767561 113.226098 \n", "L 234.834119 113.1235 \n", "L 234.86746 113.239918 \n", "L 235.033362 111.134906 \n", "L 235.265325 108.781671 \n", "L 235.29857 108.831372 \n", "L 235.49689 107.388713 \n", "L 235.530046 107.793227 \n", "L 235.563356 107.751201 \n", "L 235.79466 104.337674 \n", "L 235.860529 104.574269 \n", "L 235.893171 104.587369 \n", "L 235.992523 105.624267 \n", "L 236.025369 105.236221 \n", "L 236.124945 105.669365 \n", "L 236.290833 107.849893 \n", "L 236.457035 106.081586 \n", "L 236.4899 106.255993 \n", "L 236.523094 105.883785 \n", "L 236.556287 105.943323 \n", "L 236.722345 104.58917 \n", "L 236.954355 106.200322 \n", "L 237.053899 105.79917 \n", "L 237.086941 105.847696 \n", "L 237.120197 106.659458 \n", "L 237.186667 106.075761 \n", "L 237.252751 105.700809 \n", "L 237.285897 105.918647 \n", "L 237.319157 106.218691 \n", "L 237.352221 106.09834 \n", "L 237.385617 104.338331 \n", "L 237.451509 105.29772 \n", "L 237.484741 105.098734 \n", "L 237.517683 105.292284 \n", "L 237.749324 109.168315 \n", "L 237.782394 108.63026 \n", "L 237.815371 109.118618 \n", "L 237.848597 109.659292 \n", "L 237.882161 108.597284 \n", "L 237.915229 108.360952 \n", "L 237.981652 108.561343 \n", "L 238.047588 108.733569 \n", "L 238.179599 106.650898 \n", "L 238.245524 107.515041 \n", "L 238.278909 107.497253 \n", "L 238.311999 107.17442 \n", "L 238.378283 107.517083 \n", "L 238.41123 107.852188 \n", "L 238.444241 107.472311 \n", "L 238.477177 107.550242 \n", "L 238.642286 105.914616 \n", "L 238.675041 106.29985 \n", "L 238.840063 107.286798 \n", "L 238.905866 107.503325 \n", "L 238.939196 107.19481 \n", "L 238.972338 107.386018 \n", "L 239.105013 108.857954 \n", "L 239.137948 108.63373 \n", "L 239.303029 106.006194 \n", "L 239.63329 109.305146 \n", "L 239.76606 111.567712 \n", "L 240.130456 114.423084 \n", "L 240.163586 114.638164 \n", "L 240.196758 114.209421 \n", "L 240.295736 113.365042 \n", "L 240.328675 113.519952 \n", "L 240.361883 113.546472 \n", "L 240.3948 113.316892 \n", "L 240.55954 115.562865 \n", "L 240.658396 114.881486 \n", "L 240.691271 114.115921 \n", "L 240.790478 114.383124 \n", "L 240.823419 113.637077 \n", "L 240.922262 113.734931 \n", "L 241.05404 112.930611 \n", "L 241.120142 113.161177 \n", "L 241.350734 107.822305 \n", "L 241.38461 107.963648 \n", "L 241.417377 107.540342 \n", "L 241.450592 108.066261 \n", "L 241.483776 108.210898 \n", "L 241.516568 109.187966 \n", "L 241.549518 107.403582 \n", "L 241.582561 107.601954 \n", "L 241.615272 107.300646 \n", "L 241.9453 102.660991 \n", "L 241.978438 102.583497 \n", "L 242.110502 101.150303 \n", "L 242.143382 101.378499 \n", "L 242.176364 101.536902 \n", "L 242.507362 93.230627 \n", "L 242.639656 92.340966 \n", "L 242.672555 92.643886 \n", "L 242.804566 94.221257 \n", "L 242.970112 97.60664 \n", "L 243.631405 104.101992 \n", "L 243.730847 103.929936 \n", "L 243.896654 103.009269 \n", "L 243.929738 103.516205 \n", "L 243.996194 103.092614 \n", "L 244.128433 102.214117 \n", "L 244.1946 101.522107 \n", "L 244.392891 100.183518 \n", "L 244.558042 96.623929 \n", "L 244.590984 96.796973 \n", "L 244.624417 96.11484 \n", "L 244.690211 96.35425 \n", "L 244.723401 96.467151 \n", "L 244.756319 95.733489 \n", "L 244.855353 95.878224 \n", "L 244.922037 95.722759 \n", "L 244.955035 95.802336 \n", "L 245.053843 96.943645 \n", "L 245.086774 96.905912 \n", "L 245.317702 93.848053 \n", "L 245.582401 89.867892 \n", "L 245.64873 90.071619 \n", "L 245.681583 90.523052 \n", "L 245.714436 89.823962 \n", "L 245.74744 89.728695 \n", "L 245.7805 89.803037 \n", "L 246.01105 86.151379 \n", "L 246.043912 86.258378 \n", "L 246.076923 85.704808 \n", "L 246.142853 86.148185 \n", "L 246.175558 86.23087 \n", "L 246.208527 85.874697 \n", "L 246.274937 86.168325 \n", "L 246.340956 87.052741 \n", "L 246.440208 88.592082 \n", "L 246.473252 88.341939 \n", "L 246.50619 88.108916 \n", "L 246.539417 88.554603 \n", "L 247.067526 94.755567 \n", "L 247.133415 94.002602 \n", "L 247.199192 94.280854 \n", "L 247.231978 94.685094 \n", "L 247.265007 94.238707 \n", "L 247.298034 94.345792 \n", "L 247.331245 94.348582 \n", "L 247.364155 93.538761 \n", "L 247.430127 94.242897 \n", "L 247.56193 96.003477 \n", "L 247.628151 95.959986 \n", "L 247.661223 96.409849 \n", "L 247.694211 96.345824 \n", "L 247.85954 94.221483 \n", "L 247.892581 94.477758 \n", "L 248.057536 91.288735 \n", "L 248.090408 91.437442 \n", "L 248.12328 91.130376 \n", "L 248.222311 88.93247 \n", "L 248.288192 89.301442 \n", "L 248.321278 89.248648 \n", "L 248.420948 88.509452 \n", "L 248.65176 92.111603 \n", "L 248.816697 90.290035 \n", "L 248.850066 90.197474 \n", "L 248.949063 90.859022 \n", "L 248.982381 90.394488 \n", "L 249.015319 90.110514 \n", "L 249.246041 94.42207 \n", "L 249.311746 95.045342 \n", "L 249.344736 94.98692 \n", "L 249.443665 94.167721 \n", "L 249.707366 97.79511 \n", "L 249.904969 95.261031 \n", "L 250.466507 98.51751 \n", "L 250.565836 97.865397 \n", "L 250.664636 97.9725 \n", "L 250.7306 97.698821 \n", "L 250.82955 97.716123 \n", "L 250.862507 97.276975 \n", "L 250.895396 97.444715 \n", "L 251.258577 91.145682 \n", "L 251.29156 91.758368 \n", "L 251.324532 91.740986 \n", "L 251.522963 89.087309 \n", "L 251.852976 85.851699 \n", "L 251.885973 87.088532 \n", "L 251.985638 86.714554 \n", "L 252.11799 87.60515 \n", "L 252.151102 87.509189 \n", "L 252.184257 87.481944 \n", "L 252.217022 87.564585 \n", "L 252.480684 88.90656 \n", "L 252.513585 88.59455 \n", "L 252.612716 88.704914 \n", "L 252.744464 89.428142 \n", "L 252.975279 87.27804 \n", "L 253.272872 91.788932 \n", "L 253.305676 91.4511 \n", "L 253.504367 94.295386 \n", "L 253.769732 97.60343 \n", "L 253.901871 97.890339 \n", "L 253.967822 97.266739 \n", "L 254.000857 98.043436 \n", "L 254.099962 97.815809 \n", "L 254.132947 97.625702 \n", "L 254.265151 98.602344 \n", "L 254.298027 98.599338 \n", "L 254.331269 98.802252 \n", "L 254.397178 98.527152 \n", "L 254.430321 98.628917 \n", "L 254.595935 96.326577 \n", "L 254.727941 94.267282 \n", "L 254.761015 94.225038 \n", "L 254.860159 93.217308 \n", "L 254.926167 93.268039 \n", "L 254.959428 93.188231 \n", "L 254.992711 93.650381 \n", "L 255.025951 92.843827 \n", "L 255.058944 92.819211 \n", "L 255.091898 92.586055 \n", "L 255.290358 88.226303 \n", "L 255.356098 87.524485 \n", "L 255.389478 87.919473 \n", "L 255.522359 88.749675 \n", "L 255.555383 88.016518 \n", "L 255.621527 88.297338 \n", "L 255.654409 88.461788 \n", "L 255.918079 85.256171 \n", "L 255.950988 85.76663 \n", "L 255.983874 86.430982 \n", "L 256.049682 85.807655 \n", "L 256.082594 85.940402 \n", "L 256.148533 85.314437 \n", "L 256.214486 85.613042 \n", "L 256.28074 85.336851 \n", "L 256.446143 87.133822 \n", "L 256.578002 86.017233 \n", "L 256.610823 86.557971 \n", "L 256.643734 85.989118 \n", "L 256.676619 86.362741 \n", "L 256.808619 84.779224 \n", "L 256.841934 84.89532 \n", "L 257.072847 83.363372 \n", "L 257.138838 84.042454 \n", "L 257.171909 83.76663 \n", "L 257.237624 83.850891 \n", "L 257.270548 83.094882 \n", "L 257.369181 80.496373 \n", "L 257.402112 81.067456 \n", "L 257.567024 79.938868 \n", "L 257.699003 80.809418 \n", "L 257.962885 77.374758 \n", "L 258.127555 79.112402 \n", "L 258.45694 82.050004 \n", "L 258.522818 81.249399 \n", "L 258.555676 81.515861 \n", "L 258.68767 82.17795 \n", "L 258.72076 82.07686 \n", "L 258.78673 82.215988 \n", "L 258.885482 83.458506 \n", "L 258.984676 83.328247 \n", "L 259.083575 83.319716 \n", "L 259.281059 84.371398 \n", "L 259.347037 84.285019 \n", "L 259.413216 83.47496 \n", "L 259.446097 84.463027 \n", "L 259.511932 84.688054 \n", "L 259.577766 83.972213 \n", "L 259.644053 85.192853 \n", "L 259.67709 84.556293 \n", "L 259.709987 84.611967 \n", "L 259.743007 84.562166 \n", "L 259.776115 84.499132 \n", "L 259.809039 85.186142 \n", "L 259.907579 85.099239 \n", "L 260.039692 86.184332 \n", "L 260.138356 84.804239 \n", "L 260.17136 85.188498 \n", "L 260.204651 85.178793 \n", "L 260.369322 83.18637 \n", "L 260.435662 84.374566 \n", "L 260.501678 84.179297 \n", "L 260.567419 83.133519 \n", "L 260.633641 83.50593 \n", "L 260.699512 82.526154 \n", "L 260.765377 83.128592 \n", "L 260.798378 83.197134 \n", "L 260.930377 84.186486 \n", "L 260.996261 83.58366 \n", "L 261.095123 83.897902 \n", "L 261.128131 83.883345 \n", "L 261.194192 84.287206 \n", "L 261.227275 84.280131 \n", "L 261.590045 79.104265 \n", "L 261.721937 77.691786 \n", "L 261.853961 76.653489 \n", "L 261.919988 76.293643 \n", "L 261.952971 76.494334 \n", "L 261.986173 76.788934 \n", "L 262.052474 76.660444 \n", "L 262.282636 74.468973 \n", "L 262.315603 74.493896 \n", "L 262.44745 74.114951 \n", "L 262.480974 74.01641 \n", "L 262.547091 74.535477 \n", "L 262.579972 74.525636 \n", "L 262.646009 73.599287 \n", "L 262.71215 74.018707 \n", "L 263.17486 79.063289 \n", "L 263.207781 79.279033 \n", "L 263.240726 78.768983 \n", "L 263.273524 79.159874 \n", "L 263.33939 78.569231 \n", "L 263.372557 78.895699 \n", "L 263.471879 80.64112 \n", "L 263.537638 80.54994 \n", "L 263.570594 80.36271 \n", "L 263.603697 80.768178 \n", "L 263.636804 80.776254 \n", "L 263.669688 80.625105 \n", "L 263.867751 83.299889 \n", "L 263.900835 82.615346 \n", "L 263.966771 83.136955 \n", "L 264.197301 86.355918 \n", "L 264.328744 85.778147 \n", "L 264.361602 86.578385 \n", "L 264.427601 86.204421 \n", "L 264.690885 83.048101 \n", "L 264.723838 83.32218 \n", "L 264.789776 82.962268 \n", "L 265.25116 79.470449 \n", "L 265.349812 80.181878 \n", "L 265.481536 78.319699 \n", "L 265.514335 78.39947 \n", "L 265.58023 79.21127 \n", "L 265.613153 79.186919 \n", "L 265.646075 78.475149 \n", "L 265.678981 79.222588 \n", "L 265.711889 78.824747 \n", "L 265.74472 78.809968 \n", "L 265.909871 77.78068 \n", "L 265.810934 78.862908 \n", "L 265.942968 77.921643 \n", "L 265.975946 78.132597 \n", "L 266.108291 80.463015 \n", "L 266.141193 80.364552 \n", "L 266.273394 79.250317 \n", "L 266.306442 80.082347 \n", "L 266.372355 79.653331 \n", "L 266.438307 79.338999 \n", "L 266.603195 76.585179 \n", "L 266.636201 76.668588 \n", "L 266.834183 74.263432 \n", "L 266.900514 73.28538 \n", "L 266.966486 73.928769 \n", "L 266.99938 74.020231 \n", "L 267.032547 73.968254 \n", "L 267.098667 73.100069 \n", "L 267.131654 72.403813 \n", "L 267.23076 72.431229 \n", "L 267.561031 75.73518 \n", "L 267.593901 75.561989 \n", "L 267.62696 75.860386 \n", "L 267.659912 76.065331 \n", "L 267.725722 74.959168 \n", "L 267.758677 75.658719 \n", "L 267.791665 75.985392 \n", "L 267.824442 75.590133 \n", "L 267.857412 75.075534 \n", "L 267.923639 75.4837 \n", "L 268.02249 74.917204 \n", "L 268.088553 74.056355 \n", "L 268.154591 74.296868 \n", "L 268.385441 78.098259 \n", "L 268.418534 77.814292 \n", "L 268.583655 79.819321 \n", "L 268.616612 79.625185 \n", "L 268.682509 79.754671 \n", "L 268.71573 79.759441 \n", "L 268.881312 81.502103 \n", "L 268.914424 81.350318 \n", "L 269.079501 79.866344 \n", "L 269.178353 78.34212 \n", "L 269.211308 78.570025 \n", "L 269.310249 80.023556 \n", "L 269.343395 79.622784 \n", "L 269.37654 79.311863 \n", "L 269.409556 79.953776 \n", "L 269.607872 80.472711 \n", "L 269.640775 80.286782 \n", "L 269.806119 79.19468 \n", "L 269.872321 79.073076 \n", "L 270.003903 80.219852 \n", "L 270.070166 79.667894 \n", "L 270.1693 81.751482 \n", "L 270.235729 81.447894 \n", "L 270.268827 81.232567 \n", "L 270.30173 81.360227 \n", "L 270.367485 81.218646 \n", "L 270.632732 84.52098 \n", "L 270.798153 83.004767 \n", "L 270.897124 84.182619 \n", "L 270.963302 83.9089 \n", "L 271.029308 82.915536 \n", "L 271.095212 83.253071 \n", "L 271.161015 82.953042 \n", "L 271.193985 83.067411 \n", "L 271.359496 81.457307 \n", "L 271.524637 80.840216 \n", "L 271.623611 82.221085 \n", "L 271.656523 81.986824 \n", "L 271.722689 81.447251 \n", "L 271.755635 81.816279 \n", "L 271.78866 82.701702 \n", "L 271.854469 81.85688 \n", "L 272.613202 69.768665 \n", "L 272.646117 70.081663 \n", "L 272.679092 69.179398 \n", "L 272.744889 70.030537 \n", "L 272.777799 70.130663 \n", "L 272.975633 69.433533 \n", "L 273.00855 69.318425 \n", "L 273.042234 69.330373 \n", "\" clip-path=\"url(#p764bb8d49b)\" style=\"fill: none; stroke: #55a868; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 206.980434 131.200798 \n", "L 206.980434 69.179398 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 206.980434 131.200798 \n", "L 273.042234 131.200798 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_44\">\n", "    <!-- Zoom-in -->\n", "    <g transform=\"translate(210.283524 67.474625) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-5a\" d=\"M 359 4666 \n", "L 4025 4666 \n", "L 4025 4184 \n", "L 1075 531 \n", "L 4097 531 \n", "L 4097 0 \n", "L 288 0 \n", "L 288 481 \n", "L 3238 4134 \n", "L 359 4134 \n", "L 359 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-5a\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"68.505859\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"129.6875\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"190.869141\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"288.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"324.365234\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"352.148438\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_45\">\n", "    <!-- 0.981 -->\n", "    <g style=\"fill: #ff0000\" transform=\"translate(172.666372 131.12111) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"222.65625\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_46\">\n", "    <!-- 0.984 -->\n", "    <g style=\"fill: #ff0000\" transform=\"translate(259.666372 61.777835) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-30\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" x=\"222.65625\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 489.214434 131.200798 \n", "L 555.276234 131.200798 \n", "L 555.276234 69.179398 \n", "L 489.214434 69.179398 \n", "L 489.214434 131.200798 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_74\">\n", "      <path d=\"M 489.61303 131.200798 \n", "L 489.61303 69.179398 \n", "\" clip-path=\"url(#p89aeb5621e)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"489.61303\" y=\"131.200798\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_47\">\n", "      <!-- 2.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(480.428975 149.476993) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_76\">\n", "      <path d=\"M 552.203064 131.200798 \n", "L 552.203064 69.179398 \n", "\" clip-path=\"url(#p89aeb5621e)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"552.203064\" y=\"131.200798\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_48\">\n", "      <!-- 2.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(543.019009 149.476993) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_49\">\n", "     <!-- 1e9 -->\n", "     <g style=\"fill: #262626\" transform=\"translate(533.472 163.655227) scale(0.1155 -0.1155)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"125.146484\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <path d=\"M 489.214434 131.200798 \n", "L 489.743467 128.140021 \n", "L 489.776462 128.175673 \n", "L 489.875529 128.326591 \n", "L 489.908631 128.23508 \n", "L 490.27208 127.247221 \n", "L 490.304961 127.314678 \n", "L 490.33826 127.157246 \n", "L 490.570659 126.07261 \n", "L 490.769889 124.496788 \n", "L 490.902671 123.666464 \n", "L 491.101748 122.391105 \n", "L 491.333299 120.770888 \n", "L 491.597937 119.024194 \n", "L 491.664037 119.000035 \n", "L 492.161064 116.349568 \n", "L 492.227209 116.298485 \n", "L 492.558862 114.803596 \n", "L 492.857393 112.925433 \n", "L 492.89028 112.893006 \n", "L 493.055586 111.762648 \n", "L 493.088439 111.762248 \n", "L 493.287383 110.899675 \n", "L 493.420006 110.419581 \n", "L 494.246071 106.74755 \n", "L 494.311746 106.527225 \n", "L 494.477111 105.832822 \n", "L 494.51036 105.888866 \n", "L 494.576253 105.800932 \n", "L 494.609508 105.779375 \n", "L 495.040158 104.187621 \n", "L 495.073042 104.180531 \n", "L 495.139253 103.894065 \n", "L 495.172326 103.991062 \n", "L 495.238382 103.941084 \n", "L 495.27125 104.069034 \n", "L 495.436523 103.530892 \n", "L 495.46979 103.638602 \n", "L 495.502766 103.599678 \n", "L 495.667579 103.123769 \n", "L 495.73325 103.247744 \n", "L 495.898229 102.605927 \n", "L 496.426659 100.076027 \n", "L 496.492577 100.096884 \n", "L 496.624756 99.870155 \n", "L 496.657782 99.917637 \n", "L 496.690951 99.809588 \n", "L 496.756778 99.410698 \n", "L 496.823088 99.600491 \n", "L 496.95557 99.234056 \n", "L 497.021458 99.460714 \n", "L 497.054565 99.164513 \n", "L 497.187179 98.849909 \n", "L 497.220177 99.132333 \n", "L 497.253029 98.999462 \n", "L 497.385263 98.579847 \n", "L 497.55097 98.75552 \n", "L 497.58379 98.686261 \n", "L 497.616751 98.308293 \n", "L 497.715376 98.353715 \n", "L 497.880441 98.785755 \n", "L 498.17889 97.994305 \n", "L 498.344351 98.04817 \n", "L 498.476853 97.471305 \n", "L 498.509693 97.495851 \n", "L 498.542529 97.571753 \n", "L 498.608702 97.191783 \n", "L 498.674718 97.31551 \n", "L 498.707603 97.208585 \n", "L 498.740845 97.356571 \n", "L 498.872782 97.744022 \n", "L 498.905607 97.648054 \n", "L 498.93883 97.618452 \n", "L 499.104137 98.159695 \n", "L 499.170018 98.223291 \n", "L 499.334446 98.748702 \n", "L 499.400582 98.788073 \n", "L 499.665526 99.5169 \n", "L 499.698806 99.513522 \n", "L 499.864419 99.904874 \n", "L 499.89725 99.880738 \n", "L 499.996067 99.742528 \n", "L 500.029014 99.766221 \n", "L 500.227185 99.749076 \n", "L 500.689932 98.389596 \n", "L 500.723198 98.510428 \n", "L 500.789197 98.435894 \n", "L 500.954497 98.164047 \n", "L 500.987702 98.247494 \n", "L 501.053999 98.289823 \n", "L 501.086974 98.219766 \n", "L 501.15286 98.125007 \n", "L 501.186055 98.263769 \n", "L 501.318308 98.42511 \n", "L 501.482983 98.15377 \n", "L 501.548867 98.066074 \n", "L 501.813674 97.027919 \n", "L 501.978876 97.129938 \n", "L 502.176904 97.450626 \n", "L 502.34157 97.548048 \n", "L 502.374837 97.476181 \n", "L 502.40783 97.416681 \n", "L 502.440867 97.499517 \n", "L 502.606581 97.893078 \n", "L 502.771906 97.666279 \n", "L 502.805177 97.700837 \n", "L 502.8382 97.635381 \n", "L 502.903978 97.621716 \n", "L 503.13441 98.066295 \n", "L 503.365878 97.644131 \n", "L 503.399161 97.674405 \n", "L 503.432413 97.593949 \n", "L 503.532066 97.292488 \n", "L 503.564972 97.320386 \n", "L 503.598311 97.351543 \n", "L 503.631189 97.259973 \n", "L 503.697274 97.160496 \n", "L 503.829447 96.842109 \n", "L 503.994614 96.435153 \n", "L 504.02765 96.422017 \n", "L 504.160065 96.208924 \n", "L 504.193105 96.230607 \n", "L 504.226053 96.152125 \n", "L 504.423778 95.679029 \n", "L 504.621616 95.623353 \n", "L 504.72087 95.606831 \n", "L 504.820294 95.933454 \n", "L 504.886005 95.918852 \n", "L 504.984837 95.96768 \n", "L 505.0843 96.059546 \n", "L 505.117602 96.035996 \n", "L 505.150491 96.025607 \n", "L 505.28263 95.66315 \n", "L 505.315948 95.810684 \n", "L 505.382198 95.689761 \n", "L 505.481174 95.433085 \n", "L 505.51432 95.625008 \n", "L 505.646675 95.746446 \n", "L 505.680001 95.640646 \n", "L 505.745791 95.767082 \n", "L 505.911388 95.847063 \n", "L 506.010772 95.604063 \n", "L 506.04367 95.675367 \n", "L 506.276015 95.318004 \n", "L 506.308838 95.406119 \n", "L 506.374815 95.342956 \n", "L 506.440751 95.316155 \n", "L 506.506889 95.50083 \n", "L 506.540195 95.299115 \n", "L 506.60606 95.377093 \n", "L 506.638983 95.27229 \n", "L 506.903775 94.980538 \n", "L 507.499956 93.05341 \n", "L 507.566104 92.747319 \n", "L 507.632342 92.760169 \n", "L 508.19366 91.796591 \n", "L 508.226955 91.826999 \n", "L 508.259782 91.902497 \n", "L 508.293044 91.727897 \n", "L 508.490586 91.764721 \n", "L 508.656931 91.668912 \n", "L 508.788855 91.875745 \n", "L 509.416688 90.262088 \n", "L 509.581407 90.241268 \n", "L 510.209204 91.21414 \n", "L 510.242496 91.139748 \n", "L 510.308753 90.847548 \n", "L 510.407299 90.941744 \n", "L 510.440454 91.018729 \n", "L 510.47333 90.902214 \n", "L 510.506286 90.882672 \n", "L 510.539197 91.036475 \n", "L 510.572072 90.895909 \n", "L 510.934822 89.75232 \n", "L 510.9677 89.748076 \n", "L 511.397089 88.41187 \n", "L 511.46344 88.448662 \n", "L 511.496289 88.197437 \n", "L 511.56235 88.485245 \n", "L 511.924726 87.750487 \n", "L 512.122752 87.935205 \n", "L 512.155662 88.017598 \n", "L 512.188669 87.886903 \n", "L 512.353458 87.578124 \n", "L 512.386724 87.593339 \n", "L 512.982203 88.631649 \n", "L 513.08106 88.070338 \n", "L 513.147284 88.126524 \n", "L 513.444641 88.359965 \n", "L 513.57717 88.519699 \n", "L 513.610449 88.397414 \n", "L 513.643744 88.542585 \n", "L 513.743211 88.750618 \n", "L 513.776041 88.710552 \n", "L 513.842151 88.459645 \n", "L 513.908617 88.497802 \n", "L 513.974352 88.555352 \n", "L 514.040031 88.268521 \n", "L 514.106165 88.359557 \n", "L 514.205556 88.511476 \n", "L 514.238393 88.404065 \n", "L 514.304505 88.410614 \n", "L 514.436525 88.594589 \n", "L 514.502575 88.45279 \n", "L 514.635232 88.709937 \n", "L 514.700997 88.585658 \n", "L 514.734313 88.741061 \n", "L 515.03244 89.235062 \n", "L 515.098278 89.148674 \n", "L 515.164554 89.240938 \n", "L 515.32964 89.90601 \n", "L 515.362835 89.824584 \n", "L 515.395754 90.035004 \n", "L 515.891687 91.761988 \n", "L 515.924995 91.720634 \n", "L 516.057193 91.784093 \n", "L 516.123736 91.727923 \n", "L 516.288492 92.217726 \n", "L 516.553851 92.29282 \n", "L 516.652774 92.426151 \n", "L 516.685706 92.373642 \n", "L 516.784714 92.353903 \n", "L 517.017008 92.8479 \n", "L 517.050258 92.795716 \n", "L 517.281828 93.79059 \n", "L 517.414355 94.121085 \n", "L 517.580231 94.417038 \n", "L 517.779315 95.127479 \n", "L 517.812595 95.081415 \n", "L 517.845556 95.274395 \n", "L 517.944475 95.241921 \n", "L 518.043344 95.000302 \n", "L 518.07664 95.193737 \n", "L 518.208811 95.059815 \n", "L 518.341359 94.683535 \n", "L 518.374648 94.897643 \n", "L 518.44066 94.694363 \n", "L 518.506668 94.942078 \n", "L 518.572829 94.850198 \n", "L 518.904044 93.788276 \n", "L 519.036694 94.144731 \n", "L 519.466982 95.701834 \n", "L 519.664713 95.611984 \n", "L 519.896061 95.728946 \n", "L 520.061211 96.168252 \n", "L 520.12695 96.04972 \n", "L 520.16022 96.114481 \n", "L 520.226182 96.37835 \n", "L 520.29202 96.265589 \n", "L 520.655307 95.963178 \n", "L 520.82073 95.572701 \n", "L 520.985243 95.279202 \n", "L 521.018165 95.301147 \n", "L 521.117135 94.883396 \n", "L 521.183715 94.93214 \n", "L 521.349468 95.16495 \n", "L 521.514627 94.846098 \n", "L 521.580834 94.964603 \n", "L 521.844907 93.722818 \n", "L 521.878162 93.838215 \n", "L 521.911492 93.716659 \n", "L 522.04375 93.555856 \n", "L 522.175985 94.054776 \n", "L 522.242285 93.899464 \n", "L 522.275142 93.961237 \n", "L 522.44029 94.33942 \n", "L 522.572307 94.235717 \n", "L 522.605224 94.03732 \n", "L 522.704106 94.100133 \n", "L 522.868669 94.219713 \n", "L 522.934411 94.254079 \n", "L 522.967238 94.356613 \n", "L 523.000376 94.17632 \n", "L 523.264528 93.549445 \n", "L 523.363736 93.797777 \n", "L 523.429633 93.731169 \n", "L 523.594723 93.212555 \n", "L 523.627579 93.192865 \n", "L 523.694022 92.950738 \n", "L 523.759684 93.025647 \n", "L 524.352777 91.410772 \n", "L 524.385599 91.660993 \n", "L 524.418913 91.392241 \n", "L 524.551326 91.135627 \n", "L 524.716588 91.444524 \n", "L 524.881587 91.024406 \n", "L 525.013363 90.896547 \n", "L 525.146256 91.091681 \n", "L 525.245362 91.063154 \n", "L 525.311907 91.277338 \n", "L 525.377867 91.254576 \n", "L 525.444492 91.226548 \n", "L 525.477437 91.332748 \n", "L 525.543684 91.1885 \n", "L 525.577009 91.300641 \n", "L 525.676244 91.317872 \n", "L 525.841871 91.668536 \n", "L 525.875017 91.678233 \n", "L 525.97456 91.9691 \n", "L 526.107191 91.568014 \n", "L 526.305433 91.973245 \n", "L 526.338302 91.884723 \n", "L 526.371275 92.01384 \n", "L 526.603107 92.429495 \n", "L 526.768453 91.910131 \n", "L 526.801428 92.015263 \n", "L 526.867631 91.923608 \n", "L 526.999764 92.000365 \n", "L 527.263679 92.531341 \n", "L 527.462438 91.619705 \n", "L 527.528452 91.465478 \n", "L 527.694164 91.205454 \n", "L 527.727484 91.313661 \n", "L 527.793387 91.207343 \n", "L 527.8924 91.175386 \n", "L 528.090825 90.863337 \n", "L 528.255622 91.112835 \n", "L 528.321451 90.94835 \n", "L 528.354776 91.054787 \n", "L 528.453825 90.935246 \n", "L 528.487137 90.814691 \n", "L 528.553146 90.905212 \n", "L 528.619193 90.863799 \n", "L 528.784255 91.463071 \n", "L 528.81749 91.370002 \n", "L 528.883368 91.512683 \n", "L 529.04832 91.806826 \n", "L 529.147391 91.994775 \n", "L 529.180353 91.966603 \n", "L 529.21343 91.89793 \n", "L 529.246617 91.998093 \n", "L 529.345591 92.346324 \n", "L 529.411442 92.173492 \n", "L 529.444538 92.217941 \n", "L 529.510737 92.147964 \n", "L 529.54404 92.116793 \n", "L 529.577003 92.161442 \n", "L 529.609981 92.254336 \n", "L 529.675855 92.188125 \n", "L 529.708822 92.141364 \n", "L 529.741858 92.220814 \n", "L 529.775089 92.466447 \n", "L 529.841471 92.269758 \n", "L 530.10606 93.005491 \n", "L 530.139148 92.891867 \n", "L 530.172201 92.901065 \n", "L 530.238181 93.132776 \n", "L 530.304285 93.049224 \n", "L 530.502442 92.486978 \n", "L 530.601783 92.110162 \n", "L 530.733758 91.610051 \n", "L 530.799836 91.667947 \n", "L 530.865743 91.762221 \n", "L 530.898578 91.817746 \n", "L 530.93192 91.668322 \n", "L 530.964853 91.745126 \n", "L 531.063978 91.669491 \n", "L 531.097084 91.764066 \n", "L 531.130001 91.71368 \n", "L 531.262333 91.359157 \n", "L 531.29533 91.419137 \n", "L 531.394272 91.479974 \n", "L 531.5268 91.66487 \n", "L 531.559824 91.656415 \n", "L 531.691886 91.371066 \n", "L 531.856916 91.59041 \n", "L 532.054854 91.281036 \n", "L 532.153708 91.31401 \n", "L 532.418352 90.876363 \n", "L 532.451172 90.834976 \n", "L 532.484293 90.900234 \n", "L 532.517312 90.956777 \n", "L 532.649666 90.676682 \n", "L 532.715507 90.794798 \n", "L 532.748408 90.706737 \n", "L 532.847718 90.53354 \n", "L 532.979747 89.964199 \n", "L 533.012627 90.022435 \n", "L 533.045681 90.088033 \n", "L 533.210565 89.408932 \n", "L 533.243435 89.425005 \n", "L 533.276724 89.507273 \n", "L 533.441895 88.965837 \n", "L 534.234548 86.98233 \n", "L 534.466486 86.85049 \n", "L 534.961678 86.071302 \n", "L 534.994671 86.134356 \n", "L 535.027515 86.217295 \n", "L 535.06057 86.085168 \n", "L 535.489736 85.631702 \n", "L 535.126292 86.170176 \n", "L 535.522562 85.660308 \n", "L 535.688028 86.386156 \n", "L 535.854246 86.88792 \n", "L 535.887547 86.865443 \n", "L 535.986813 86.63812 \n", "L 536.019728 86.688399 \n", "L 536.151658 86.844443 \n", "L 536.18462 86.812916 \n", "L 536.217485 86.879693 \n", "L 536.250435 86.802007 \n", "L 536.976558 85.132232 \n", "L 537.141878 84.733315 \n", "L 537.175131 84.689665 \n", "L 537.208373 84.86621 \n", "L 537.274454 84.725366 \n", "L 537.307459 84.708816 \n", "L 537.439607 84.474033 \n", "L 537.50584 84.584107 \n", "L 537.571686 84.501445 \n", "L 537.604988 84.437453 \n", "L 537.637988 84.577672 \n", "L 537.736836 84.490273 \n", "L 537.901903 84.051357 \n", "L 537.934858 84.121502 \n", "L 538.099773 83.391044 \n", "L 538.132843 83.370473 \n", "L 538.330949 82.81058 \n", "L 538.396841 82.883867 \n", "L 538.42971 82.782502 \n", "L 538.52916 82.443111 \n", "L 538.562145 82.517821 \n", "L 538.825923 83.098135 \n", "L 538.858846 83.053867 \n", "L 538.891944 83.079291 \n", "L 538.92508 82.989911 \n", "L 539.057589 82.988385 \n", "L 539.123637 83.090978 \n", "L 539.156834 82.963912 \n", "L 539.289356 82.494183 \n", "L 539.322313 82.517248 \n", "L 540.016274 81.406118 \n", "L 540.115753 81.128199 \n", "L 540.148768 81.213934 \n", "L 540.247775 81.199278 \n", "L 540.314054 81.327962 \n", "L 540.347052 81.239189 \n", "L 540.380174 81.190451 \n", "L 540.41317 81.244429 \n", "L 540.578408 81.589247 \n", "L 540.974347 81.826977 \n", "L 541.073502 81.732685 \n", "L 541.106623 81.844878 \n", "L 541.139911 81.866469 \n", "L 541.337646 82.524054 \n", "L 541.601702 83.079932 \n", "L 541.634966 83.023459 \n", "L 541.668063 83.115696 \n", "L 541.865651 83.280686 \n", "L 542.030624 83.241902 \n", "L 542.162393 83.147594 \n", "L 542.261525 83.285135 \n", "L 542.327472 83.231124 \n", "L 542.558502 82.591025 \n", "L 542.690984 82.315895 \n", "L 542.723906 82.445598 \n", "L 542.789733 82.322472 \n", "L 543.053445 81.798582 \n", "L 543.15243 81.900008 \n", "L 543.185349 82.136687 \n", "L 543.284473 82.074003 \n", "L 543.317582 82.074579 \n", "L 543.416816 82.246645 \n", "L 543.449808 82.186618 \n", "L 543.680785 82.067807 \n", "L 543.977914 81.247609 \n", "L 544.308529 80.341695 \n", "L 544.374392 80.391061 \n", "L 544.40734 80.20062 \n", "L 544.50618 80.230568 \n", "L 544.60503 80.605143 \n", "L 544.704223 80.525088 \n", "L 544.869239 80.265098 \n", "L 544.90255 80.331801 \n", "L 544.968585 80.267361 \n", "L 545.001631 80.341165 \n", "L 545.13415 80.159714 \n", "L 545.167458 80.18681 \n", "L 545.233651 80.345007 \n", "L 545.266717 80.218784 \n", "L 545.960702 78.531722 \n", "L 546.059857 78.623319 \n", "L 546.324086 79.20786 \n", "L 546.489243 78.89508 \n", "L 546.62142 78.764588 \n", "L 546.654488 78.800517 \n", "L 547.117413 77.328872 \n", "L 547.249642 77.327758 \n", "L 547.382003 76.847584 \n", "L 547.414983 76.958733 \n", "L 547.547561 77.121437 \n", "L 547.944253 76.390469 \n", "L 548.241543 75.415581 \n", "L 548.373485 75.415452 \n", "L 548.439464 75.462595 \n", "L 548.472504 75.410093 \n", "L 548.934763 74.392808 \n", "L 549.19916 73.665391 \n", "L 549.430885 72.810307 \n", "L 549.595762 72.302514 \n", "L 549.925819 72.164115 \n", "L 550.15675 71.726551 \n", "L 550.189829 71.790578 \n", "L 550.387962 71.172093 \n", "L 550.71822 70.716173 \n", "L 550.88301 70.152524 \n", "L 550.949084 70.298818 \n", "L 550.982164 70.109329 \n", "L 551.015212 70.017388 \n", "L 551.048531 70.228335 \n", "L 551.081619 70.077137 \n", "L 551.148084 70.259598 \n", "L 551.181098 70.176764 \n", "L 551.313249 70.093003 \n", "L 551.610463 69.543907 \n", "L 551.742737 69.297344 \n", "L 551.775632 69.358637 \n", "L 551.808615 69.319432 \n", "L 551.841684 69.179398 \n", "L 551.874616 69.335794 \n", "L 551.907669 69.329535 \n", "L 552.006768 69.495065 \n", "L 552.237676 69.764714 \n", "L 552.303935 69.570889 \n", "L 552.336871 69.788247 \n", "L 552.568014 70.405689 \n", "L 552.600913 70.276999 \n", "L 552.633936 70.37313 \n", "L 552.766625 70.684217 \n", "L 552.865769 70.679211 \n", "L 553.097666 71.265188 \n", "L 553.130597 71.246796 \n", "L 553.296102 70.964773 \n", "L 553.362043 71.006906 \n", "L 553.428035 71.024136 \n", "L 553.460905 70.881174 \n", "L 553.494156 70.991433 \n", "L 553.659515 71.544326 \n", "L 553.692621 71.517144 \n", "L 553.725696 71.573012 \n", "L 553.857306 71.869773 \n", "L 553.89016 71.711824 \n", "L 553.923176 71.931013 \n", "L 553.956327 71.930004 \n", "L 554.121353 71.748126 \n", "L 554.154514 71.791394 \n", "L 554.187443 71.825926 \n", "L 554.253434 71.61037 \n", "L 554.319452 71.715207 \n", "L 554.583901 71.168547 \n", "L 554.650122 71.417337 \n", "L 554.716065 71.403142 \n", "L 555.013182 71.914545 \n", "L 555.046155 71.875594 \n", "L 555.079125 71.831388 \n", "L 555.210513 72.254408 \n", "L 555.243399 72.227651 \n", "L 555.276234 72.404821 \n", "L 555.276234 72.404821 \n", "\" clip-path=\"url(#p89aeb5621e)\" style=\"fill: none; stroke: #4c72b0; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 489.214434 131.200798 \n", "L 489.214434 69.179398 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 489.214434 131.200798 \n", "L 555.276234 131.200798 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_50\">\n", "    <!-- Zoom-in -->\n", "    <g transform=\"translate(492.517524 67.474625) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-5a\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"68.505859\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"129.6875\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"190.869141\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"288.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"324.365234\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"352.148438\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_51\">\n", "    <!-- 99.576 -->\n", "    <g style=\"fill: #ff0000\" transform=\"translate(451.719122 131.12111) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-39\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"222.65625\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" x=\"286.279297\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_52\">\n", "    <!-- 99.751 -->\n", "    <g style=\"fill: #ff0000\" transform=\"translate(538.719122 61.777835) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-39\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "     <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"286.279297\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 771.448434 131.200798 \n", "L 837.510234 131.200798 \n", "L 837.510234 69.179398 \n", "L 771.448434 69.179398 \n", "L 771.448434 131.200798 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_79\">\n", "      <path d=\"M 771.84703 131.200798 \n", "L 771.84703 69.179398 \n", "\" clip-path=\"url(#paa277a8b49)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_80\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"771.84703\" y=\"131.200798\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_53\">\n", "      <!-- 2.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(762.662975 149.476993) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_81\">\n", "      <path d=\"M 834.437064 131.200798 \n", "L 834.437064 69.179398 \n", "\" clip-path=\"url(#paa277a8b49)\" style=\"fill: none; stroke: #d3d3d3; stroke-opacity: 0.3; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_82\">\n", "      <g>\n", "       <use xlink:href=\"#m96752845a8\" x=\"834.437064\" y=\"131.200798\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_54\">\n", "      <!-- 2.5 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(825.253009 149.476993) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_55\">\n", "     <!-- 1e9 -->\n", "     <g style=\"fill: #262626\" transform=\"translate(815.706 163.655227) scale(0.1155 -0.1155)\">\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-39\" x=\"125.146484\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_83\">\n", "      <defs>\n", "       <path id=\"mc771496d92\" d=\"M 0 0 \n", "L -4 0 \n", "\" style=\"stroke: #262626\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc771496d92\" x=\"771.448434\" y=\"124.255013\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_56\">\n", "      <!-- $\\mathdefault{5\\times10^{-1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(715.422934 128.64311) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-d7\" d=\"M 4488 3438 \n", "L 3059 2003 \n", "L 4488 575 \n", "L 4116 197 \n", "L 2681 1631 \n", "L 1247 197 \n", "L 878 575 \n", "L 2303 2003 \n", "L 878 3438 \n", "L 1247 3816 \n", "L 2681 2381 \n", "L 4116 3816 \n", "L 4488 3438 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(314.580078 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(373.232422 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_84\">\n", "      <g>\n", "       <use xlink:href=\"#mc771496d92\" x=\"771.448434\" y=\"75.507399\" style=\"fill: #262626; stroke: #262626\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_57\">\n", "      <!-- $\\mathdefault{6\\times10^{-1}}$ -->\n", "      <g style=\"fill: #262626\" transform=\"translate(715.422934 79.895497) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-d7\" transform=\"translate(83.105469 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.376953 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(250 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(314.580078 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(373.232422 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 771.448434 92.965944 \n", "L 771.646872 94.178223 \n", "L 771.811945 93.310444 \n", "L 771.911506 93.469622 \n", "L 771.94462 92.839513 \n", "L 772.010462 93.580388 \n", "L 772.043575 93.504607 \n", "L 772.142631 94.161493 \n", "L 772.175931 93.875108 \n", "L 772.241684 93.60352 \n", "L 772.274737 93.631534 \n", "L 772.73839 98.329996 \n", "L 772.77165 98.258182 \n", "L 772.804659 97.90616 \n", "L 772.837981 98.394871 \n", "L 773.037146 100.230356 \n", "L 773.103393 99.673775 \n", "L 773.203222 98.048994 \n", "L 773.269503 98.171149 \n", "L 773.335748 98.476779 \n", "L 773.434953 97.833581 \n", "L 773.468194 97.892263 \n", "L 773.501081 97.969603 \n", "L 773.633488 99.044179 \n", "L 773.732552 98.940271 \n", "L 773.831937 99.966807 \n", "L 773.864799 99.521041 \n", "L 773.898037 99.623106 \n", "L 773.964611 99.205221 \n", "L 773.997557 99.730194 \n", "L 774.03039 99.425438 \n", "L 774.129431 98.611456 \n", "L 774.195551 98.646854 \n", "L 774.228833 98.617964 \n", "L 774.428097 97.030748 \n", "L 774.461209 97.160854 \n", "L 774.594119 98.653223 \n", "L 774.693677 98.55476 \n", "L 774.792862 97.535354 \n", "L 774.825953 97.92998 \n", "L 774.85925 97.926332 \n", "L 774.958725 96.921622 \n", "L 775.024959 97.116616 \n", "L 775.05807 97.049657 \n", "L 775.223437 98.082338 \n", "L 775.322439 98.405148 \n", "L 775.355642 98.239718 \n", "L 775.554618 99.454274 \n", "L 775.587491 99.578931 \n", "L 775.720295 101.926759 \n", "L 775.753171 101.75837 \n", "L 775.786389 102.06202 \n", "L 775.984828 105.829049 \n", "L 776.017698 105.751488 \n", "L 776.215839 104.330545 \n", "L 776.24883 104.315251 \n", "L 776.380682 102.919647 \n", "L 776.413618 103.140687 \n", "L 776.480071 103.68187 \n", "L 776.545746 103.474002 \n", "L 776.57903 103.546175 \n", "L 776.910014 99.271364 \n", "L 776.942985 99.472407 \n", "L 777.042559 99.847968 \n", "L 777.208011 98.840279 \n", "L 777.307042 98.862058 \n", "L 777.373253 98.739186 \n", "L 777.50525 99.67328 \n", "L 777.604384 98.552277 \n", "L 777.637368 99.054638 \n", "L 777.70379 98.986145 \n", "L 777.736766 99.354597 \n", "L 777.901579 100.638582 \n", "L 777.934418 100.3521 \n", "L 778.000113 100.316244 \n", "L 778.297241 103.405501 \n", "L 778.330338 103.279887 \n", "L 778.396832 102.64449 \n", "L 778.660659 100.321129 \n", "L 778.792875 98.983755 \n", "L 778.858756 98.29011 \n", "L 778.891782 98.354124 \n", "L 778.924951 98.845725 \n", "L 778.957767 98.42761 \n", "L 779.18957 96.475756 \n", "L 779.222399 96.542358 \n", "L 779.454177 93.211927 \n", "L 779.553401 93.813733 \n", "L 779.586403 93.952296 \n", "L 779.751816 91.552038 \n", "L 779.78497 91.785902 \n", "L 779.883671 92.435972 \n", "L 779.982214 91.489636 \n", "L 780.015046 91.955384 \n", "L 780.048253 91.845649 \n", "L 780.180889 92.385112 \n", "L 780.246931 92.508149 \n", "L 780.280205 92.464148 \n", "L 780.41289 91.177589 \n", "L 780.446102 91.492088 \n", "L 780.479076 91.259149 \n", "L 780.64495 89.417585 \n", "L 781.205933 81.48886 \n", "L 781.271896 81.387765 \n", "L 781.436836 84.228684 \n", "L 781.502736 85.320823 \n", "L 781.568446 84.947747 \n", "L 781.601643 84.981891 \n", "L 781.833459 86.333263 \n", "L 781.667616 84.836902 \n", "L 781.86629 86.32249 \n", "L 782.098419 84.81207 \n", "L 782.164303 85.405755 \n", "L 782.230067 85.460162 \n", "L 782.295978 84.997387 \n", "L 782.395031 85.735069 \n", "L 782.461185 85.619898 \n", "L 782.494116 85.550006 \n", "L 782.857674 91.260449 \n", "L 782.890617 91.284401 \n", "L 783.023197 93.30722 \n", "L 783.08896 92.947966 \n", "L 783.287999 91.405438 \n", "L 783.320974 91.307863 \n", "L 783.486376 90.206216 \n", "L 783.552308 89.618102 \n", "L 783.585189 90.011786 \n", "L 783.684077 90.670542 \n", "L 783.749922 90.511246 \n", "L 783.782867 90.511318 \n", "L 783.948612 89.14883 \n", "L 783.981627 89.572217 \n", "L 784.080608 89.8901 \n", "L 784.246155 89.23978 \n", "L 784.311972 89.804629 \n", "L 784.344991 89.387725 \n", "L 784.443964 89.049129 \n", "L 784.509752 89.073337 \n", "L 784.64183 90.253333 \n", "L 784.708196 89.928847 \n", "L 784.741322 89.872703 \n", "L 784.939736 91.295167 \n", "L 784.972766 91.123184 \n", "L 785.0722 91.817948 \n", "L 785.105051 91.679313 \n", "L 785.137978 91.694043 \n", "L 785.170855 92.101153 \n", "L 785.236538 91.542544 \n", "L 785.43428 90.995399 \n", "L 785.633161 89.301746 \n", "L 785.699637 88.7069 \n", "L 785.732826 89.038722 \n", "L 785.865189 90.89724 \n", "L 785.964366 90.178076 \n", "L 786.129706 89.574576 \n", "L 786.16266 89.642851 \n", "L 786.195636 90.087357 \n", "L 786.228614 89.467287 \n", "L 786.26165 90.040541 \n", "L 786.427105 89.522346 \n", "L 786.493036 89.776506 \n", "L 786.525917 89.518851 \n", "L 786.558811 89.442359 \n", "L 786.690634 90.597665 \n", "L 786.723593 90.230182 \n", "L 787.251798 94.583053 \n", "L 787.285026 94.02107 \n", "L 787.351602 94.39496 \n", "L 787.384491 94.485273 \n", "L 787.58317 97.38327 \n", "L 787.74832 99.72469 \n", "L 787.814867 99.463604 \n", "L 788.145388 96.04233 \n", "L 788.211438 96.207481 \n", "L 788.244772 96.483387 \n", "L 788.31097 96.126276 \n", "L 788.410312 96.357428 \n", "L 788.443604 96.167932 \n", "L 788.476745 96.554605 \n", "L 788.510015 96.551857 \n", "L 788.542838 96.347884 \n", "L 788.608815 97.436816 \n", "L 788.674751 97.005822 \n", "L 788.774195 96.81536 \n", "L 788.972402 98.57616 \n", "L 789.005664 98.221213 \n", "L 789.137775 99.704632 \n", "L 789.370592 103.230508 \n", "L 789.436622 103.343358 \n", "L 789.66829 101.666782 \n", "L 789.766795 99.414667 \n", "L 789.866342 99.466151 \n", "L 789.965768 100.673718 \n", "L 789.998596 100.527382 \n", "L 790.130863 98.641766 \n", "L 790.164132 98.986261 \n", "L 790.55994 101.88771 \n", "L 790.592802 101.845375 \n", "L 790.791086 100.380674 \n", "L 790.857696 100.114826 \n", "L 791.022855 98.556984 \n", "L 791.056189 98.423227 \n", "L 791.254411 96.00272 \n", "L 791.287237 95.76833 \n", "L 791.353506 96.030841 \n", "L 791.41928 96.282874 \n", "L 791.518345 97.193468 \n", "L 791.551562 97.094359 \n", "L 791.650688 96.853725 \n", "L 791.683682 97.013213 \n", "L 791.716516 96.702634 \n", "L 792.0473 94.582004 \n", "L 792.080364 94.964089 \n", "L 792.113249 95.412406 \n", "L 792.179397 94.805239 \n", "L 792.278306 93.679175 \n", "L 792.476496 92.20278 \n", "L 792.904855 94.876147 \n", "L 793.003887 95.660861 \n", "L 793.102805 95.427118 \n", "L 793.2017 94.706252 \n", "L 793.234546 95.216895 \n", "L 793.333264 95.729617 \n", "L 793.366296 96.143241 \n", "L 793.399295 95.987922 \n", "L 793.432538 95.378659 \n", "L 793.465801 96.020433 \n", "L 793.8295 99.493031 \n", "L 793.862397 99.393812 \n", "L 793.895289 99.505584 \n", "L 793.928105 100.132712 \n", "L 793.99401 99.315167 \n", "L 794.125568 98.240347 \n", "L 794.191966 98.525262 \n", "L 794.323665 97.825907 \n", "L 794.389662 97.302572 \n", "L 794.455627 97.522795 \n", "L 794.753151 99.821676 \n", "L 794.885486 100.416603 \n", "L 794.98487 100.971853 \n", "L 795.017708 100.727015 \n", "L 795.083793 100.513751 \n", "L 795.447156 96.813933 \n", "L 795.480069 96.797827 \n", "L 795.645795 97.970987 \n", "L 795.711482 97.67897 \n", "L 795.744749 97.522754 \n", "L 795.844449 98.281035 \n", "L 795.877744 98.27716 \n", "L 795.944304 97.43741 \n", "L 796.010041 97.564183 \n", "L 796.043302 97.390416 \n", "L 796.076151 97.443477 \n", "L 796.571413 101.296037 \n", "L 796.703673 100.790036 \n", "L 797.100857 102.644359 \n", "L 797.134067 102.393903 \n", "L 797.166956 102.483091 \n", "L 797.26644 103.120047 \n", "L 797.299435 102.9859 \n", "L 797.464656 100.927342 \n", "L 797.629754 100.144326 \n", "L 797.662931 100.429908 \n", "L 797.695902 100.368765 \n", "L 797.728745 99.777777 \n", "L 797.761692 100.436643 \n", "L 797.794511 100.410827 \n", "L 797.893683 99.795349 \n", "L 797.926944 100.047022 \n", "L 798.158995 102.326015 \n", "L 798.191825 102.170006 \n", "L 798.291193 101.384301 \n", "L 798.324486 101.587236 \n", "L 798.489629 102.204862 \n", "L 798.588662 101.08155 \n", "L 798.72172 99.829734 \n", "L 798.75468 99.964769 \n", "L 798.787851 100.01903 \n", "L 798.820695 99.476806 \n", "L 798.886774 99.634765 \n", "L 799.018714 100.917916 \n", "L 799.051687 100.844548 \n", "L 799.118143 100.265307 \n", "L 799.151437 100.731752 \n", "L 799.317327 101.660432 \n", "L 799.549038 104.049038 \n", "L 799.582234 103.979409 \n", "L 799.61548 103.609033 \n", "L 799.648355 103.863508 \n", "L 799.814231 104.678557 \n", "L 799.980299 103.508693 \n", "L 800.013315 103.675107 \n", "L 800.211639 104.7493 \n", "L 800.244514 104.919047 \n", "L 800.277344 104.828429 \n", "L 800.376637 103.245272 \n", "L 800.409492 103.580623 \n", "L 800.442811 103.546624 \n", "L 800.50881 103.024833 \n", "L 800.542025 103.229586 \n", "L 800.67466 103.665379 \n", "L 800.707565 103.619673 \n", "L 800.773635 104.159778 \n", "L 800.839954 104.016934 \n", "L 801.10497 102.269971 \n", "L 801.171253 102.715044 \n", "L 801.204356 102.833964 \n", "L 801.403088 100.968676 \n", "L 801.435972 101.252942 \n", "L 801.469215 101.191024 \n", "L 801.63483 99.404035 \n", "L 801.668132 99.877464 \n", "L 801.700982 100.139926 \n", "L 801.734019 99.688025 \n", "L 801.767023 99.461469 \n", "L 801.799879 99.714476 \n", "L 801.832985 99.925197 \n", "L 801.865877 99.521543 \n", "L 802.031043 98.272804 \n", "L 802.097131 98.665439 \n", "L 802.228913 99.332922 \n", "L 802.262039 99.154536 \n", "L 802.460182 98.027262 \n", "L 802.558953 98.237875 \n", "L 802.624904 99.506843 \n", "L 802.69103 99.246465 \n", "L 802.823507 98.957418 \n", "L 803.021855 97.389498 \n", "L 803.05473 97.403468 \n", "L 803.087629 97.424608 \n", "L 803.153487 97.056588 \n", "L 803.252165 97.10551 \n", "L 803.284981 97.136989 \n", "L 803.318021 96.944133 \n", "L 803.351135 96.952121 \n", "L 803.517386 98.185419 \n", "L 803.583468 98.480694 \n", "L 803.748627 100.083207 \n", "L 803.8806 99.15089 \n", "L 804.045992 97.424023 \n", "L 804.078907 97.74886 \n", "L 804.211669 97.480944 \n", "L 804.244489 97.672221 \n", "L 804.27775 97.43462 \n", "L 804.310909 97.517563 \n", "L 804.377052 96.656273 \n", "L 804.509142 96.721633 \n", "L 804.542227 96.778873 \n", "L 804.641461 95.598476 \n", "L 804.70717 95.945461 \n", "L 804.740102 95.998396 \n", "L 804.773138 95.883228 \n", "L 804.806307 95.916974 \n", "L 804.938106 94.487239 \n", "L 805.036923 94.701661 \n", "L 805.102669 94.598856 \n", "L 805.234376 93.676925 \n", "L 805.300346 94.144595 \n", "L 805.333316 93.793474 \n", "L 805.366266 93.721323 \n", "L 805.399165 93.899477 \n", "L 805.663633 95.337114 \n", "L 805.696497 95.261579 \n", "L 805.795521 96.649469 \n", "L 805.828723 96.56124 \n", "L 806.026673 95.211723 \n", "L 806.059498 95.283332 \n", "L 806.092385 94.98019 \n", "L 806.158143 95.349168 \n", "L 806.224601 95.843037 \n", "L 806.257708 95.560543 \n", "L 806.356446 94.678867 \n", "L 806.389392 94.91591 \n", "L 806.52105 97.08198 \n", "L 806.652913 98.45493 \n", "L 807.082556 99.616894 \n", "L 806.719201 98.378408 \n", "L 807.115587 99.417568 \n", "L 807.148529 99.36123 \n", "L 807.31397 96.432308 \n", "L 807.380256 96.900004 \n", "L 807.413192 96.835871 \n", "L 807.611867 94.540517 \n", "L 807.744581 93.546597 \n", "L 807.777684 93.604775 \n", "L 807.87726 94.387586 \n", "L 807.910244 93.858468 \n", "L 808.009708 92.952244 \n", "L 808.042598 93.301805 \n", "L 808.075871 93.255856 \n", "L 808.109017 93.335004 \n", "L 808.4403 95.668834 \n", "L 808.473135 95.470464 \n", "L 808.539433 95.508008 \n", "L 808.737544 96.681582 \n", "L 808.770481 96.651977 \n", "L 808.969436 98.075798 \n", "L 809.233764 96.713705 \n", "L 809.266589 97.333545 \n", "L 809.36558 98.815622 \n", "L 809.431592 98.344892 \n", "L 809.464666 98.341171 \n", "L 809.530653 97.850235 \n", "L 809.563811 97.998886 \n", "L 809.663532 98.66313 \n", "L 809.696438 98.109856 \n", "L 809.762452 98.725581 \n", "L 810.027387 101.200896 \n", "L 810.1264 100.110076 \n", "L 810.159293 100.229996 \n", "L 810.225328 100.720276 \n", "L 810.29152 100.676838 \n", "L 810.489622 100.093388 \n", "L 810.787146 103.103614 \n", "L 810.985387 101.366432 \n", "L 811.117368 101.41011 \n", "L 811.183569 102.129259 \n", "L 811.216486 101.850179 \n", "L 811.480617 99.815427 \n", "L 811.513788 99.843508 \n", "L 811.579591 100.321435 \n", "L 811.612577 100.137341 \n", "L 811.77804 99.533657 \n", "L 811.811003 99.729741 \n", "L 811.843981 99.645611 \n", "L 812.075471 97.651191 \n", "L 812.141639 97.915431 \n", "L 812.174641 98.509177 \n", "L 812.240725 98.024095 \n", "L 812.273866 98.131934 \n", "L 812.306932 97.932165 \n", "L 812.439184 96.388125 \n", "L 812.472181 96.496113 \n", "L 812.80274 98.893057 \n", "L 812.835783 98.892345 \n", "L 812.868827 99.576206 \n", "L 812.934725 99.292022 \n", "L 813.099743 98.235301 \n", "L 813.16592 99.51662 \n", "L 813.2647 99.290335 \n", "L 813.297978 99.190533 \n", "L 813.331084 99.206935 \n", "L 813.496333 100.389443 \n", "L 813.628272 99.123987 \n", "L 813.661309 99.166291 \n", "L 813.958876 100.430252 \n", "L 813.991857 100.159392 \n", "L 814.05782 100.407223 \n", "L 814.156838 100.24597 \n", "L 814.354696 101.914368 \n", "L 814.387708 101.962415 \n", "L 814.42074 102.265583 \n", "L 814.453741 101.632779 \n", "L 814.486769 101.728522 \n", "L 814.519829 101.494845 \n", "L 814.586281 101.688055 \n", "L 814.619434 101.725887 \n", "L 814.784641 99.842698 \n", "L 814.91658 98.552976 \n", "L 814.949507 98.376277 \n", "L 814.982408 98.58291 \n", "L 815.015747 98.629349 \n", "L 815.048847 98.59451 \n", "L 815.312655 100.528055 \n", "L 815.411639 101.409227 \n", "L 815.576829 104.221862 \n", "L 815.642586 104.188135 \n", "L 815.675895 104.488791 \n", "L 815.708979 104.415128 \n", "L 815.840874 103.380498 \n", "L 815.873814 104.091797 \n", "L 815.940182 103.572856 \n", "L 815.973137 103.61744 \n", "L 816.204408 105.341744 \n", "L 816.369452 106.479583 \n", "L 816.435592 106.286029 \n", "L 816.468548 105.719331 \n", "L 816.535016 105.842774 \n", "L 816.600985 106.633729 \n", "L 816.66718 106.311219 \n", "L 816.700486 106.049061 \n", "L 816.766497 106.090958 \n", "L 817.031052 108.799396 \n", "L 817.064044 108.905095 \n", "L 817.097008 108.297557 \n", "L 817.162781 108.747485 \n", "L 817.492067 112.028695 \n", "L 817.524996 111.975854 \n", "L 817.558119 111.71274 \n", "L 817.624405 111.850868 \n", "L 817.657232 111.871857 \n", "L 817.723736 111.54675 \n", "L 817.789427 112.197461 \n", "L 817.822714 111.94386 \n", "L 817.855918 111.326859 \n", "L 817.922028 111.689559 \n", "L 817.955309 111.686312 \n", "L 817.988657 111.94201 \n", "L 818.021935 111.564847 \n", "L 818.286717 110.480905 \n", "L 818.5174 112.063979 \n", "L 818.550403 111.707998 \n", "L 818.649094 111.450477 \n", "L 818.781611 112.349135 \n", "L 818.946562 111.766657 \n", "L 818.979492 111.846878 \n", "L 819.375878 116.627113 \n", "L 819.442373 116.52407 \n", "L 819.541459 116.936341 \n", "L 819.805686 119.973037 \n", "L 819.937932 119.284659 \n", "L 819.970836 119.392122 \n", "L 820.201729 121.312737 \n", "L 820.234814 121.024581 \n", "L 820.267866 121.301255 \n", "L 820.399769 122.709612 \n", "L 820.531875 122.051597 \n", "L 820.729978 123.818596 \n", "L 820.796145 123.478357 \n", "L 820.829037 123.703835 \n", "L 820.862004 123.31915 \n", "L 820.895336 123.327266 \n", "L 820.961229 123.863003 \n", "L 821.02705 123.738961 \n", "L 821.059923 123.631653 \n", "L 821.258294 124.735068 \n", "L 821.291589 124.436571 \n", "L 821.324707 124.862717 \n", "L 821.357637 125.519126 \n", "L 821.457138 125.412621 \n", "L 821.556313 125.960065 \n", "L 821.589374 125.673882 \n", "L 821.622272 125.765743 \n", "L 821.655502 125.674937 \n", "L 821.688539 125.507977 \n", "L 821.919885 127.64247 \n", "L 821.952849 127.222212 \n", "L 822.051788 127.249027 \n", "L 822.118018 127.071584 \n", "L 822.349753 125.800936 \n", "L 822.382768 126.076705 \n", "L 822.415826 125.624651 \n", "L 822.448742 124.586958 \n", "L 822.514975 125.143914 \n", "L 822.548054 125.386882 \n", "L 822.87839 120.302695 \n", "L 822.911211 120.226355 \n", "L 822.944158 120.549216 \n", "L 822.977283 120.421754 \n", "L 823.010389 120.004848 \n", "L 823.076326 120.431207 \n", "L 823.142462 120.453825 \n", "L 823.274557 121.307669 \n", "L 823.307502 120.974981 \n", "L 823.373911 121.288002 \n", "L 823.4069 121.36604 \n", "L 823.736558 116.894521 \n", "L 823.902063 116.171555 \n", "L 823.967903 115.409688 \n", "L 824.000893 115.9384 \n", "L 824.198683 117.755716 \n", "L 824.231663 117.266792 \n", "L 824.330614 117.387267 \n", "L 824.429229 115.908597 \n", "L 824.495525 116.091538 \n", "L 824.561472 116.841877 \n", "L 824.594422 115.989256 \n", "L 824.627417 115.780723 \n", "L 824.693633 115.824045 \n", "L 824.891691 118.488362 \n", "L 824.957906 118.79386 \n", "L 825.023733 119.440321 \n", "L 825.056693 119.133094 \n", "L 825.155484 118.615349 \n", "L 825.188691 118.769508 \n", "L 825.221619 118.713592 \n", "L 825.320395 117.111472 \n", "L 825.35338 117.732976 \n", "L 825.419349 117.571788 \n", "L 825.551582 116.443841 \n", "L 825.584621 116.744138 \n", "L 825.650816 116.494368 \n", "L 825.881723 114.572066 \n", "L 826.01375 115.091301 \n", "L 826.04684 115.022852 \n", "L 826.079821 115.101884 \n", "L 826.211914 115.923633 \n", "L 826.3771 114.314444 \n", "L 826.410099 114.393978 \n", "L 826.509541 113.757181 \n", "L 826.542529 114.032048 \n", "L 826.74018 115.250416 \n", "L 826.77312 115.047519 \n", "L 826.805994 115.306107 \n", "L 826.83903 115.392101 \n", "L 827.037016 113.775272 \n", "L 827.103239 113.90078 \n", "L 827.13655 114.1442 \n", "L 827.169527 113.68837 \n", "L 827.202585 113.757636 \n", "L 827.53378 110.097831 \n", "L 827.698619 107.495105 \n", "L 827.731552 107.537299 \n", "L 827.797876 108.390614 \n", "L 827.863846 108.027837 \n", "L 827.963355 106.682515 \n", "L 827.996449 106.788415 \n", "L 828.029461 107.025617 \n", "L 828.062394 106.344981 \n", "L 828.161666 106.494677 \n", "L 828.194702 106.622902 \n", "L 828.227619 106.402451 \n", "L 828.360155 105.940531 \n", "L 828.393078 106.231136 \n", "L 828.426274 106.411256 \n", "L 828.690224 103.438849 \n", "L 828.723243 103.483849 \n", "L 828.789507 104.171696 \n", "L 828.85542 103.906927 \n", "L 828.888488 103.800459 \n", "L 828.954783 104.477737 \n", "L 828.987807 104.225704 \n", "L 829.186397 103.323801 \n", "L 829.21935 103.349 \n", "L 829.285449 103.673672 \n", "L 829.351413 103.637635 \n", "L 829.417695 103.481622 \n", "L 829.549962 104.642218 \n", "L 829.582886 104.225519 \n", "L 829.682329 105.237321 \n", "L 829.847623 106.34842 \n", "L 829.880692 106.253758 \n", "L 829.946868 106.998432 \n", "L 830.013028 106.952116 \n", "L 830.078934 106.174341 \n", "L 830.178253 106.2067 \n", "L 830.211378 106.14024 \n", "L 830.310444 105.59391 \n", "L 830.409387 105.981916 \n", "L 830.475543 106.08771 \n", "L 830.541484 105.391969 \n", "L 830.706504 108.359525 \n", "L 830.80565 107.125928 \n", "L 830.838758 107.872344 \n", "L 831.135893 111.397575 \n", "L 831.201736 111.152283 \n", "L 831.234926 111.227871 \n", "L 831.367096 111.810945 \n", "L 831.400165 112.026581 \n", "L 831.466306 111.860679 \n", "L 831.499344 111.715999 \n", "L 831.532462 111.898185 \n", "L 831.598636 112.44379 \n", "L 831.631744 112.34781 \n", "L 831.763793 111.72358 \n", "L 831.829762 111.914275 \n", "L 831.994571 113.776927 \n", "L 832.027594 113.683584 \n", "L 832.060806 113.796651 \n", "L 832.093735 112.957026 \n", "L 832.159819 113.608365 \n", "L 832.192657 114.080558 \n", "L 832.291806 114.068372 \n", "L 832.456742 112.791613 \n", "L 832.489783 113.105572 \n", "L 832.522862 112.53999 \n", "L 833.382084 103.176677 \n", "L 833.415098 103.875173 \n", "L 833.514091 103.850229 \n", "L 833.547249 103.877663 \n", "L 833.712193 104.78703 \n", "L 833.811367 105.337015 \n", "L 833.844463 105.132214 \n", "L 834.075684 103.954851 \n", "L 834.108616 103.919992 \n", "L 834.141669 103.71734 \n", "L 834.174738 104.104423 \n", "L 834.273932 104.585342 \n", "L 834.306826 104.565502 \n", "L 834.37283 104.431513 \n", "L 834.405774 104.039578 \n", "L 834.471676 104.300534 \n", "L 834.603799 105.185876 \n", "L 834.636945 105.061464 \n", "L 834.670142 104.868687 \n", "L 834.703005 104.960777 \n", "L 834.802014 106.157769 \n", "L 834.834913 105.9444 \n", "L 835.099769 103.941553 \n", "L 835.298611 105.455282 \n", "L 835.596043 107.06203 \n", "L 835.728156 108.515275 \n", "L 835.761183 108.105415 \n", "L 835.959696 109.221775 \n", "L 835.992565 109.148138 \n", "L 836.12416 107.901254 \n", "L 836.157176 108.092863 \n", "L 836.190327 108.559849 \n", "L 836.223235 107.784764 \n", "L 836.256294 107.228381 \n", "L 836.322211 107.454199 \n", "L 836.355353 107.442927 \n", "L 836.388514 108.010675 \n", "L 836.487434 107.973394 \n", "L 836.52045 107.946253 \n", "L 836.652315 108.509816 \n", "L 836.685382 108.290198 \n", "L 836.751512 108.4167 \n", "L 836.850975 109.786483 \n", "L 836.884122 109.529439 \n", "L 836.91715 109.380476 \n", "L 837.148185 112.49002 \n", "L 837.181148 112.37865 \n", "L 837.247182 112.506529 \n", "L 837.510234 115.294912 \n", "L 837.510234 115.294912 \n", "\" clip-path=\"url(#paa277a8b49)\" style=\"fill: none; stroke: #c44e52; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 771.448434 94.305776 \n", "L 771.547796 92.882043 \n", "L 771.61393 93.23413 \n", "L 771.679699 93.413851 \n", "L 771.712897 93.786931 \n", "L 771.778648 93.481898 \n", "L 772.076688 91.506626 \n", "L 772.307626 88.792851 \n", "L 772.373556 88.916192 \n", "L 772.57226 87.415437 \n", "L 772.605395 87.459636 \n", "L 772.871259 89.584923 \n", "L 772.904348 89.654366 \n", "L 773.037146 90.689763 \n", "L 773.070404 90.615223 \n", "L 773.203222 89.726054 \n", "L 773.269503 89.627038 \n", "L 773.335748 88.915103 \n", "L 773.369086 89.132061 \n", "L 773.434953 88.047169 \n", "L 773.468194 88.672946 \n", "L 773.765459 94.004095 \n", "L 773.798743 93.999304 \n", "L 773.964611 94.812025 \n", "L 773.997557 94.811003 \n", "L 774.162293 93.646208 \n", "L 774.228833 92.913608 \n", "L 774.295291 93.201981 \n", "L 774.594119 90.716522 \n", "L 774.89242 89.661769 \n", "L 774.925422 89.740267 \n", "L 774.991965 89.680143 \n", "L 775.091393 90.385706 \n", "L 775.12428 90.380311 \n", "L 775.521383 92.212114 \n", "L 775.587491 92.024516 \n", "L 775.654006 91.550646 \n", "L 775.951792 88.624713 \n", "L 775.984828 88.578147 \n", "L 776.017698 88.772535 \n", "L 776.050869 88.525064 \n", "L 776.24883 85.701122 \n", "L 776.446781 83.240496 \n", "L 776.512923 83.265588 \n", "L 776.777422 82.155844 \n", "L 776.843508 81.985935 \n", "L 776.876721 82.01187 \n", "L 776.910014 82.132189 \n", "L 777.075733 80.908363 \n", "L 777.108912 80.904045 \n", "L 777.307042 79.332756 \n", "L 777.340332 79.346668 \n", "L 777.373253 79.581696 \n", "L 777.406326 79.224083 \n", "L 777.50525 78.689404 \n", "L 777.538488 78.865787 \n", "L 777.70379 79.989964 \n", "L 777.736766 79.991557 \n", "L 777.96725 79.068663 \n", "L 778.000113 79.624717 \n", "L 778.065979 79.420578 \n", "L 778.198468 79.102164 \n", "L 778.297241 79.856974 \n", "L 778.396832 79.7678 \n", "L 778.561736 78.94644 \n", "L 778.594761 78.974525 \n", "L 778.627722 79.319173 \n", "L 778.660659 79.267923 \n", "L 778.693701 78.71063 \n", "L 778.759531 79.082409 \n", "L 778.792875 79.233721 \n", "L 778.825795 79.187447 \n", "L 779.156506 76.915139 \n", "L 779.388204 76.171184 \n", "L 779.421179 76.437764 \n", "L 779.454177 76.169234 \n", "L 779.718641 73.144401 \n", "L 779.78497 73.541023 \n", "L 779.81779 73.500742 \n", "L 779.883671 72.946146 \n", "L 779.916519 73.293508 \n", "L 779.949376 73.257702 \n", "L 780.246931 74.826019 \n", "L 780.280205 74.633482 \n", "L 780.313486 74.238693 \n", "L 780.379881 74.381799 \n", "L 780.446102 75.157075 \n", "L 780.512145 74.876571 \n", "L 780.64495 74.078114 \n", "L 780.677902 74.108819 \n", "L 780.743693 73.862271 \n", "L 780.809818 73.402754 \n", "L 780.875671 73.476263 \n", "L 781.041101 71.998552 \n", "L 781.17283 72.109095 \n", "L 781.502736 75.744769 \n", "L 781.833459 78.945513 \n", "L 781.899526 79.564037 \n", "L 781.96607 79.499531 \n", "L 782.164303 77.862024 \n", "L 782.362091 78.604636 \n", "L 782.659262 81.851505 \n", "L 782.758664 82.510127 \n", "L 782.791896 82.138869 \n", "L 782.824761 81.768747 \n", "L 782.890617 81.939101 \n", "L 783.023197 82.271714 \n", "L 783.056027 82.206631 \n", "L 783.420055 77.762873 \n", "L 783.486376 77.51496 \n", "L 783.519409 77.628959 \n", "L 783.585189 77.929043 \n", "L 783.618242 77.642511 \n", "L 783.65106 77.601755 \n", "L 783.882241 75.874979 \n", "L 783.915277 75.989149 \n", "L 783.948612 75.899148 \n", "L 783.981627 76.05756 \n", "L 784.014697 75.35589 \n", "L 784.080608 76.167842 \n", "L 784.113428 76.279204 \n", "L 784.311972 75.389589 \n", "L 784.377972 75.053262 \n", "L 784.443964 75.260625 \n", "L 785.005906 80.767371 \n", "L 785.039177 80.417257 \n", "L 785.0722 80.714658 \n", "L 785.105051 80.942832 \n", "L 785.137978 80.70461 \n", "L 785.170855 80.488162 \n", "L 785.236538 80.5829 \n", "L 785.269436 80.619821 \n", "L 785.36841 80.312715 \n", "L 785.699637 82.815207 \n", "L 785.732826 82.610003 \n", "L 785.798972 82.325003 \n", "L 785.865189 82.194676 \n", "L 785.898399 81.679961 \n", "L 785.964366 81.865955 \n", "L 786.063447 82.12802 \n", "L 786.228614 82.908245 \n", "L 786.26165 82.454025 \n", "L 786.294936 82.956108 \n", "L 786.361041 83.308668 \n", "L 786.427105 83.092342 \n", "L 786.657778 81.973486 \n", "L 786.789829 83.399141 \n", "L 786.822715 83.353505 \n", "L 786.888673 83.211983 \n", "L 787.251798 87.405545 \n", "L 787.285026 87.320219 \n", "L 787.3183 87.395572 \n", "L 787.549948 89.933455 \n", "L 787.979791 94.296261 \n", "L 788.045987 94.242686 \n", "L 788.079226 94.088735 \n", "L 788.145388 94.790163 \n", "L 788.211438 94.702701 \n", "L 788.377109 93.69113 \n", "L 788.410312 93.784975 \n", "L 788.774195 95.726783 \n", "L 789.436622 101.351096 \n", "L 789.601911 100.636924 \n", "L 789.66829 100.161838 \n", "L 789.833132 98.77645 \n", "L 789.866342 98.679819 \n", "L 789.899349 98.954639 \n", "L 789.965768 98.629104 \n", "L 790.197032 97.011222 \n", "L 790.22994 97.089526 \n", "L 790.262827 97.394811 \n", "L 790.296109 96.87359 \n", "L 790.329045 96.816932 \n", "L 790.592802 100.226056 \n", "L 790.857696 98.485597 \n", "L 790.890931 98.676447 \n", "L 790.989985 98.332506 \n", "L 791.022855 98.533871 \n", "L 791.122272 99.138872 \n", "L 791.15512 99.001077 \n", "L 791.287237 98.477922 \n", "L 791.32059 98.40495 \n", "L 791.584426 101.773042 \n", "L 791.617759 101.962449 \n", "L 791.683682 101.721851 \n", "L 791.716516 101.930477 \n", "L 791.749581 101.867329 \n", "L 791.848632 101.095755 \n", "L 791.881718 101.405486 \n", "L 791.915012 101.303189 \n", "L 791.947923 101.540601 \n", "L 792.344094 105.970631 \n", "L 792.641299 107.285242 \n", "L 792.806072 105.961168 \n", "L 792.838987 106.184799 \n", "L 792.871952 106.300617 \n", "L 792.904855 106.004116 \n", "L 792.971051 106.138486 \n", "L 793.036956 106.99253 \n", "L 793.102805 106.799977 \n", "L 793.135913 107.02863 \n", "L 793.234546 106.980385 \n", "L 793.300276 107.092567 \n", "L 793.366296 106.844794 \n", "L 793.531948 107.857903 \n", "L 793.564848 107.561652 \n", "L 793.597856 107.694112 \n", "L 793.631089 108.192296 \n", "L 793.664361 107.828717 \n", "L 793.862397 105.244234 \n", "L 793.895289 105.432491 \n", "L 794.356752 107.655275 \n", "L 794.455627 107.592565 \n", "L 794.587458 107.118823 \n", "L 794.686902 107.873474 \n", "L 794.819611 108.537765 \n", "L 794.852482 108.278341 \n", "L 794.885486 108.827956 \n", "L 794.98487 107.910651 \n", "L 795.017708 107.629685 \n", "L 795.05096 107.907267 \n", "L 795.216203 108.74645 \n", "L 795.281946 108.436613 \n", "L 795.447156 107.311929 \n", "L 795.512965 107.005772 \n", "L 795.546276 107.303044 \n", "L 795.579457 107.416194 \n", "L 795.844449 104.403255 \n", "L 795.911008 103.895746 \n", "L 796.010041 103.027948 \n", "L 796.043302 103.182109 \n", "L 796.142617 103.440192 \n", "L 796.175529 103.277269 \n", "L 796.208352 103.226443 \n", "L 796.340165 104.130348 \n", "L 796.406358 103.936077 \n", "L 796.472393 104.224066 \n", "L 796.703673 105.891064 \n", "L 796.736575 105.841791 \n", "L 796.769736 105.655041 \n", "L 796.8029 105.827124 \n", "L 796.836073 105.934689 \n", "L 796.869232 105.782929 \n", "L 797.100857 104.87417 \n", "L 797.134067 104.982551 \n", "L 797.332278 104.006542 \n", "L 797.56364 105.048393 \n", "L 797.596835 105.056369 \n", "L 797.629754 105.273302 \n", "L 797.695902 105.246236 \n", "L 797.827679 104.389741 \n", "L 797.893683 104.409368 \n", "L 797.959862 104.594256 \n", "L 797.993101 104.432777 \n", "L 798.026178 104.373616 \n", "L 798.092688 104.473627 \n", "L 798.158995 104.383718 \n", "L 798.225027 104.667061 \n", "L 798.258315 104.550583 \n", "L 798.952588 99.892543 \n", "L 799.151437 101.157053 \n", "L 799.184579 101.246578 \n", "L 799.217808 101.145816 \n", "L 799.284258 100.826724 \n", "L 799.383846 99.761784 \n", "L 799.416986 99.964188 \n", "L 799.582234 101.104068 \n", "L 799.61548 101.101512 \n", "L 799.814231 102.137559 \n", "L 799.880651 101.898234 \n", "L 800.112637 105.005387 \n", "L 800.145509 104.904583 \n", "L 800.178475 105.047234 \n", "L 800.211639 104.840894 \n", "L 800.277344 104.047472 \n", "L 800.31064 104.512146 \n", "L 800.343631 104.64679 \n", "L 800.376637 104.32444 \n", "L 800.409492 104.498056 \n", "L 800.475641 104.387329 \n", "L 800.50881 104.673744 \n", "L 800.575359 104.349533 \n", "L 800.608648 104.318337 \n", "L 800.641554 104.41854 \n", "L 800.972181 106.18811 \n", "L 801.237425 108.640814 \n", "L 801.337095 108.6342 \n", "L 801.469215 107.443974 \n", "L 801.535588 107.67803 \n", "L 801.63483 107.946604 \n", "L 801.898713 106.250903 \n", "L 801.931995 106.583244 \n", "L 802.097131 108.036974 \n", "L 802.130061 107.816412 \n", "L 802.328049 107.229696 \n", "L 802.196041 107.917143 \n", "L 802.39422 107.276079 \n", "L 802.427326 107.491549 \n", "L 802.493069 107.193431 \n", "L 802.724336 108.881609 \n", "L 802.955574 111.264826 \n", "L 802.988543 110.978194 \n", "L 803.021855 111.328474 \n", "L 803.186324 112.357013 \n", "L 803.252165 112.808097 \n", "L 803.284981 112.625581 \n", "L 803.417715 112.178983 \n", "L 803.517386 112.581726 \n", "L 803.550589 112.324347 \n", "L 803.583468 112.837601 \n", "L 803.649603 113.345418 \n", "L 803.781596 114.466756 \n", "L 803.814834 114.026813 \n", "L 803.847779 113.987486 \n", "L 803.979867 115.249373 \n", "L 804.045992 115.073923 \n", "L 804.145492 113.680145 \n", "L 804.409985 109.12719 \n", "L 804.443084 109.291623 \n", "L 804.509142 109.143442 \n", "L 804.608164 107.656354 \n", "L 804.67429 107.954089 \n", "L 804.806307 106.864065 \n", "L 804.872087 106.883386 \n", "L 804.938106 107.379828 \n", "L 805.004005 107.350909 \n", "L 805.036923 107.180761 \n", "L 805.069739 107.327478 \n", "L 805.432207 109.200307 \n", "L 805.465246 109.082372 \n", "L 805.498528 109.098358 \n", "L 805.597736 109.325077 \n", "L 805.729786 108.670953 \n", "L 805.762692 108.742824 \n", "L 805.795521 108.888319 \n", "L 805.828723 108.601379 \n", "L 805.894851 108.67214 \n", "L 806.092385 109.947282 \n", "L 806.125292 109.739482 \n", "L 806.158143 109.945581 \n", "L 806.257708 110.25235 \n", "L 806.290634 110.219469 \n", "L 806.389392 110.141907 \n", "L 806.48822 108.363 \n", "L 806.553929 109.110737 \n", "L 806.619599 108.982385 \n", "L 806.884598 111.084168 \n", "L 807.049225 110.125254 \n", "L 807.115587 110.26508 \n", "L 807.148529 110.112414 \n", "L 807.413192 107.928752 \n", "L 807.545907 107.674394 \n", "L 807.578841 108.439837 \n", "L 807.645209 108.333035 \n", "L 807.910244 105.092287 \n", "L 807.976731 105.213798 \n", "L 808.042598 105.216909 \n", "L 808.075871 105.133211 \n", "L 808.109017 105.226599 \n", "L 808.175663 105.641642 \n", "L 808.241744 105.438606 \n", "L 808.374088 104.476892 \n", "L 808.407304 104.658775 \n", "L 808.4403 104.583694 \n", "L 808.473135 104.765881 \n", "L 808.539433 105.665133 \n", "L 808.605275 105.590825 \n", "L 808.803789 106.490307 \n", "L 808.903255 106.092884 \n", "L 808.936507 106.31346 \n", "L 808.969436 105.924682 \n", "L 809.134698 104.755468 \n", "L 809.167681 105.072291 \n", "L 809.299642 105.73474 \n", "L 809.332628 105.696148 \n", "L 809.596942 104.439138 \n", "L 809.630262 104.324341 \n", "L 809.663532 104.468674 \n", "L 810.060706 107.545988 \n", "L 810.192354 108.113816 \n", "L 810.225328 108.102709 \n", "L 810.258433 108.231992 \n", "L 810.29152 108.018293 \n", "L 810.324825 107.679242 \n", "L 810.357788 107.867814 \n", "L 810.489622 108.442393 \n", "L 810.522503 108.435813 \n", "L 810.555451 108.638486 \n", "L 810.588776 108.16088 \n", "L 810.654529 108.263256 \n", "L 810.787146 107.154339 \n", "L 810.918891 105.567026 \n", "L 810.95212 105.57847 \n", "L 810.985387 105.510826 \n", "L 811.216486 103.675091 \n", "L 811.249468 103.640856 \n", "L 811.414353 102.387958 \n", "L 811.480617 102.443823 \n", "L 811.513788 102.047734 \n", "L 811.546751 102.564925 \n", "L 811.645442 103.089182 \n", "L 811.678538 102.801231 \n", "L 811.711727 102.7194 \n", "L 811.811003 102.953621 \n", "L 812.009089 101.590828 \n", "L 812.042355 101.904627 \n", "L 812.075471 102.517802 \n", "L 812.141639 102.139942 \n", "L 812.207536 102.237555 \n", "L 812.240725 101.91128 \n", "L 812.306932 102.11397 \n", "L 812.34006 102.203434 \n", "L 812.868827 109.649368 \n", "L 812.901881 109.587945 \n", "L 812.934725 109.500085 \n", "L 813.033836 109.846219 \n", "L 813.2647 108.652841 \n", "L 813.297978 108.939522 \n", "L 813.397052 108.502753 \n", "L 813.430238 108.540914 \n", "L 813.694654 105.563017 \n", "L 814.123888 101.590131 \n", "L 814.354696 100.104217 \n", "L 814.42074 99.630289 \n", "L 814.586281 98.627044 \n", "L 814.652352 98.498985 \n", "L 814.718293 98.916381 \n", "L 814.751312 99.64012 \n", "L 814.850406 99.580393 \n", "L 814.883666 99.602615 \n", "L 814.91658 99.435994 \n", "L 814.949507 99.7018 \n", "L 815.015747 99.837786 \n", "L 815.048847 99.688244 \n", "L 815.081718 99.56678 \n", "L 815.114748 99.638399 \n", "L 815.213747 100.465386 \n", "L 815.312655 100.73845 \n", "L 815.345812 100.637651 \n", "L 815.378767 100.678525 \n", "L 815.642586 103.261847 \n", "L 815.675895 103.055979 \n", "L 815.708979 103.190874 \n", "L 815.741958 103.064805 \n", "L 815.774949 102.989802 \n", "L 815.940182 104.444531 \n", "L 816.270696 106.367221 \n", "L 816.468548 105.571804 \n", "L 816.535016 105.835584 \n", "L 816.567865 105.79451 \n", "L 816.66718 105.368006 \n", "L 816.700486 105.562736 \n", "L 816.733333 105.759786 \n", "L 816.766497 105.376009 \n", "L 816.799517 105.533284 \n", "L 817.064044 103.438608 \n", "L 817.129851 103.824412 \n", "L 817.228671 104.036202 \n", "L 817.39329 105.23807 \n", "L 817.42615 105.302156 \n", "L 817.624405 103.812147 \n", "L 817.789427 102.873262 \n", "L 817.822714 102.223587 \n", "L 817.88883 102.652437 \n", "L 817.922028 102.82276 \n", "L 817.955309 102.682765 \n", "L 818.220813 99.384592 \n", "L 818.286717 99.638495 \n", "L 818.385658 99.762897 \n", "L 818.5174 100.145973 \n", "L 818.583331 100.198997 \n", "L 818.68231 99.165669 \n", "L 818.781611 98.616228 \n", "L 818.814575 98.760515 \n", "L 819.045631 100.222786 \n", "L 819.177369 101.662709 \n", "L 819.210558 101.61115 \n", "L 819.409131 102.69098 \n", "L 819.772739 107.450291 \n", "L 819.805686 107.361888 \n", "L 819.871988 107.269896 \n", "L 819.937932 107.512451 \n", "L 820.069924 106.880373 \n", "L 820.201729 107.210953 \n", "L 820.267866 107.240879 \n", "L 820.300822 106.713516 \n", "L 820.466002 105.706591 \n", "L 820.564949 106.924864 \n", "L 820.597987 106.867396 \n", "L 820.928308 104.482254 \n", "L 820.994229 104.833681 \n", "L 821.059923 104.565778 \n", "L 821.092846 104.699366 \n", "L 821.125944 104.462409 \n", "L 821.291589 103.674595 \n", "L 821.390834 103.884853 \n", "L 821.423962 104.25463 \n", "L 821.490285 103.75033 \n", "L 821.523356 104.109438 \n", "L 821.556313 103.838687 \n", "L 821.688539 103.062185 \n", "L 821.820805 104.642265 \n", "L 821.919885 104.205552 \n", "L 821.952849 104.10254 \n", "L 821.985838 104.295741 \n", "L 822.415826 109.469765 \n", "L 822.581052 109.356019 \n", "L 822.614174 109.356743 \n", "L 822.713168 110.164342 \n", "L 822.746193 109.711322 \n", "L 822.779159 109.578648 \n", "L 822.911211 110.240183 \n", "L 822.944158 110.070973 \n", "L 823.043399 110.910295 \n", "L 823.274557 109.114083 \n", "L 823.307502 108.707175 \n", "L 823.340623 108.94372 \n", "L 823.4069 109.317795 \n", "L 823.439838 108.980162 \n", "L 823.637689 109.639947 \n", "L 823.736558 109.213321 \n", "L 823.769588 109.501514 \n", "L 823.835702 109.441002 \n", "L 823.868966 109.869596 \n", "L 823.902063 109.61197 \n", "L 823.934929 109.750213 \n", "L 824.033717 110.221335 \n", "L 824.099651 109.428631 \n", "L 824.165865 109.481373 \n", "L 824.198683 109.612836 \n", "L 824.231663 109.489261 \n", "L 824.264624 109.102247 \n", "L 824.330614 109.382183 \n", "L 824.363577 109.352987 \n", "L 824.627417 110.500849 \n", "L 824.660742 110.283002 \n", "L 824.726547 110.411356 \n", "L 824.924984 110.949946 \n", "L 825.089656 110.274421 \n", "L 825.188691 111.486984 \n", "L 825.287445 111.445799 \n", "L 825.38643 111.806451 \n", "L 825.584621 114.119255 \n", "L 825.617833 113.926182 \n", "L 825.683808 113.438129 \n", "L 825.749665 113.556908 \n", "L 825.947807 115.630772 \n", "L 826.112762 117.65387 \n", "L 826.145745 117.498508 \n", "L 826.311073 118.23154 \n", "L 826.476202 119.728852 \n", "L 826.509541 119.335822 \n", "L 826.542529 119.220448 \n", "L 826.575487 119.614942 \n", "L 826.674287 119.583904 \n", "L 826.70723 119.74007 \n", "L 826.805994 119.679562 \n", "L 826.872119 119.859834 \n", "L 827.037016 121.238444 \n", "L 827.169527 120.389471 \n", "L 827.335101 118.699664 \n", "L 827.53378 117.031254 \n", "L 827.863846 114.051133 \n", "L 827.897029 114.157439 \n", "L 827.930289 113.99921 \n", "L 828.227619 112.600035 \n", "L 828.260624 112.638135 \n", "L 828.293857 112.57914 \n", "L 828.393078 112.128622 \n", "L 828.525118 110.666809 \n", "L 828.789507 112.18516 \n", "L 828.822393 111.727659 \n", "L 828.888488 112.240004 \n", "L 828.954783 112.166164 \n", "L 829.020926 112.595582 \n", "L 829.285449 115.002185 \n", "L 829.483642 115.777482 \n", "L 829.516839 116.263664 \n", "L 829.582886 115.919535 \n", "L 829.616003 116.027644 \n", "L 829.748602 116.835505 \n", "L 829.781561 116.503359 \n", "L 829.814528 116.578633 \n", "L 829.946868 117.973687 \n", "L 829.979968 117.89975 \n", "L 830.013028 117.651895 \n", "L 830.046054 118.082602 \n", "L 830.078934 117.937581 \n", "L 830.111918 117.924088 \n", "L 830.145057 117.644978 \n", "L 830.178253 117.773255 \n", "L 830.211378 118.126818 \n", "L 830.310444 118.05379 \n", "L 830.343489 118.27469 \n", "L 830.409387 118.086439 \n", "L 830.442291 118.050861 \n", "L 830.739549 116.439183 \n", "L 830.80565 116.396882 \n", "L 830.970859 115.735208 \n", "L 831.069757 116.381392 \n", "L 831.135893 116.348972 \n", "L 831.168763 116.21993 \n", "L 831.367096 117.472775 \n", "L 831.400165 117.517276 \n", "L 831.565591 119.074093 \n", "L 831.829762 119.910248 \n", "L 831.895731 119.654901 \n", "L 832.159819 116.053052 \n", "L 832.192657 116.098518 \n", "L 832.225719 115.902478 \n", "L 832.258815 115.945197 \n", "L 832.522862 118.957269 \n", "L 832.555875 118.883761 \n", "L 832.588965 118.846635 \n", "L 832.621962 119.030772 \n", "L 832.654928 118.944556 \n", "L 832.820003 118.324951 \n", "L 832.98516 118.778654 \n", "L 833.01818 118.713922 \n", "L 833.051215 118.735214 \n", "L 833.084127 118.510688 \n", "L 833.11701 118.820376 \n", "L 833.150034 118.633866 \n", "L 833.183084 118.761811 \n", "L 833.216164 118.453995 \n", "L 833.282531 118.345778 \n", "L 833.315619 118.802352 \n", "L 833.34893 119.210173 \n", "L 833.415098 118.795473 \n", "L 833.448202 118.563449 \n", "L 833.646251 120.356549 \n", "L 833.811367 118.603227 \n", "L 833.877349 119.174548 \n", "L 833.910449 119.149784 \n", "L 834.042615 120.001673 \n", "L 834.075684 119.523129 \n", "L 834.174738 119.667424 \n", "L 834.306826 119.197834 \n", "L 834.339908 119.213261 \n", "L 834.37283 118.820621 \n", "L 834.471676 118.858214 \n", "L 834.537935 119.343447 \n", "L 834.570871 119.013576 \n", "L 834.736113 118.100727 \n", "L 834.802014 118.352032 \n", "L 834.867936 118.802921 \n", "L 835.132761 120.38191 \n", "L 835.165841 120.207396 \n", "L 835.198888 120.214268 \n", "L 835.364597 120.921907 \n", "L 835.397559 120.837628 \n", "L 835.430902 120.985418 \n", "L 835.596043 121.895832 \n", "L 835.629113 121.554988 \n", "L 835.694905 121.820752 \n", "L 835.959696 124.170309 \n", "L 836.025384 123.63128 \n", "L 836.157176 122.957557 \n", "L 836.190327 123.017766 \n", "L 836.256294 122.424156 \n", "L 836.322211 122.822638 \n", "L 837.016126 128.305919 \n", "L 837.049134 128.381643 \n", "L 837.082196 128.225804 \n", "L 837.181148 127.336861 \n", "L 837.214108 127.498472 \n", "L 837.247182 127.506402 \n", "L 837.313125 128.227829 \n", "L 837.378836 127.896458 \n", "L 837.510234 128.206527 \n", "L 837.510234 128.206527 \n", "\" clip-path=\"url(#paa277a8b49)\" style=\"fill: none; stroke: #ffa500; stroke-width: 2; stroke-linecap: round\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 771.448434 131.200798 \n", "L 771.448434 69.179398 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 771.448434 131.200798 \n", "L 837.510234 131.200798 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_58\">\n", "    <!-- Zoom-in -->\n", "    <g transform=\"translate(774.751524 67.474625) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-5a\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"68.505859\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"129.6875\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"190.869141\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"288.28125\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"324.365234\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"352.148438\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p13454f8562\">\n", "   <rect x=\"58.611234\" y=\"11.588098\" width=\"220.206\" height=\"177.204\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p821b794013\">\n", "   <rect x=\"340.845234\" y=\"11.588098\" width=\"220.206\" height=\"177.204\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p6756c34627\">\n", "   <rect x=\"623.079234\" y=\"11.588098\" width=\"220.206\" height=\"177.204\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p764bb8d49b\">\n", "   <rect x=\"206.980434\" y=\"69.179398\" width=\"66.0618\" height=\"62.0214\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p89aeb5621e\">\n", "   <rect x=\"489.214434\" y=\"69.179398\" width=\"66.0618\" height=\"62.0214\"/>\n", "  </clipPath>\n", "  <clipPath id=\"paa277a8b49\">\n", "   <rect x=\"771.448434\" y=\"69.179398\" width=\"66.0618\" height=\"62.0214\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1200x350 with 6 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from mpl_toolkits.axes_grid1.inset_locator import inset_axes\n", "import matplotlib.ticker as ticker\n", "\n", "# Define constant for the number of steps to zoom in\n", "ZOOM_STEPS = 2000\n", "\n", "fig, axs = plt.subplots(1, 3, figsize=(12, 3.5))\n", "\n", "# First axis (Average reward)\n", "axs[0].plot(time, reward, color='g', linewidth=2)\n", "axs[0].grid(True, color='lightgrey', alpha=0.3)\n", "axs[0].set_xlabel(\"Wall-clock-time [h]\", labelpad=10)\n", "axs[0].set_ylabel(\"Average reward\")\n", "axs[0].set_xlim([0, 24])\n", "axs[0].set_ylim([0.79, 1])\n", "\n", "# Inset for the first axis (zoom-in on last ZOOM_STEPS steps)\n", "axins0 = inset_axes(axs[0], width=\"30%\", height=\"35%\", loc=\"center right\")  # Slightly increased height\n", "axins0.plot(time[-ZOOM_STEPS:], reward[-ZOOM_STEPS:], color='g', linewidth=2)\n", "axins0.grid(True, color='lightgrey', alpha=0.3)\n", "axins0.set_xlim(time[-ZOOM_STEPS], time[-1])\n", "axins0.set_ylim(min(reward[-ZOOM_STEPS:]), max(reward[-ZOOM_STEPS:]))\n", "axins0.set_yscale('log')\n", "\n", "# Label for the zoom-in plot\n", "axins0.text(0.05, 1.15, 'Zoom-in', transform=axins0.transAxes, fontsize=10, color='black', ha='left', va='top')\n", "\n", "# Hide y-axis labels and ticks\n", "axins0.get_yaxis().set_visible(False)\n", "\n", "# Annotate the min and max values in red\n", "min_value = min(reward[-ZOOM_STEPS:])\n", "max_value = max(reward[-ZOOM_STEPS:])\n", "\n", "# Annotating min and max values\n", "axins0.annotate(f'{min_value:.3f}', xy=(time[-ZOOM_STEPS], min_value), xytext=(-20, -2),\n", "                textcoords='offset points', color='red', fontsize=10, ha='center', va='bottom')\n", "axins0.annotate(f'{max_value:.3f}', xy=(time[-ZOOM_STEPS], max_value), xytext=(67, 15),\n", "                textcoords='offset points', color='red', fontsize=10, ha='center', va='top')\n", "\n", "# Second axis (Goal achieved)\n", "axs[1].plot(global_step, goal_achieved, color='b', linewidth=2)\n", "axs[1].grid(True, color='lightgrey', alpha=0.3)\n", "axs[1].set_xlabel(\"Global step\", labelpad=10)\n", "axs[1].set_ylabel(\"Perc. goal achieved [%]\")\n", "\n", "# Inset for the second axis (zoom-in on last ZOOM_STEPS steps)\n", "axins1 = inset_axes(axs[1], width=\"30%\", height=\"35%\", loc=\"center right\")  # Slightly increased height\n", "axins1.plot(global_step[-ZOOM_STEPS:], goal_achieved[-ZOOM_STEPS:], color='b', linewidth=2)\n", "axins1.grid(True, color='lightgrey', alpha=0.3)\n", "axins1.set_xlim(global_step[-ZOOM_STEPS], global_step[-1])\n", "axins1.set_ylim(min(goal_achieved[-ZOOM_STEPS:]), max(goal_achieved[-ZOOM_STEPS:]))\n", "axins1.set_yscale('log')\n", "\n", "# Label for the zoom-in plot\n", "axins1.text(0.05, 1.15, 'Zoom-in', transform=axins1.transAxes, fontsize=10, color='black', ha='left', va='top')\n", "\n", "# Format y-axis labels of the inset plot\n", "for tick in axins1.get_yticklabels():\n", "    tick.set_rotation(0)\n", "    tick.set_fontsize(1)\n", "\n", "axins1.get_yaxis().set_visible(False)\n", "\n", "# Annotate the min and max values in red\n", "min_value = min(goal_achieved[-ZOOM_STEPS:])\n", "max_value = max(goal_achieved[-ZOOM_STEPS:])\n", "\n", "# Annotating min and max values\n", "axins1.annotate(f'{min_value:.3f}', xy=(global_step[-ZOOM_STEPS], min_value), xytext=(-20, -2),\n", "                textcoords='offset points', color='red', fontsize=10, ha='center', va='bottom')\n", "axins1.annotate(f'{max_value:.3f}', xy=(global_step[-ZOOM_STEPS], max_value), xytext=(67, 15),\n", "                textcoords='offset points', color='red', fontsize=10, ha='center', va='top')\n", "\n", "\n", "# Third axis (Collisions and Off-road)\n", "axs[2].plot(global_step, collision, color='r', label='Collided', linewidth=2)\n", "axs[2].plot(global_step, offroad, color='orange', label='Off-road', linewidth=2)\n", "axs[2].grid(True, color='lightgrey', alpha=0.3)\n", "axs[2].set_xlabel(\"Global step\", labelpad=10)\n", "axs[2].set_ylabel(\"Metric [%]\")\n", "#axs[2].set_yscale('log')\n", "axs[2].legend(facecolor='white', framealpha=1, fontsize=10)\n", "\n", "# Inset for the third axis (zoom-in on last ZOOM_STEPS steps)\n", "axins2 = inset_axes(axs[2], width=\"30%\", height=\"35%\", loc=\"center right\")  # Slightly increased height\n", "axins2.plot(global_step[-ZOOM_STEPS:], collision[-ZOOM_STEPS:], color='r', linewidth=2)\n", "axins2.plot(global_step[-ZOOM_STEPS:], offroad[-ZOOM_STEPS:], color='orange', linewidth=2)\n", "axins2.grid(True, color='lightgrey', alpha=0.3)\n", "axins2.set_xlim(global_step[-ZOOM_STEPS], global_step[-1])\n", "axins2.set_yscale('log')\n", "\n", "# Label for the zoom-in plot\n", "axins2.text(0.05, 1.15, 'Zoom-in', transform=axins2.transAxes, fontsize=10, color='black', ha='left', va='top')\n", "\n", "# Format y-axis labels of the inset plot\n", "for tick in axins2.get_yticklabels():\n", "    tick.set_rotation(0)\n", "    tick.set_fontsize(6)\n", "\n", "# Disable scientific notation for y-axis\n", "axins2.yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, _: f'{x:.2f}'))\n", "\n", "sns.despine()\n", "plt.tight_layout()\n", "plt.savefig(f'training_curves.pdf', bbox_inches='tight', format='pdf')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "gpudrive", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}