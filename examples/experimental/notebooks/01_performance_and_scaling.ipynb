{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import seaborn as sns\n", "import warnings\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import datetime\n", "\n", "sns.set(\"notebook\", font_scale=1.05, rc={\"figure.figsize\": (10, 5)})\n", "sns.set_style(\"ticks\", rc={\"figure.facecolor\": \"none\", \"axes.facecolor\": \"none\"})\n", "%config InlineBackend.figure_format = 'svg'\n", "warnings.filterwarnings(\"ignore\")\n", "plt.set_loglevel(\"WARNING\")\n", "mpl.rcParams[\"lines.markersize\"] = 8\n", "\n", "now = datetime.datetime.now()\n", "now_str = now.strftime(\"%m_%d_%H_%M\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Helper functions"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def make_barplot(data, df, df_random_baseline=None, save_path=None):\n", "    \n", "    fig, axs = plt.subplots(1, 3, figsize=(10, 3))\n", "    fig.suptitle(f'Policy performance on the {data} set', y=1.05)\n", "\n", "    # Goal achieved\n", "    sns.barplot(data=df[df['dataset'] == data], errorbar=\"sd\", x='train_dataset_size', y='goal_achieved', ax=axs[0])\n", "    axs[0].set_ylim(0, 1)\n", "    axs[0].grid(True, alpha=0.3)\n", "\n", "    # Collided\n", "    sns.barplot(data=df[df['dataset'] == data], errorbar=\"sd\", x='train_dataset_size', y='collided', ax=axs[1])\n", "    axs[1].grid(True, alpha=0.3)\n", "\n", "    # Off road\n", "    sns.barplot(data=df[df['dataset'] == data], errorbar=\"sd\", x='train_dataset_size', y='off_road', ax=axs[2])\n", "    axs[2].grid(True, alpha=0.3)\n", "        \n", "    # Random policy performance\n", "    if df_random_baseline is not None:\n", "        rand_pi_mean_gr = df_random_baseline[df_random_baseline['dataset'] == data]['goal_achieved'].mean()\n", "        rand_pi_mean_col = df_random_baseline[df_random_baseline['dataset'] == data]['collided'].mean()\n", "        rand_pi_mean_off = df_random_baseline[df_random_baseline['dataset'] == data]['off_road'].mean()\n", "        \n", "        axs[0].axhline(rand_pi_mean_gr, color='r', linestyle='--', linewidth=2)\n", "        axs[1].axhline(rand_pi_mean_col, color='r', linestyle='--', linewidth=2)\n", "        axs[2].axhline(rand_pi_mean_off, color='r', linestyle='--', linewidth=2, label='Random policy')\n", "        \n", "        axs[1].set_ylim(0, rand_pi_mean_col + 0.05)\n", "        axs[2].set_ylim(0, rand_pi_mean_off + 0.05)\n", "        \n", "    fig.legend()\n", "    plt.tight_layout()\n", "\n", "    sns.despine()\n", "    if save_path:\n", "        plt.savefig(save_path)\n", "        \n", "        \n", "def make_histplot(data, df, bins=None, save_path=None):\n", "    \n", "    fig, axs = plt.subplots(1, 3, figsize=(10, 3))\n", "    fig.suptitle(f'Policy performance on the {data} set', y=1.05)\n", "\n", "    # Goal achieved\n", "    sns.histplot(data=df[df['dataset'] == data], x='goal_achieved', bins=bins, ax=axs[0])\n", "    axs[0].grid(True, alpha=0.3)\n", "\n", "    # Collided\n", "    sns.histplot(data=df[df['dataset'] == data], x='collided', bins=bins, ax=axs[1])\n", "    axs[1].grid(True, alpha=0.3)\n", "\n", "    # Off road\n", "    sns.histplot(data=df[df['dataset'] == data], x='off_road', bins=bins, ax=axs[2])\n", "    axs[2].grid(True, alpha=0.3)\n", "        \n", "    fig.legend()\n", "    plt.tight_layout()\n", "\n", "    sns.despine()\n", "    if save_path:\n", "        plt.savefig(save_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configs"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["BASE_DIR = \"...\"\n", "FIGURE_DIR = \"...\"\n", "\n", "# Constants\n", "GRID_ALPHA = 0.4\n", "ERRORBAR = 'ci'\n", "DATA = 'test'\n", "\n", "colors = sns.color_palette(\"tab10\").as_hex()\n", "blue = colors[0]\n", "orange = colors[1]\n", "green = colors[2]\n", "red = colors[3]\n", "purple = colors[4]\n", "\n", "SCENE_BASED_METRICS = ['goal_achieved_frac', 'collided_frac', 'off_road_frac', 'other_frac']\n", "AGENT_BASED_METRICS = ['goal_achieved_count', 'collided_count', 'off_road_count', 'other_count']"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<svg  width=\"550\" height=\"55\"><rect x=\"0\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#1f77b4;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"55\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#ff7f0e;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"110\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#2ca02c;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"165\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#d62728;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"220\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#9467bd;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"275\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#8c564b;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"330\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#e377c2;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"385\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#7f7f7f;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"440\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#bcbd22;stroke-width:2;stroke:rgb(255,255,255)\"/><rect x=\"495\" y=\"0\" width=\"55\" height=\"55\" style=\"fill:#17becf;stroke-width:2;stroke:rgb(255,255,255)\"/></svg>"], "text/plain": ["['#1f77b4',\n", " '#ff7f0e',\n", " '#2ca02c',\n", " '#d62728',\n", " '#9467bd',\n", " '#8c564b',\n", " '#e377c2',\n", " '#7f7f7f',\n", " '#bcbd22',\n", " '#17becf']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["sns.color_palette(\"tab10\").as_hex()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load the results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scaling laws dataframes\n", "df_100 = pd.read_csv(BASE_DIR + 'model_PPO__R_100__02_23_21_28_06_645_006500.csv')\n", "df_1000 = pd.read_csv(BASE_DIR + 'model_PPO__R_1000__02_23_22_34_47_084_007500.csv')\n", "df_10_000 = pd.read_csv(BASE_DIR + 'model_PPO____R_10000__02_26_11_02_54_752_002800.csv')\n", "\n", "#df_rand_baseline = pd.read_csv(BASE_DIR + \"random_baseline.csv\")\n", "\n", "# Best model\n", "df_10_000 = pd.read_csv(BASE_DIR + \"model_PPO__C__R_10000__01_28_20_57_35_873_011426.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Aggregate performance"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# tab_rand_baseline = df_rand_baseline.groupby('dataset')[['goal_achieved', 'collided', 'off_road']].agg(['mean', 'std'])\n", "# tab_rand_baseline = tab_rand_baseline * 100  \n", "# tab_rand_baseline = tab_rand_baseline.round(2)  \n", "\n", "# tab_rand_baseline"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">goal_achieved_frac</th>\n", "      <th colspan=\"2\" halign=\"left\">collided_frac</th>\n", "      <th colspan=\"2\" halign=\"left\">off_road_frac</th>\n", "      <th colspan=\"2\" halign=\"left\">other_frac</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "    <tr>\n", "      <th>dataset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>test</th>\n", "      <td>99.81</td>\n", "      <td>1.53</td>\n", "      <td>0.44</td>\n", "      <td>3.17</td>\n", "      <td>0.31</td>\n", "      <td>2.59</td>\n", "      <td>0.14</td>\n", "      <td>1.1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        goal_achieved_frac       collided_frac       off_road_frac        \\\n", "                      mean   std          mean   std          mean   std   \n", "dataset                                                                    \n", "test                 99.81  1.53          0.44  3.17          0.31  2.59   \n", "\n", "        other_frac       \n", "              mean  std  \n", "dataset                  \n", "test          0.14  1.1  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_10_000 = df_10_000[df_10_000['controlled_agents_in_scene'] != 0]\n", "\n", "tab_10_000 = df_10_000.groupby('dataset')[SCENE_BASED_METRICS].agg(['mean', 'std'])\n", "tab_10_000 = tab_10_000 * 100  \n", "tab_10_000 = tab_10_000.round(2)  \n", "\n", "tab_10_000"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goal_achieved_count</th>\n", "      <th>collided_count</th>\n", "      <th>off_road_count</th>\n", "      <th>other_count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>dataset</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>test</th>\n", "      <td>99.81</td>\n", "      <td>0.32</td>\n", "      <td>0.18</td>\n", "      <td>0.17</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         goal_achieved_count  collided_count  off_road_count  other_count\n", "dataset                                                                  \n", "test                   99.81            0.32            0.18         0.17"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compute the sum of controlled agents\n", "df = df_10_000[df_10_000['dataset'] == 'test']\n", "\n", "total_controlled_agents = df['controlled_agents_in_scene'].sum()\n", "\n", "# Group by 'dataset' and compute normalized values\n", "tab_agent_based_10_000 = (df.groupby('dataset')[AGENT_BASED_METRICS].sum() / total_controlled_agents)*100\n", "\n", "tab_agent_based_10_000.round(2)  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Error distribution"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["8.959999999999999"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_error = df_10_000[df_10_000['dataset'] == 'test']\n", "\n", "df_error = df_10_000[\n", "    (df_10_000['collided_frac'] > 0) |\n", "    (df_10_000['off_road_frac'] > 0) |\n", "    (df_10_000['other_frac'] > 0)\n", "    \n", "]\n", "\n", "(df_error.shape[0] / df_10_000.shape[0]) * 100"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>scene</th>\n", "      <th>goal_achieved_count</th>\n", "      <th>goal_achieved_frac</th>\n", "      <th>collided_count</th>\n", "      <th>collided_frac</th>\n", "      <th>off_road_count</th>\n", "      <th>off_road_frac</th>\n", "      <th>other_count</th>\n", "      <th>other_frac</th>\n", "      <th>controlled_agents_in_scene</th>\n", "      <th>episode_lengths</th>\n", "      <th>dataset</th>\n", "      <th>model_name</th>\n", "      <th>train_dataset_size</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>tfrecord-00048-of-00150_243.json</td>\n", "      <td>3.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.333333</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>3.0</td>\n", "      <td>44.0</td>\n", "      <td>test</td>\n", "      <td>model_PPO__C__R_10000__01_28_20_57_35_873_011426</td>\n", "      <td>10000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>tfrecord-00029-of-00150_80.json</td>\n", "      <td>44.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.022727</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>44.0</td>\n", "      <td>27.0</td>\n", "      <td>test</td>\n", "      <td>model_PPO__C__R_10000__01_28_20_57_35_873_011426</td>\n", "      <td>10000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>tfrecord-00096-of-00150_283.json</td>\n", "      <td>36.0</td>\n", "      <td>0.972973</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.027027</td>\n", "      <td>37.0</td>\n", "      <td>90.0</td>\n", "      <td>test</td>\n", "      <td>model_PPO__C__R_10000__01_28_20_57_35_873_011426</td>\n", "      <td>10000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>tfrecord-00061-of-00150_178.json</td>\n", "      <td>5.0</td>\n", "      <td>0.833333</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.166667</td>\n", "      <td>6.0</td>\n", "      <td>90.0</td>\n", "      <td>test</td>\n", "      <td>model_PPO__C__R_10000__01_28_20_57_35_873_011426</td>\n", "      <td>10000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>tfrecord-00115-of-00150_93.json</td>\n", "      <td>16.0</td>\n", "      <td>0.941176</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.058824</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>17.0</td>\n", "      <td>90.0</td>\n", "      <td>test</td>\n", "      <td>model_PPO__C__R_10000__01_28_20_57_35_873_011426</td>\n", "      <td>10000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               scene  goal_achieved_count  goal_achieved_frac  \\\n", "4   tfrecord-00048-of-00150_243.json                  3.0            1.000000   \n", "8    tfrecord-00029-of-00150_80.json                 44.0            1.000000   \n", "15  tfrecord-00096-of-00150_283.json                 36.0            0.972973   \n", "16  tfrecord-00061-of-00150_178.json                  5.0            0.833333   \n", "24   tfrecord-00115-of-00150_93.json                 16.0            0.941176   \n", "\n", "    collided_count  collided_frac  off_road_count  off_road_frac  other_count  \\\n", "4              0.0       0.000000             1.0       0.333333          0.0   \n", "8              1.0       0.022727             0.0       0.000000          0.0   \n", "15             0.0       0.000000             0.0       0.000000          1.0   \n", "16             0.0       0.000000             0.0       0.000000          1.0   \n", "24             0.0       0.000000             1.0       0.058824          0.0   \n", "\n", "    other_frac  controlled_agents_in_scene  episode_lengths dataset  \\\n", "4     0.000000                         3.0             44.0    test   \n", "8     0.000000                        44.0             27.0    test   \n", "15    0.027027                        37.0             90.0    test   \n", "16    0.166667                         6.0             90.0    test   \n", "24    0.000000                        17.0             90.0    test   \n", "\n", "                                          model_name  train_dataset_size  \n", "4   model_PPO__C__R_10000__01_28_20_57_35_873_011426               10000  \n", "8   model_PPO__C__R_10000__01_28_20_57_35_873_011426               10000  \n", "15  model_PPO__C__R_10000__01_28_20_57_35_873_011426               10000  \n", "16  model_PPO__C__R_10000__01_28_20_57_35_873_011426               10000  \n", "24  model_PPO__C__R_10000__01_28_20_57_35_873_011426               10000  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_error.head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(896, 14)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_error.shape"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"634.5045pt\" height=\"202.284082pt\" viewBox=\"0 0 634.5045 202.284082\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2025-01-30T21:09:19.246370</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 202.284082 \n", "L 634.5045 202.284082 \n", "L 634.5045 0 \n", "L 0 0 \n", "L 0 202.284082 \n", "z\n", "\" style=\"fill: none\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 54.9405 158.792098 \n", "L 204.3765 158.792098 \n", "L 204.3765 11.588098 \n", "L 54.9405 11.588098 \n", "L 54.9405 158.792098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 61.733045 158.792098 \n", "L 61.733045 11.588098 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_2\">\n", "      <defs>\n", "       <path id=\"m40789a9f25\" d=\"M 0 0 \n", "L 0 6 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"61.733045\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(52.548991 177.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 102.488316 158.792098 \n", "L 102.488316 11.588098 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"102.488316\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.2 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(93.304261 177.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 143.243587 158.792098 \n", "L 143.243587 11.588098 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"143.243587\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.4 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(134.059532 177.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 183.998858 158.792098 \n", "L 183.998858 11.588098 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"183.998858\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.6 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(174.814803 177.068293) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_5\">\n", "     <!-- Collided in scene -->\n", "     <g style=\"fill: #262626\" transform=\"translate(78.592875 192.588457) scale(0.12 -0.12)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-43\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"69.824219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"131.005859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"158.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"186.572266\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"214.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"277.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"339.355469\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"402.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"434.619141\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"462.402344\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"525.78125\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"557.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"609.667969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"664.648438\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"726.171875\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"789.550781\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 54.9405 158.792098 \n", "L 204.3765 158.792098 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m6bc957fec3\" d=\"M 0 0 \n", "L -6 0 \n", "\" style=\"stroke: #262626; stroke-width: 1.25\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"54.9405\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(38.091813 163.180195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 54.9405 129.351298 \n", "L 204.3765 129.351298 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"54.9405\" y=\"129.351298\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 133.739395) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 54.9405 99.910498 \n", "L 204.3765 99.910498 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"54.9405\" y=\"99.910498\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 104.298595) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_15\">\n", "      <path d=\"M 54.9405 70.469698 \n", "L 204.3765 70.469698 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"54.9405\" y=\"70.469698\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 74.857795) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_17\">\n", "      <path d=\"M 54.9405 41.028898 \n", "L 204.3765 41.028898 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"54.9405\" y=\"41.028898\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(30.743125 45.416995) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_19\">\n", "      <path d=\"M 54.9405 11.588098 \n", "L 204.3765 11.588098 \n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"54.9405\" y=\"11.588098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 100 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(23.394437 15.976195) scale(0.1155 -0.1155)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- Percent -->\n", "     <g style=\"fill: #262626\" transform=\"translate(16.774031 108.887941) rotate(-90) scale(0.126 -0.126)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"56.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"118.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"157.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"212.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"273.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"336.947266\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 61.733045 158.792098 \n", "L 75.318136 158.792098 \n", "L 75.318136 48.881968 \n", "L 61.733045 48.881968 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 75.318136 158.792098 \n", "L 88.903227 158.792098 \n", "L 88.903227 139.405857 \n", "L 75.318136 139.405857 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 88.903227 158.792098 \n", "L 102.488318 158.792098 \n", "L 102.488318 148.277526 \n", "L 88.903227 148.277526 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 102.488318 158.792098 \n", "L 116.073409 158.792098 \n", "L 116.073409 157.149196 \n", "L 102.488318 157.149196 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 116.073409 158.792098 \n", "L 129.6585 158.792098 \n", "L 129.6585 156.163455 \n", "L 116.073409 156.163455 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 129.6585 158.792098 \n", "L 143.243591 158.792098 \n", "L 143.243591 157.970647 \n", "L 129.6585 157.970647 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 143.243591 158.792098 \n", "L 156.828682 158.792098 \n", "L 156.828682 158.792098 \n", "L 143.243591 158.792098 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 156.828682 158.792098 \n", "L 170.413773 158.792098 \n", "L 170.413773 157.477776 \n", "L 156.828682 157.477776 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 170.413773 158.792098 \n", "L 183.998864 158.792098 \n", "L 183.998864 158.299227 \n", "L 170.413773 158.299227 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 183.998864 158.792098 \n", "L 197.583955 158.792098 \n", "L 197.583955 158.299227 \n", "L 183.998864 158.299227 \n", "z\n", "\" clip-path=\"url(#p602089941c)\" style=\"fill: #d62728; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 54.9405 158.792098 \n", "L 54.9405 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 54.9405 158.792098 \n", "L 204.3765 158.792098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 266.4045 158.792098 \n", "L 415.8405 158.792098 \n", "L 415.8405 11.588098 \n", "L 266.4045 11.588098 \n", "L 266.4045 158.792098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_21\">\n", "      <path d=\"M 273.197045 158.792098 \n", "L 273.197045 11.588098 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"273.197045\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(264.012991 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_23\">\n", "      <path d=\"M 313.952316 158.792098 \n", "L 313.952316 11.588098 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"313.952316\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.2 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(304.768261 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_25\">\n", "      <path d=\"M 354.707587 158.792098 \n", "L 354.707587 11.588098 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"354.707587\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0.4 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(345.523532 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_27\">\n", "      <path d=\"M 395.462858 158.792098 \n", "L 395.462858 11.588098 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"395.462858\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 0.6 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(386.278803 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Off-road in scene -->\n", "     <g style=\"fill: #262626\" transform=\"translate(289.9575 192.588457) scale(0.12 -0.12)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4f\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"113.916016\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"143.621094\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"179.705078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"218.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"279.75\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"341.029297\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"404.505859\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"436.292969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"464.076172\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"527.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"559.242188\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"611.341797\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"666.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"727.845703\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"791.224609\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_29\">\n", "      <path d=\"M 266.4045 158.792098 \n", "L 415.8405 158.792098 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"266.4045\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(249.555813 163.180195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_31\">\n", "      <path d=\"M 266.4045 129.351298 \n", "L 415.8405 129.351298 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"266.4045\" y=\"129.351298\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(242.207125 133.739395) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_33\">\n", "      <path d=\"M 266.4045 99.910498 \n", "L 415.8405 99.910498 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"266.4045\" y=\"99.910498\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(242.207125 104.298595) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_35\">\n", "      <path d=\"M 266.4045 70.469698 \n", "L 415.8405 70.469698 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"266.4045\" y=\"70.469698\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(242.207125 74.857795) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_37\">\n", "      <path d=\"M 266.4045 41.028898 \n", "L 415.8405 41.028898 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"266.4045\" y=\"41.028898\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(242.207125 45.416995) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_39\">\n", "      <path d=\"M 266.4045 11.588098 \n", "L 415.8405 11.588098 \n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"266.4045\" y=\"11.588098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 100 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(234.858438 15.976195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_24\">\n", "     <!-- Percent -->\n", "     <g style=\"fill: #262626\" transform=\"translate(228.238031 108.887941) rotate(-90) scale(0.126 -0.126)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"56.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"118.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"157.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"212.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"273.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"336.947266\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 273.197045 158.792098 \n", "L 286.782136 158.792098 \n", "L 286.782136 35.410174 \n", "L 273.197045 35.410174 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 286.782136 158.792098 \n", "L 300.367227 158.792098 \n", "L 300.367227 144.991723 \n", "L 286.782136 144.991723 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 300.367227 158.792098 \n", "L 313.952318 158.792098 \n", "L 313.952318 154.027682 \n", "L 300.367227 154.027682 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 313.952318 158.792098 \n", "L 327.537409 158.792098 \n", "L 327.537409 157.313486 \n", "L 313.952318 157.313486 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 327.537409 158.792098 \n", "L 341.1225 158.792098 \n", "L 341.1225 156.984906 \n", "L 327.537409 156.984906 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 341.1225 158.792098 \n", "L 354.707591 158.792098 \n", "L 354.707591 158.627807 \n", "L 341.1225 158.627807 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 354.707591 158.792098 \n", "L 368.292682 158.792098 \n", "L 368.292682 158.792098 \n", "L 354.707591 158.792098 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 368.292682 158.792098 \n", "L 381.877773 158.792098 \n", "L 381.877773 157.313486 \n", "L 368.292682 157.313486 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 381.877773 158.792098 \n", "L 395.462864 158.792098 \n", "L 395.462864 158.792098 \n", "L 381.877773 158.792098 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 395.462864 158.792098 \n", "L 409.047955 158.792098 \n", "L 409.047955 158.463517 \n", "L 395.462864 158.463517 \n", "z\n", "\" clip-path=\"url(#p066e3676a5)\" style=\"fill: #ff7f0e; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 266.4045 158.792098 \n", "L 266.4045 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 266.4045 158.792098 \n", "L 415.8405 158.792098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 477.8685 158.792098 \n", "L 627.3045 158.792098 \n", "L 627.3045 11.588098 \n", "L 477.8685 11.588098 \n", "L 477.8685 158.792098 \n", "z\n", "\" style=\"fill: none\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_41\">\n", "      <path d=\"M 484.661045 158.792098 \n", "L 484.661045 11.588098 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"484.661045\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- 0.0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(475.476991 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_43\">\n", "      <path d=\"M 525.416316 158.792098 \n", "L 525.416316 11.588098 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"525.416316\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 0.2 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(516.232261 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_45\">\n", "      <path d=\"M 566.171587 158.792098 \n", "L 566.171587 11.588098 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"566.171587\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.4 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(556.987532 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_47\">\n", "      <path d=\"M 606.926858 158.792098 \n", "L 606.926858 11.588098 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m40789a9f25\" x=\"606.926858\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.6 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(597.742803 177.068293) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_29\">\n", "     <!-- Other in scene -->\n", "     <g style=\"fill: #262626\" transform=\"translate(508.656187 192.588457) scale(0.12 -0.12)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4f\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"78.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"117.919922\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"181.298828\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"242.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"283.935547\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"315.722656\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"343.505859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"406.884766\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"438.671875\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"490.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"545.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"607.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"670.654297\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_49\">\n", "      <path d=\"M 477.8685 158.792098 \n", "L 627.3045 158.792098 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"477.8685\" y=\"158.792098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(461.019813 163.180195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_51\">\n", "      <path d=\"M 477.8685 129.351298 \n", "L 627.3045 129.351298 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"477.8685\" y=\"129.351298\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 20 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(453.671125 133.739395) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_53\">\n", "      <path d=\"M 477.8685 99.910498 \n", "L 627.3045 99.910498 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"477.8685\" y=\"99.910498\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_32\">\n", "      <!-- 40 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(453.671125 104.298595) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_55\">\n", "      <path d=\"M 477.8685 70.469698 \n", "L 627.3045 70.469698 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"477.8685\" y=\"70.469698\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_33\">\n", "      <!-- 60 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(453.671125 74.857795) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_57\">\n", "      <path d=\"M 477.8685 41.028898 \n", "L 627.3045 41.028898 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"477.8685\" y=\"41.028898\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_34\">\n", "      <!-- 80 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(453.671125 45.416995) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_59\">\n", "      <path d=\"M 477.8685 11.588098 \n", "L 627.3045 11.588098 \n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: none; stroke: #cccccc; stroke-opacity: 0.2; stroke-linecap: round\"/>\n", "     </g>\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#m6bc957fec3\" x=\"477.8685\" y=\"11.588098\" style=\"fill: #262626; stroke: #262626; stroke-width: 1.25\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_35\">\n", "      <!-- 100 -->\n", "      <g style=\"fill: #262626\" transform=\"translate(446.322438 15.976195) scale(0.1155 -0.1155)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_36\">\n", "     <!-- Percent -->\n", "     <g style=\"fill: #262626\" transform=\"translate(439.702031 108.887941) rotate(-90) scale(0.126 -0.126)\">\n", "      <use xlink:href=\"#DejaVuSans-50\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"56.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"118.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"157.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"212.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"273.568359\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"336.947266\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 484.661045 158.792098 \n", "L 494.849863 158.792098 \n", "L 494.849863 25.717053 \n", "L 484.661045 25.717053 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 494.849863 158.792098 \n", "L 505.038681 158.792098 \n", "L 505.038681 148.277526 \n", "L 494.849863 148.277526 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 505.038681 158.792098 \n", "L 515.227498 158.792098 \n", "L 515.227498 156.492035 \n", "L 505.038681 156.492035 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 515.227498 158.792098 \n", "L 525.416316 158.792098 \n", "L 525.416316 158.134937 \n", "L 515.227498 158.134937 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 525.416316 158.792098 \n", "L 535.605134 158.792098 \n", "L 535.605134 158.463517 \n", "L 525.416316 158.463517 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 535.605134 158.792098 \n", "L 545.793951 158.792098 \n", "L 545.793951 158.627807 \n", "L 535.605134 158.627807 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 545.793951 158.792098 \n", "L 555.982769 158.792098 \n", "L 555.982769 158.792098 \n", "L 545.793951 158.792098 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 555.982769 158.792098 \n", "L 566.171587 158.792098 \n", "L 566.171587 158.792098 \n", "L 555.982769 158.792098 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 566.171587 158.792098 \n", "L 576.360405 158.792098 \n", "L 576.360405 158.792098 \n", "L 566.171587 158.792098 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 576.360405 158.792098 \n", "L 586.549222 158.792098 \n", "L 586.549222 158.627807 \n", "L 576.360405 158.627807 \n", "z\n", "\" clip-path=\"url(#p4bb5b924e3)\" style=\"fill: #808080; fill-opacity: 0.75; stroke: #ffffff; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 477.8685 158.792098 \n", "L 477.8685 11.588098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 477.8685 158.792098 \n", "L 627.3045 158.792098 \n", "\" style=\"fill: none; stroke: #262626; stroke-width: 1.25; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p602089941c\">\n", "   <rect x=\"54.9405\" y=\"11.588098\" width=\"149.436\" height=\"147.204\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p066e3676a5\">\n", "   <rect x=\"266.4045\" y=\"11.588098\" width=\"149.436\" height=\"147.204\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p4bb5b924e3\">\n", "   <rect x=\"477.8685\" y=\"11.588098\" width=\"149.436\" height=\"147.204\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x300 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 1000x500 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axes = plt.subplots(1, 3, figsize=(9, 3), sharex=True)\n", "metrics = ['collided_frac', 'off_road_frac', 'other_frac']\n", "titles = ['Collided in scene', 'Off-road in scene', 'Other in scene']\n", "colors = [red, orange, 'grey']\n", "\n", "for ax, metric, title, color in zip(axes, metrics, titles, colors):\n", "    sns.histplot(df_error, stat='percent', color=color, x=metric, kde=False, bins=10, ax=ax)\n", "    #ax.set_title(title, fontsize=14)\n", "    ax.set_xlabel('')\n", "    ax.grid(True, alpha=0.2)\n", "    ax.set_ylim(0, 100)\n", "    ax.set_xlabel(title, fontsize=12)\n", "    sns.despine()\n", "\n", "# Improve layout\n", "#plt.xlabel(\"Scene-based metric (%)\")\n", "plt.tight_layout()\n", "plt.show()\n", "sns.despine()\n", "fig.savefig(f'{FIGURE_DIR}eror_distribution_{now_str}.pdf', bbox_inches='tight', format='pdf');"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scipy.stats import pearsonr\n", "\n", "# Compute correlation and p-value\n", "corr, p_value = pearsonr(df_error['collided_frac'], df_error['off_road_frac'])\n", "\n", "print(f'Correlation: {corr:.4f}')\n", "print(f'P-value: {p_value:.4f}')\n", "\n", "alpha = 0.05  # Typical significance level\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["# THE_TABLE = tab_train_10_000_cont\n", "\n", "# # Combine mean and std into \"mean ± std\"\n", "# def format_mean_std(row):\n", "#     formatted = {}\n", "#     for col in row.index.get_level_values(0).unique():  # Iterate over the metric names\n", "#         mean = row[(col, 'mean')]\n", "#         std = row[(col, 'std')]\n", "#         formatted[col] = f\"{mean:.2f} ± {std:.2f}\"\n", "#     return formatted\n", "\n", "# # Apply formatting to all rows\n", "# tab_formatted = THE_TABLE.apply(format_mean_std, axis=1)\n", "\n", "# # Flatten the result\n", "# tab_formatted = pd.DataFrame(tab_formatted.tolist(), index=tab_formatted.index)\n", "\n", "# # Generate LaTeX table\n", "# latex_table = tab_formatted.to_latex(\n", "#     index=True,\n", "#     caption=\"Aggregate performance on WOMD (mean ± std)\",\n", "#     label=\"tab:aggregate_performance\",\n", "# )\n", "\n", "# # Generate LaTeX table code with \\resizebox to fit within a single column\n", "# latex_code = \"\"\"\n", "# \\\\begin{table}[htbp]\n", "# \\\\centering\n", "# \\\\resizebox{\\\\columnwidth}{!}{\n", "# \\\\begin{tabular}{@{}lcccc@{}} \n", "# \\\\toprule\n", "# \\\\textbf{Dataset} & \\\\textbf{Goal achieved} & \\\\textbf{Collided} & \\\\textbf{Off-road} & \\\\textbf{Other} \\\\\\\\\n", "# \\\\midrule\n", "# \"\"\"\n", "\n", "# # Add rows to the table\n", "# for idx, row in tab_formatted.iterrows():\n", "#     latex_code += f\"\\\\textbf{{{idx}}} & \"  # dataset name in bold\n", "#     latex_code += \" & \".join([f\"{value}\" for value in row])  # values for each metric\n", "#     latex_code += \" \\\\\\\\ \\n\"\n", "\n", "# # Closing part of the LaTeX table\n", "# latex_code += \"\"\"\n", "# \\\\bottomrule\n", "# \\\\end{tabular}\n", "# }\n", "# \\\\caption{Aggregate performance on train and 10,000 test traffic scenarios from the Waymo Open Motion Dataset (mean $\\\\pm$ std)}\n", "# \\\\label{tab:aggregate_perf_best_policy}\n", "# \\\\end{table}\n", "# \"\"\"\n", "# print(latex_code)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> laws\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["df = pd.concat([df_100, df_1000, df_10_000]).reset_index(drop=True)\n", "categories = ['goal_achieved_frac','collided_frac', 'off_road_frac']\n", "df[categories] = df[categories] * 100\n", "df['train_dataset_size'] = df['train_dataset_size'].apply(lambda x: f\"{int(x):,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ERRORBAR = 'se'\n", "MARKERSIZE = 6\n", "MARKER = 's'\n", "\n", "fig, axs = plt.subplots(1, 3, figsize=(9.3, 3))\n", "\n", "# Goal achieved\n", "sns.pointplot(\n", "    data=df[df['dataset'] == DATA], \n", "    errorbar=ERRORBAR, \n", "    x='train_dataset_size', \n", "    y='goal_achieved_frac', \n", "    marker='o',\n", "    markersize=MARKERSIZE,\n", "    color=blue, \n", "    ax=axs[0]\n", ")\n", "axs[0].set_ylim(70, 105)\n", "axs[0].grid(axis='y', linestyle='-', color='lightgrey', alpha=0.3)\n", "axs[0].set_xlabel(\"Training dataset size\", labelpad=10)\n", "# Add horizontal line at 100%\n", "axs[0].axhline(100, color='#aac0ea', linestyle='--', linewidth=2, zorder=0)\n", "axs[0].set_ylabel(\"Goal achieved [%]\")\n", "\n", "# Collided\n", "sns.pointplot(\n", "    data=df[df['dataset'] == DATA], \n", "    errorbar=ERRORBAR, \n", "    x='train_dataset_size', \n", "    y='collided_frac', \n", "    marker='s',\n", "    markersize=MARKERSIZE,\n", "    color=red, \n", "    ax=axs[1]\n", ")\n", "\n", "axs[1].set_ylim(-2, 15)\n", "axs[1].grid(axis='y', linestyle='-', color='lightgrey', alpha=0.3)\n", "axs[1].axhline(0, color='#ffc7c4', linestyle='--', linewidth=2, zorder=0)\n", "axs[1].set_xlabel(\"Training dataset size\", labelpad=10)\n", "axs[1].set_ylabel(\"Collided [%]\")\n", "\n", "# Off road\n", "sns.pointplot(\n", "    data=df[df['dataset'] == DATA], \n", "    errorbar=ERRORBAR, \n", "    x='train_dataset_size', \n", "    y='off_road_frac', \n", "    marker=\"D\",\n", "    markersize=MARKERSIZE,\n", "    color=orange, \n", "    ax=axs[2]\n", ")\n", "\n", "axs[2].set_ylim(-2, 15)\n", "axs[2].grid(axis='y', linestyle='-', color='lightgrey', alpha=0.3)\n", "axs[2].set_xlabel(\"Training dataset size\", labelpad=10)\n", "axs[2].axhline(0, color=\"#ffb995\", linestyle='--', linewidth=2, zorder=0)\n", "axs[2].set_ylabel(\"Off-road [%]\")\n", "\n", "plt.tight_layout()\n", "sns.despine()\n", "plt.savefig(f'{FIGURE_DIR}scaling_laws_point_{now_str}_simple.pdf', bbox_inches='tight', format='pdf')"]}], "metadata": {"kernelspec": {"display_name": "gpudrive", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}