{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import dataclasses\n", "import mediapy\n", "from huggingface_hub import PyTorchModelHubMixin\n", "from huggingface_hub import ModelCard\n", "from gpudrive.networks.late_fusion import NeuralNet\n", "from gpudrive.env.config import EnvConfig\n", "from gpudrive.env.env_torch import GPUDriveTorchEnv\n", "from gpudrive.env.dataset import SceneDataLoader\n", "from gpudrive.utils.config import load_config \n", "import sys\n", "import imageio\n", "import numpy as np\n", "import os\n", "from gpudrive.utils.multi_policy_rollout import multi_policy_rollout\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_policy_masks(env, num_sim_agents=2, num_worlds=10):\n", "    policy_mask = torch.zeros_like(env.cont_agent_mask, dtype=torch.int)\n", "    agent_indices = env.cont_agent_mask.nonzero(as_tuple=True)\n", "\n", "    for i, (world_idx, agent_idx) in enumerate(zip(*agent_indices)):\n", "        policy_mask[world_idx, agent_idx] = (i % num_sim_agents) + 1\n", "\n", "    policy_masks = {f'pi_{int(policy.item())}': torch.zeros_like(env.cont_agent_mask, dtype=torch.bool,device=device) \n", "                    for policy in policy_mask.unique() if policy.item() != 0}\n", "\n", "    for p in range(1, num_sim_agents + 1):\n", "        policy_masks[f'pi_{p}'] = (policy_mask == p).reshape(num_worlds, -1)\n", "\n", "    return policy_masks\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Configs model has been trained with\n", "config = load_config(\"../../examples/experimental/config/reliable_agents_params\")\n", "max_agents = config.max_controlled_agents\n", "NUM_ENVS = 2\n", "device = \"cpu\" # cpu just because we're in a notebook\n", "NUM_SIM_AGENTS = 2\n", "FPS = 5\n", "\n", "sim_agent1 = NeuralNet.from_pretrained(\"daphne-cornelisse/policy_S10_000_02_27\")\n", "sim_agent2 = NeuralNet.from_pretrained(\"daphne-cornelisse/policy_S1000_02_27\")\n", "\n", "# Some other info\n", "card = ModelCard.load(\"daphne-cornelisse/policy_S10_000_02_27\")\n", "\n", "\n", "\n", "\n", "sim_agent1 = NeuralNet.from_pretrained(\"daphne-cornelisse/policy_S10_000_02_27\")\n", "sim_agent2 = NeuralNet.from_pretrained(\"daphne-cornelisse/policy_S1000_02_27\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "train_loader = SceneDataLoader(\n", "    root='../../data/processed/examples',\n", "    batch_size=NUM_ENVS,\n", "    dataset_size=100,\n", "    sample_with_replacement=False,\n", ")\n", "\n", "# Set params\n", "env_config = dataclasses.replace(\n", "    EnvConfig(),\n", "    ego_state=config.ego_state,\n", "    road_map_obs=config.road_map_obs,\n", "    partner_obs=config.partner_obs,\n", "    reward_type=config.reward_type,\n", "    norm_obs=config.norm_obs,\n", "    dynamics_model=config.dynamics_model,\n", "    collision_behavior=config.collision_behavior,\n", "    dist_to_goal_threshold=config.dist_to_goal_threshold,\n", "    polyline_reduction_threshold=config.polyline_reduction_threshold,\n", "    remove_non_vehicles=config.remove_non_vehicles,\n", "    lidar_obs=config.lidar_obs,\n", "    disable_classic_obs=config.lidar_obs,\n", "    obs_radius=config.obs_radius,\n", "    steer_actions = torch.round(\n", "        torch.linspace(-torch.pi, torch.pi, config.action_space_steer_disc), decimals=3  \n", "    ),\n", "    accel_actions = torch.round(\n", "        torch.linspace(-4.0, 4.0, config.action_space_accel_disc), decimals=3\n", "    ),\n", ")\n", "\n", "\n", "\n", "env = GPUDriveTorchEnv(\n", "    config=env_config,\n", "    data_loader=train_loader,\n", "    max_cont_agents=max_agents,\n", "    device=device,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next_obs = env.reset()\n", "\n", "\n", "control_mask = env.cont_agent_mask\n", "\n", "policy_mask = create_policy_masks(env, 2,NUM_ENVS)\n", "\n", "policies_set = {'pi_1': (sim_agent1,policy_mask['pi_1']),\n", "                'pi_2': (sim_agent2, policy_mask['pi_2'])\n", "                } \n", "        \n", "\n", "\n", "metrics,frames=multi_policy_rollout(\n", "env,\n", "policies_set, \n", "device,\n", "deterministic=False,\n", "render_sim_state = True,\n", "render_every_n_steps= 5\n", ")\n", "\n", "\n", "\n", "\n", "\n", "\n", "env.close()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "mediapy.show_videos(frames, fps=15, width=500, height=500, columns=2, codec='gif')"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "gpudriveenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}