{"cells": [{"cell_type": "code", "execution_count": 2, "id": "c5bb52dc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/smas/lib/python3.11/site-packages/jaxlib/plugin_support.py:71: RuntimeWarning: JAX plugin jax_cuda12_plugin version 0.5.3 is installed, but it is not compatible with the installed jaxlib version 0.6.1, so it will not be used.\n", "  warnings.warn(\n", "2025-06-04 00:55:22.375456: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:467] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1748987722.384332 1216789 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1748987722.387120 1216789 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "W0000 00:00:1748987722.394050 1216789 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "W0000 00:00:1748987722.394060 1216789 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "W0000 00:00:1748987722.394061 1216789 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "W0000 00:00:1748987722.394062 1216789 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n"]}, {"ename": "ValueError", "evalue": "numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 21\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mjax\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mjnp\u001b[39;00m  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[32m     20\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m config \u001b[38;5;28;01mas\u001b[39;00m wconfig  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dataloader \u001b[38;5;28;01mas\u001b[39;00m wdl  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdatatypes\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m simulator_state \u001b[38;5;28;01mas\u001b[39;00m sim_state  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/waymax/dataloader/__init__.py:16\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Copyright 2023 The Waymax Authors.\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# Licensed under the Waymax License Agreement for Non-commercial Use\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     12\u001b[39m \u001b[38;5;66;03m# See the License for the specific language governing permissions and\u001b[39;00m\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# limitations under the License.\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[33;03m\"\"\"Libraries for loading data in Waymax.\"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdataloader\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdataloader_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_data_generator\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdataloader\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdataloader_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf_examples_dataset\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdataloader\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mwomd_dataloader\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m preprocess_serialized_womd_data\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/waymax/dataloader/dataloader_utils.py:24\u001b[39m\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Callable, Iterator, Optional, Sequence, TypeVar\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mjax\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtf\u001b[39;00m\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwaymax\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m config \u001b[38;5;28;01mas\u001b[39;00m _config\n\u001b[32m     29\u001b[39m T = TypeVar(\u001b[33m'\u001b[39m\u001b[33mT\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/tensorflow/__init__.py:468\u001b[39m\n\u001b[32m    466\u001b[39m     importlib.import_module(\u001b[33m\"\u001b[39m\u001b[33mtf_keras.src.optimizers\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    467\u001b[39m   \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m468\u001b[39m     \u001b[43mimportlib\u001b[49m\u001b[43m.\u001b[49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mkeras.src.optimizers\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    469\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mImportError\u001b[39;00m, \u001b[38;5;167;01mAttributeError\u001b[39;00m):\n\u001b[32m    470\u001b[39m   \u001b[38;5;28;01mpass\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/importlib/__init__.py:126\u001b[39m, in \u001b[36mimport_module\u001b[39m\u001b[34m(name, package)\u001b[39m\n\u001b[32m    124\u001b[39m             \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m    125\u001b[39m         level += \u001b[32m1\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m126\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/__init__.py:7\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\"DO NOT EDIT.\u001b[39;00m\n\u001b[32m      2\u001b[39m \n\u001b[32m      3\u001b[39m \u001b[33;03mThis file was autogenerated. Do not edit it by hand,\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03msince your modifications would be overwritten.\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _tf_keras \u001b[38;5;28;01mas\u001b[39;00m _tf_keras\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m activations \u001b[38;5;28;01mas\u001b[39;00m activations\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m applications \u001b[38;5;28;01mas\u001b[39;00m applications\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/_tf_keras/__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_tf_keras\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m keras\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/_tf_keras/keras/__init__.py:7\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\"DO NOT EDIT.\u001b[39;00m\n\u001b[32m      2\u001b[39m \n\u001b[32m      3\u001b[39m \u001b[33;03mThis file was autogenerated. Do not edit it by hand,\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03msince your modifications would be overwritten.\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m activations \u001b[38;5;28;01mas\u001b[39;00m activations\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m applications \u001b[38;5;28;01mas\u001b[39;00m applications\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m callbacks \u001b[38;5;28;01mas\u001b[39;00m callbacks\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/activations/__init__.py:7\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\"DO NOT EDIT.\u001b[39;00m\n\u001b[32m      2\u001b[39m \n\u001b[32m      3\u001b[39m \u001b[33;03mThis file was autogenerated. Do not edit it by hand,\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03msince your modifications would be overwritten.\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mactivations\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m deserialize \u001b[38;5;28;01mas\u001b[39;00m deserialize\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mker<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mactivations\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get \u001b[38;5;28;01mas\u001b[39;00m get\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mactivations\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m serialize \u001b[38;5;28;01mas\u001b[39;00m serialize\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m activations\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m applications\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m backend\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/activations/__init__.py:34\u001b[39m\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mactivations\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mactivations\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m threshold\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi_export\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m keras_export\n\u001b[32m---> \u001b[39m\u001b[32m34\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mker<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m object_registration\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m serialization_lib\n\u001b[32m     37\u001b[39m ALL_OBJECTS = {\n\u001b[32m     38\u001b[39m     relu,\n\u001b[32m     39\u001b[39m     leaky_relu,\n\u001b[32m   (...)\u001b[39m\u001b[32m     67\u001b[39m     sparsemax,\n\u001b[32m     68\u001b[39m }\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/saving/__init__.py:7\u001b[39m\n\u001b[32m      5\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mobject_registration\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m get_registered_object\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mobject_registration\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m register_keras_serializable\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving_api\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m load_model\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mserialization_lib\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m deserialize_keras_object\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mserialization_lib\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m serialize_keras_object\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/saving/saving_api.py:7\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mabsl\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m logging\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi_export\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m keras_export\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlegacy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m legacy_h5_format\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m saving_lib\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m file_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/legacy/saving/legacy_h5_format.py:13\u001b[39m\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlegacy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m json_utils\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mker<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlegacy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m saving_options\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlegacy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m saving_utils\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m object_registration\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m io_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/legacy/saving/saving_utils.py:10\u001b[39m\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m losses\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m metrics \u001b[38;5;28;01mas\u001b[39;00m metrics_module\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m models\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m optimizers\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tree\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/models/__init__.py:1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfunctional\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Functional\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodel\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Model\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msequential\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Sequential\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/models/functional.py:16\u001b[39m\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlegacy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m saving_utils\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlegacy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m serialization \u001b[38;5;28;01mas\u001b[39;00m legacy_serialization\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodel\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Model\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfunction\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Function\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfunction\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _build_map\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/models/model.py:12\u001b[39m\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mvariable_mapping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m map_saveable_variables\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mker<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msaving\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m saving_api\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m trainer \u001b[38;5;28;01mas\u001b[39;00m base_trainer\n\u001b[32m     13\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m summary_utils\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m traceback_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/trainers/trainer.py:14\u001b[39m\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcompile_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m CompileLoss\n\u001b[32m     13\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcompile_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m CompileMetrics\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m data_adapter_utils\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m python_utils\n\u001b[32m     16\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m traceback_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/trainers/data_adapters/__init__.py:4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtypes\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdistribution\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m distribution_lib\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m array_data_adapter\n\u001b[32m      5\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m data_adapter\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m py_dataset_adapter\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/trainers/data_adapters/array_data_adapter.py:7\u001b[39m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tree\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m array_slicing\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m data_adapter_utils\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mkeras\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapters\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata_adapter\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m DataAdapter\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/keras/src/trainers/data_adapters/array_slicing.py:12\u001b[39m\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msrc\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmodule_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensorflow \u001b[38;5;28;01mas\u001b[39;00m tf\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m     \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\n\u001b[32m     13\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[32m     14\u001b[39m     pandas = \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/__init__.py:22\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mdel\u001b[39;00m _hard_dependencies, _dependency, _missing_dependencies\n\u001b[32m     21\u001b[39m \u001b[38;5;66;03m# numpy compat\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcompat\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m is_numpy_dev \u001b[38;5;28;01mas\u001b[39;00m _is_numpy_dev  \u001b[38;5;66;03m# pyright: ignore # noqa:F401\u001b[39;00m\n\u001b[32m     24\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m     25\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m hashtable \u001b[38;5;28;01mas\u001b[39;00m _hashtable, lib \u001b[38;5;28;01mas\u001b[39;00m _lib, tslib \u001b[38;5;28;01mas\u001b[39;00m _tslib\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/compat/__init__.py:18\u001b[39m\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TYPE_CHECKING\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_typing\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m F\n\u001b[32m---> \u001b[39m\u001b[32m18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcompat\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     19\u001b[39m     is_numpy_dev,\n\u001b[32m     20\u001b[39m     np_version_under1p21,\n\u001b[32m     21\u001b[39m )\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcompat\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyarrow\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     23\u001b[39m     pa_version_under1p01,\n\u001b[32m     24\u001b[39m     pa_version_under2p0,\n\u001b[32m   (...)\u001b[39m\u001b[32m     31\u001b[39m     pa_version_under9p0,\n\u001b[32m     32\u001b[39m )\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m TYPE_CHECKING:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/compat/numpy/__init__.py:4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[33;03m\"\"\" support numpy compatibility across versions \"\"\"\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mversion\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Version\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# numpy versioning\u001b[39;00m\n\u001b[32m      7\u001b[39m _np_version = np.__version__\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/util/__init__.py:2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# pyright: reportUnusedImport = false\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_decorators\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (  \u001b[38;5;66;03m# noqa:F401\u001b[39;00m\n\u001b[32m      3\u001b[39m     Appender,\n\u001b[32m      4\u001b[39m     Substitution,\n\u001b[32m      5\u001b[39m     cache_readonly,\n\u001b[32m      6\u001b[39m )\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mhashing\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (  \u001b[38;5;66;03m# noqa:F401\u001b[39;00m\n\u001b[32m      9\u001b[39m     hash_array,\n\u001b[32m     10\u001b[39m     hash_pandas_object,\n\u001b[32m     11\u001b[39m )\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__getattr__\u001b[39m(name):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/util/_decorators.py:14\u001b[39m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m      7\u001b[39m     Any,\n\u001b[32m      8\u001b[39m     Callable,\n\u001b[32m      9\u001b[39m     Mapping,\n\u001b[32m     10\u001b[39m     cast,\n\u001b[32m     11\u001b[39m )\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwarnings\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mproperties\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m cache_readonly\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_typing\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     16\u001b[39m     F,\n\u001b[32m     17\u001b[39m     T,\n\u001b[32m     18\u001b[39m )\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_exceptions\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m find_stack_level\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/_libs/__init__.py:13\u001b[39m\n\u001b[32m      1\u001b[39m __all__ = [\n\u001b[32m      2\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mNaT\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m      3\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mNaTType\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m      9\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mInterval\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m     10\u001b[39m ]\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01minterval\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m <PERSON>val\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_libs\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtslibs\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[32m     15\u001b[39m     NaT,\n\u001b[32m     16\u001b[39m     NaTType,\n\u001b[32m   (...)\u001b[39m\u001b[32m     21\u001b[39m     iNaT,\n\u001b[32m     22\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/smas/lib/python3.11/site-packages/pandas/_libs/interval.pyx:1\u001b[39m, in \u001b[36minit pandas._libs.interval\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mValueError\u001b[39m: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject"]}], "source": ["from __future__ import annotations\n", "\n", "import os\n", "import glob\n", "import dataclasses\n", "import math\n", "import random\n", "from pathlib import Path\n", "from typing import Iterator, <PERSON>ple, Dict\n", "\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import IterableDataset, DataLoader\n", "from tqdm import tqdm\n", "\n", "# Waymax / Waymo‑Open‑Dataset imports (jax backend)\n", "import jax.numpy as jnp  # type: ignore\n", "from waymax import config as wconfig  # type: ignore\n", "from waymax import dataloader as wdl  # type: ignore\n", "from waymax.datatypes import simulator_state as sim_state  # type: ignore"]}], "metadata": {"kernelspec": {"display_name": "smas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}