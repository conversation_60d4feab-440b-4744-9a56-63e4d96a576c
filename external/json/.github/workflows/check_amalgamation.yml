name: "Check amalgamation"

on:
  pull_request:

permissions: read-all

jobs:
  save:
    runs-on: ubuntu-latest
    steps:
      - name: Save PR number
        run: |
          mkdir -p ./pr
          echo ${{ github.event.number }} > ./pr/number
          echo ${{ github.event.pull_request.user.login }} > ./pr/author
      - uses: actions/upload-artifact@v2
        with:
          name: pr
          path: pr/

  check:
    runs-on: ubuntu-latest
    env:
      MAIN_DIR: ${{ github.workspace }}/main
      INCLUDE_DIR: ${{ github.workspace }}/main/single_include/nlohmann
      TOOL_DIR: ${{ github.workspace }}/tools/tools/amalgamate
      ASTYLE_FLAGS: >
        --style=allman --indent=spaces=4 --indent-modifiers --indent-switches --indent-preproc-block
        --indent-preproc-define --indent-col1-comments --pad-oper --pad-header --align-pointer=type
        --align-reference=type --add-brackets --convert-tabs --close-templates --lineend=linux --preserve-date
        --formatted

    steps:
      - name: Checkout pull request
        uses: actions/checkout@v3
        with:
          path: main
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Checkout tools
        uses: actions/checkout@v3
        with:
          path: tools
          ref: develop

      - name: Install astyle
        run: |
          sudo apt-get update
          sudo apt-get install astyle

      - name: Check amalgamation
        run: |
          cd $MAIN_DIR

          rm -fr $INCLUDE_DIR/json.hpp~ $INCLUDE_DIR/json_fwd.hpp~
          cp $INCLUDE_DIR/json.hpp $INCLUDE_DIR/json.hpp~
          cp $INCLUDE_DIR/json_fwd.hpp $INCLUDE_DIR/json_fwd.hpp~

          python3 $TOOL_DIR/amalgamate.py -c $TOOL_DIR/config_json.json -s .
          python3 $TOOL_DIR/amalgamate.py -c $TOOL_DIR/config_json_fwd.json -s .
          echo "Format (1)"
          astyle $ASTYLE_FLAGS --suffix=none --quiet $INCLUDE_DIR/json.hpp $INCLUDE_DIR/json_fwd.hpp

          diff $INCLUDE_DIR/json.hpp~ $INCLUDE_DIR/json.hpp
          diff $INCLUDE_DIR/json_fwd.hpp~ $INCLUDE_DIR/json_fwd.hpp

          astyle $ASTYLE_FLAGS $(find docs/examples include tests -type f \( -name '*.hpp' -o -name '*.cpp' -o -name '*.cu' \) -not -path 'tests/thirdparty/*' -not -path 'tests/abi/include/nlohmann/*' | sort)
          echo Check
          find $MAIN_DIR -name '*.orig' -exec false {} \+
