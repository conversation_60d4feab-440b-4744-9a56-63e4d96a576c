save_results_path: examples/experimental/visualizations
train_path: "data/processed/training"

cpt_path: examples/experimental/models
cpt_name: model_PPO__C__R_10000__01_28_20_57_35_873_011426

num_worlds: 1
dataset_size: 3

device: cpu
deterministic: false
render_sim_state: true

# Environment settings
max_controlled_agents: 64 
ego_state: true
road_map_obs: true
partner_obs: true
norm_obs: true
remove_non_vehicles: true 
lidar_obs: false
reward_type: "weighted_combination"
collision_weight: -0.75
off_road_weight: -0.75
goal_achieved_weight: 1.0
dynamics_model: "classic"
collision_behavior: "ignore" 
dist_to_goal_threshold: 2.0
polyline_reduction_threshold: 0.1 
sampling_seed: 42 
obs_radius: 50.0 

# Number of discretizations in the action space
action_space_steer_disc: 41 # 11 in smaller act space
action_space_accel_disc: 17 # 7 in smaller act space